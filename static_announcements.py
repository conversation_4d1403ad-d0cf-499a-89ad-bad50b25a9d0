"""
静态公告配置文件
避免数据库查询，提供快速响应的公告数据
"""

# 静态公告数据
STATIC_ANNOUNCEMENTS = [
    {
        'id': 1,
        'title': '🎉 欢迎使用风车AUG云激活系统',
        'content': '''
            <div style="padding: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px; margin-bottom: 15px;">
                <h3 style="margin: 0 0 10px 0; color: #fff;">🎉 欢迎使用风车AUG云激活系统</h3>
                <p style="margin: 0; font-size: 14px; opacity: 0.9;">本系统提供自动化的AUG激活服务，请按照使用说明正确操作。</p>
            </div>
        ''',
        'type': 'welcome',
        'is_active': True,
        'sort_order': 1,
        'created_at': '2025-07-07 15:30:00',
        'updated_at': '2025-07-07 15:30:00'
    },
    {
        'id': 2,
        'title': '📋 使用说明',
        'content': '''
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">
                <h4 style="color: #28a745; margin: 0 0 10px 0;">📋 使用说明</h4>
                <ol style="margin: 0; padding-left: 20px;">
                    <li>请确保邮箱格式正确</li>
                    <li>每个邮箱只能提交一次</li>
                    <li>处理时间约1-3分钟</li>
                    <li>请耐心等待处理完成</li>
                    <li>激活成功后请到邮箱查收结果</li>
                </ol>
            </div>
        ''',
        'type': 'instruction',
        'is_active': True,
        'sort_order': 2,
        'created_at': '2025-07-07 15:30:00',
        'updated_at': '2025-07-07 15:30:00'
    },
    {
        'id': 3,
        'title': '⚠️ 注意事项',
        'content': '''
            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
                <h4 style="color: #856404; margin: 0 0 10px 0;">⚠️ 注意事项</h4>
                <ul style="margin: 0; padding-left: 20px; color: #856404;">
                    <li>请勿重复提交相同邮箱</li>
                    <li>系统会自动检测并拒绝重复请求</li>
                    <li>如需帮助请联系管理员</li>
                    <li>处理过程中请勿关闭页面</li>
                    <li>建议使用Chrome或Edge浏览器</li>
                </ul>
            </div>
        ''',
        'type': 'warning',
        'is_active': True,
        'sort_order': 3,
        'created_at': '2025-07-07 15:30:00',
        'updated_at': '2025-07-07 15:30:00'
    },
    {
        'id': 4,
        'title': '🚀 系统优化通知',
        'content': '''
            <div style="background: #d1ecf1; padding: 15px; border-radius: 8px; border-left: 4px solid #17a2b8;">
                <h4 style="color: #0c5460; margin: 0 0 10px 0;">🚀 系统优化通知</h4>
                <p style="margin: 0; color: #0c5460;">
                    ✅ 系统已升级连接池技术，响应速度大幅提升<br>
                    ✅ 采用静态公告机制，确保快速加载<br>
                    ✅ 优化了Cloudflare Tunnel配置<br>
                    ✅ 如遇问题请刷新页面重试
                </p>
            </div>
        ''',
        'type': 'info',
        'is_active': True,
        'sort_order': 4,
        'created_at': '2025-07-07 15:30:00',
        'updated_at': '2025-07-07 15:30:00'
    },
    {
        'id': 5,
        'title': '📞 联系支持',
        'content': '''
            <div style="background: #f8d7da; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545;">
                <h4 style="color: #721c24; margin: 0 0 10px 0;">📞 需要帮助？</h4>
                <p style="margin: 0; color: #721c24;">
                    如果您在使用过程中遇到任何问题，请联系技术支持：<br>
                    • 系统会自动处理大部分问题<br>
                    • 如果任务长时间未完成，请检查邮箱格式<br>
                    • 重复提交会被自动拒绝<br>
                    • 系统维护时间：每日凌晨2:00-3:00
                </p>
            </div>
        ''',
        'type': 'support',
        'is_active': True,
        'sort_order': 5,
        'created_at': '2025-07-07 15:30:00',
        'updated_at': '2025-07-07 15:30:00'
    }
]

# 紧急公告（可以临时添加）
EMERGENCY_ANNOUNCEMENTS = [
    # 示例：系统维护公告
    # {
    #     'id': 999,
    #     'title': '🔧 系统维护通知',
    #     'content': '''
    #         <div style="background: #f8d7da; padding: 15px; border-radius: 8px; border-left: 4px solid #dc3545;">
    #             <h4 style="color: #721c24; margin: 0 0 10px 0;">🔧 系统维护通知</h4>
    #             <p style="margin: 0; color: #721c24;">
    #                 系统将于今晚22:00-23:00进行维护升级，期间可能无法提交新任务。
    #                 维护完成后系统性能将进一步提升。感谢您的理解！
    #             </p>
    #         </div>
    #     ''',
    #     'type': 'emergency',
    #     'is_active': True,
    #     'sort_order': 0,
    #     'created_at': '2025-07-07 20:00:00',
    #     'updated_at': '2025-07-07 20:00:00'
    # }
]


def get_all_announcements():
    """获取所有有效的公告"""
    all_announcements = []
    
    # 添加紧急公告（优先显示）
    for announcement in EMERGENCY_ANNOUNCEMENTS:
        if announcement.get('is_active', True):
            all_announcements.append(announcement)
    
    # 添加常规公告
    for announcement in STATIC_ANNOUNCEMENTS:
        if announcement.get('is_active', True):
            all_announcements.append(announcement)
    
    # 按sort_order排序
    all_announcements.sort(key=lambda x: x.get('sort_order', 999))
    
    return all_announcements


def get_announcements_by_type(announcement_type):
    """根据类型获取公告"""
    all_announcements = get_all_announcements()
    return [a for a in all_announcements if a.get('type') == announcement_type]


def add_emergency_announcement(title, content, announcement_type='emergency'):
    """添加紧急公告"""
    emergency_id = max([a['id'] for a in EMERGENCY_ANNOUNCEMENTS], default=900) + 1
    
    emergency_announcement = {
        'id': emergency_id,
        'title': title,
        'content': content,
        'type': announcement_type,
        'is_active': True,
        'sort_order': 0,
        'created_at': '2025-07-07 15:30:00',
        'updated_at': '2025-07-07 15:30:00'
    }
    
    EMERGENCY_ANNOUNCEMENTS.append(emergency_announcement)
    return emergency_announcement


def remove_emergency_announcement(announcement_id):
    """移除紧急公告"""
    global EMERGENCY_ANNOUNCEMENTS
    EMERGENCY_ANNOUNCEMENTS = [a for a in EMERGENCY_ANNOUNCEMENTS if a['id'] != announcement_id]


def get_announcement_stats():
    """获取公告统计信息"""
    all_announcements = get_all_announcements()
    
    stats = {
        'total': len(all_announcements),
        'by_type': {},
        'active': len([a for a in all_announcements if a.get('is_active', True)]),
        'emergency': len(EMERGENCY_ANNOUNCEMENTS)
    }
    
    for announcement in all_announcements:
        announcement_type = announcement.get('type', 'unknown')
        stats['by_type'][announcement_type] = stats['by_type'].get(announcement_type, 0) + 1
    
    return stats


if __name__ == "__main__":
    # 测试静态公告
    print("🧪 测试静态公告配置...")
    
    announcements = get_all_announcements()
    print(f"📊 总公告数: {len(announcements)}")
    
    for announcement in announcements:
        print(f"  {announcement['id']}: {announcement['title']} ({announcement['type']})")
    
    stats = get_announcement_stats()
    print(f"\n📈 统计信息: {stats}")
    
    print("\n✅ 静态公告配置测试完成")

/**
 * 连接状态组件 - 小图标版本
 */
import React, { useState, useEffect, useCallback } from 'react';
import { Tooltip, Button } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import { emailAPI } from '../services/api';

interface ConnectionStatusProps {
  onConnectionChange?: (connected: boolean) => void;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ onConnectionChange }) => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  // 检查连接状态 - 使用useCallback避免无限循环
  const checkConnection = useCallback(async () => {
    setIsChecking(true);

    try {
      const connected = await emailAPI.testConnection();
      setIsConnected(connected);
      onConnectionChange?.(connected);
    } catch (error) {
      setIsConnected(false);
      onConnectionChange?.(false);
    } finally {
      setIsChecking(false);
    }
  }, [onConnectionChange]);

  // 组件挂载时检查连接
  useEffect(() => {
    checkConnection();

    // 每30秒检查一次连接状态
    const interval = setInterval(checkConnection, 30000);

    return () => clearInterval(interval);
  }, [checkConnection]);

  // 根据连接状态返回不同的小图标
  const getStatusIcon = () => {
    if (isConnected === null) {
      return <LoadingOutlined style={{ color: '#1890ff', fontSize: '16px' }} />;
    }

    if (isConnected) {
      return <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '16px' }} />;
    }

    return <CloseCircleOutlined style={{ color: '#ff4d4f', fontSize: '16px' }} />;
  };

  const getTooltipTitle = () => {
    if (isConnected === null) {
      return '正在检查连接状态...';
    }

    if (isConnected) {
      return (
        <div>
          <div>✅ 服务器连接正常</div>
          <div style={{ marginTop: 4, fontSize: '12px', opacity: 0.8 }}>
            点击重新检查连接
          </div>
        </div>
      );
    }

    return (
      <div>
        <div>❌ 服务器连接失败</div>
        <div style={{ marginTop: 4, fontSize: '12px', opacity: 0.8 }}>
          请检查autoback.py是否运行
        </div>
        <div style={{ marginTop: 4, fontSize: '12px', opacity: 0.8 }}>
          点击重新连接
        </div>
      </div>
    );
  };

  return (
    <Tooltip title={getTooltipTitle()} placement="bottomRight">
      <Button
        type="text"
        icon={getStatusIcon()}
        loading={isChecking}
        onClick={checkConnection}
        style={{
          border: 'none',
          boxShadow: 'none',
          padding: '4px 8px',
          height: 'auto'
        }}
      />
    </Tooltip>
  );
};

export default ConnectionStatus;

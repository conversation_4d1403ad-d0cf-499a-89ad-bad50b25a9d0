[{"description": "treehash per file", "signed_content": {"payload": "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", "signatures": [{"header": {"kid": "publisher"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "N205S7_1HsOa1Lg65t0ihKLWOeZl-P0l3XEXDO6HfBwzjezN3yhHRahAoW2y4J5wJN5Qt1mZnfnq15LRQmRWObDDwRdCdoWawWv15UKmTWMxuJLZyAn2QmO-Il-KMPLY-6RBbGV7mRuEPAN5rxnOf4D2xBUDPKDu4BLLqXMdlrKAkAWnmU598ycvQyK5LlOxAX8KC6CYq94KMO4bR03sph-vp7xgqSYSGRo7TniPqMKSb40Qn42R2TZd8i9R7e_UCm0e20CYjQjR_EP3Ddim_qrKacdZVsHZwDH-jQJt704ybyRyNdQjclXjIKifcnoDAUr3yp92H73Ax7ZvXDz6NA"}, {"header": {"kid": "webstore"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "HboEBAbvTTtDFPz28JCLLprNn2WTkqMR3y_UWOXeKumEm8gt-NsJ2ACbgwiaYkfDSJfY2gfHDBtECk0uvI2HHgyOU-Qwx7RGRzOb1Puzazvqi_AkBedu5eb6bM632dQj41uwIfDn0wlF4FqE18le6x8KwN-zP_Zq493ah8H-9UMRa0jDEfF96OnA9rjAX7fX2m9RRqdNd8LqAqh59-B-rSa-mRWPZ3X5vYdE8XELxslgl7bhXn4Wad_Q1PaZIBa0HdXaqMCkypGEnTU4uwNy6yzus1UncLvdekvw0DD4AVUbh3OJ_ZsN5mm4EWAlqGfAjc34Oinjqk1AFm4X87dNog"}]}}]
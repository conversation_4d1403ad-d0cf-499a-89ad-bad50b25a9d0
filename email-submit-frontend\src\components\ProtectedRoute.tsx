/**
 * 路由守卫组件
 * 保护需要登录才能访问的页面
 */
import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { useUserState, useUserActions } from '../stores/userStore';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isLoggedIn, userInfo, isLoading } = useUserState();
  const { refreshUserInfo } = useUserActions();
  const location = useLocation();

  // 组件挂载时刷新用户信息
  useEffect(() => {
    if (isLoggedIn && userInfo) {
      // 添加错误处理，避免因网络问题导致自动登出
      refreshUserInfo().catch(error => {
        console.warn('路由守卫刷新用户信息失败，但不影响登录状态:', error);
        // 不做任何处理，保持用户登录状态
      });
    }
  }, [isLoggedIn, userInfo, refreshUserInfo]);

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh'
      }}>
        <Spin size="large" />
      </div>
    );
  }

  // 如果未登录，重定向到登录页面
  if (!isLoggedIn || !userInfo) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 如果已登录，渲染子组件
  return <>{children}</>;
};

export default ProtectedRoute;

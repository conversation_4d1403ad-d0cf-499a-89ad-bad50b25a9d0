"""
图形用户界面
使用Tkinter创建多账号浏览器管理界面
"""
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading
import os
from typing import Dict, Optional
from config_manager import ConfigManager
from browser_manager import BrowserManager
from automation_engine import AutomationEngine
from selenium.webdriver.common.by import By

# 尝试导入原生环境模拟器
try:
    from native_browser_analyzer import NativeBrowserAnalyzer
    from native_environment_simulator import NativeEnvironmentSimulator
    NATIVE_SIMULATION_AVAILABLE = True
    print("✅ 原生环境模拟器已加载到GUI")
except ImportError as e:
    NATIVE_SIMULATION_AVAILABLE = False
    print(f"⚠️ 原生环境模拟器不可用: {e}")
    print("💡 将使用标准配置模式")


class BrowserManagerGUI:
    """浏览器管理器图形界面"""

    def __init__(self, root: tk.Tk):
        """初始化GUI"""
        self.root = root
        self.root.title("多账号指纹浏览器管理器")
        self.root.geometry("900x600")

        # 初始化核心组件
        self.config_manager = ConfigManager()
        self.browser_manager = BrowserManager(self.config_manager)
        self.automation_engine = AutomationEngine(self.browser_manager)

        # 初始化原生环境模拟器（不自动分析）
        if NATIVE_SIMULATION_AVAILABLE:
            try:
                # 只有在分析文件存在时才初始化模拟器
                if os.path.exists('native_browser_analysis.json'):
                    self.native_analyzer = NativeBrowserAnalyzer()
                    self.native_simulator = NativeEnvironmentSimulator()
                    print("🎭 GUI原生环境模拟器初始化成功")
                else:
                    print("💡 原生环境分析文件不存在，跳过模拟器初始化")
                    self.native_analyzer = None
                    self.native_simulator = None

            except Exception as e:
                print(f"⚠️ GUI原生环境模拟器初始化失败: {e}")
                self.native_analyzer = None
                self.native_simulator = None
        else:
            self.native_analyzer = None
            self.native_simulator = None

        # 创建界面
        self._create_ui()
        self._load_profiles()

        # 定期更新运行状态
        self._update_status()

    def _analyze_native_environment_background(self):
        """在后台分析原生环境"""
        def analyze():
            try:
                print("🔍 后台分析原生浏览器环境...")
                report = self.native_analyzer.generate_analysis_report()

                # 保存分析报告
                import json
                with open('native_browser_analysis.json', 'w', encoding='utf-8') as f:
                    json.dump(report, f, indent=2, ensure_ascii=False)

                print(f"✅ 原生环境分析完成")
                print(f"📊 发现Chrome安装: {len(report['chrome_installations'])}")
                print(f"📊 发现Edge安装: {len(report['edge_installations'])}")

                # 重新初始化模拟器以使用新的分析结果
                if NATIVE_SIMULATION_AVAILABLE:
                    self.native_simulator = NativeEnvironmentSimulator()

            except Exception as e:
                print(f"⚠️ 后台分析失败: {e}")

        # 在后台线程中运行分析
        threading.Thread(target=analyze, daemon=True).start()

    def _create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建顶部工具栏
        self._create_toolbar(main_frame)

        # 创建主要内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # 创建配置文件列表
        self._create_profile_list(content_frame)

        # 创建操作面板
        self._create_operation_panel(content_frame)

        # 创建状态栏
        self._create_status_bar(main_frame)

    def _create_toolbar(self, parent):
        """创建工具栏"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(toolbar, text="新建配置", command=self._create_profile).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="删除配置", command=self._delete_profile).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="删除已使用", command=self._delete_used_profiles).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="刷新列表", command=self._load_profiles).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="检查配置", command=self._check_profiles).pack(side=tk.LEFT, padx=(0, 5))

        # 分隔符
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)

        ttk.Button(toolbar, text="关闭所有浏览器", command=self._close_all_browsers).pack(side=tk.LEFT, padx=(0, 5))

    def _create_profile_list(self, parent):
        """创建配置文件列表"""
        list_frame = ttk.LabelFrame(parent, text="账号配置文件")
        list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # 创建Treeview
        columns = ("名称", "创建时间", "最后使用", "状态", "模拟模式", "调试端口")
        self.profile_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)

        # 设置列标题和宽度
        column_widths = {"名称": 120, "创建时间": 140, "最后使用": 140, "状态": 80, "模拟模式": 100, "调试端口": 80}
        for col in columns:
            self.profile_tree.heading(col, text=col)
            self.profile_tree.column(col, width=column_widths.get(col, 100))

        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.profile_tree.yview)
        self.profile_tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        self.profile_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定右键菜单和双击事件
        self.profile_tree.bind("<Button-3>", self.show_profile_context_menu)  # 右键
        self.profile_tree.bind("<Double-1>", self.on_profile_double_click)  # 双击

        # 绑定双击事件
        self.profile_tree.bind("<Double-1>", self._on_profile_double_click)

    def _create_operation_panel(self, parent):
        """创建操作面板"""
        panel_frame = ttk.LabelFrame(parent, text="操作面板")
        panel_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))

        # 浏览器操作
        browser_frame = ttk.LabelFrame(panel_frame, text="浏览器操作")
        browser_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(browser_frame, text="🌐 打开配置", command=self._launch_browser, width=15).pack(pady=2)
        ttk.Button(browser_frame, text="关闭浏览器", command=self._close_browser, width=15).pack(pady=2)
        ttk.Button(browser_frame, text="打开网页", command=self._open_url, width=15).pack(pady=2)
        ttk.Button(browser_frame, text="自定义网页", command=self._open_custom_url, width=15).pack(pady=2)

        # 配置操作
        config_frame = ttk.LabelFrame(panel_frame, text="配置操作")
        config_frame.pack(fill=tk.X)

        ttk.Button(config_frame, text="更新profiles", command=self._sync_profiles, width=15).pack(pady=2)

    def _create_status_bar(self, parent):
        """创建状态栏"""
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")

        status_bar = ttk.Label(parent, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))

    def _load_profiles(self):
        """加载配置文件列表 - 快速版本"""
        try:
            # 清空现有项目
            for item in self.profile_tree.get_children():
                self.profile_tree.delete(item)

            # 获取所有配置文件
            profiles = self.config_manager.get_all_profiles()

            # 快速检查运行状态，不做耗时操作
            running_browsers = {}
            try:
                # 只检查内存中的记录，不做实际进程检查
                for name, browser_info in self.browser_manager.running_browsers.items():
                    # 简单检查process是否存在
                    process = browser_info.get("process")
                    if process and hasattr(process, 'poll'):
                        try:
                            if process.poll() is None:
                                running_browsers[name] = browser_info
                        except:
                            pass
            except Exception as e:
                print(f"⚠️ 快速状态检查失败: {e}")

            # 添加到列表
            for name, profile in profiles.items():
                status = "运行中" if name in running_browsers else "未运行"
                debug_port = profile.get("debug_port", "N/A")

                # 确定模拟模式
                stealth_enabled = profile.get("stealth_enabled", False)
                native_simulation = profile.get("native_simulation", False)

                if native_simulation:
                    simulation_mode = "🎭 原生模拟"
                elif stealth_enabled:
                    simulation_mode = "🛡️ Stealth"
                else:
                    simulation_mode = "标准"

                self.profile_tree.insert("", tk.END, values=(
                    name,
                    profile.get("created_at", "N/A"),
                    profile.get("last_used", "从未使用"),
                    status,
                    simulation_mode,
                    debug_port
                ))

        except Exception as e:
            print(f"⚠️ 加载配置列表时出错: {e}")
            # 即使出错也要确保界面可用
            pass

    def _get_selected_profile(self) -> Optional[str]:
        """获取选中的配置文件名称"""
        selection = self.profile_tree.selection()
        if not selection:
            return None

        return self.profile_tree.item(selection[0], "values")[0]

    def show_profile_context_menu(self, event):
        """显示配置文件右键菜单"""
        try:
            # 获取点击的项目
            item = self.profile_tree.identify_row(event.y)
            if not item:
                return

            # 选中该项目
            self.profile_tree.selection_set(item)

            # 获取配置信息
            profile_values = self.profile_tree.item(item, 'values')
            if not profile_values:
                return

            profile_name = profile_values[0]
            profile_status = profile_values[3]  # 状态列

            # 创建右键菜单
            context_menu = tk.Menu(self.root, tearoff=0)

            # 查看详情
            context_menu.add_command(
                label=f"📋 查看详情",
                command=lambda: self.show_profile_details(profile_name)
            )

            context_menu.add_separator()

            # 打开配置
            if "运行中" not in profile_status:
                context_menu.add_command(
                    label="🌐 打开配置",
                    command=lambda: self.start_browser_for_profile(profile_name)
                )
            else:
                context_menu.add_command(
                    label="🛑 停止浏览器",
                    command=lambda: self.stop_browser_for_profile(profile_name)
                )

            # 打开配置文件夹
            context_menu.add_command(
                label="📁 打开文件夹",
                command=lambda: self.open_profile_folder(profile_name)
            )

            context_menu.add_separator()

            # 删除配置
            context_menu.add_command(
                label="🗑️ 删除配置",
                command=lambda: self.delete_profile_with_confirm(profile_name)
            )

            # 显示菜单
            context_menu.post(event.x_root, event.y_root)

        except Exception as e:
            messagebox.showerror("错误", f"显示右键菜单失败: {str(e)}")

    def on_profile_double_click(self, event):
        """双击配置文件"""
        try:
            profile_name = self._get_selected_profile()
            if profile_name:
                self.show_profile_details(profile_name)
        except Exception as e:
            messagebox.showerror("错误", f"双击处理失败: {str(e)}")

    def show_profile_details(self, profile_name: str):
        """显示配置详情"""
        try:
            profile = self.config_manager.get_profile(profile_name)
            if not profile:
                messagebox.showerror("错误", f"配置 {profile_name} 不存在")
                return

            # 创建详情对话框
            dialog = tk.Toplevel(self.root)
            dialog.title(f"配置详情 - {profile_name}")
            dialog.geometry("500x400")
            dialog.transient(self.root)
            dialog.grab_set()

            # 居中显示
            dialog.geometry("+%d+%d" % (
                self.root.winfo_rootx() + 100,
                self.root.winfo_rooty() + 100
            ))

            # 创建详情内容
            main_frame = ttk.Frame(dialog, padding=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 基本信息
            info_frame = ttk.LabelFrame(main_frame, text="基本信息", padding=10)
            info_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(info_frame, text=f"配置名称: {profile.get('name', 'N/A')}", font=("Arial", 10, "bold")).pack(anchor=tk.W)
            ttk.Label(info_frame, text=f"创建时间: {profile.get('created_at', 'N/A')}").pack(anchor=tk.W, pady=2)
            ttk.Label(info_frame, text=f"最后使用: {profile.get('last_used', '从未使用')}").pack(anchor=tk.W, pady=2)
            ttk.Label(info_frame, text=f"调试端口: {profile.get('debug_port', 'N/A')}").pack(anchor=tk.W, pady=2)

            # 路径信息
            path_frame = ttk.LabelFrame(main_frame, text="路径信息", padding=10)
            path_frame.pack(fill=tk.X, pady=(0, 10))

            path_text = tk.Text(path_frame, height=3, width=50)
            path_text.pack(fill=tk.X)
            path_text.insert(tk.END, profile.get('path', 'N/A'))
            path_text.config(state=tk.DISABLED)

            # 技术信息
            tech_frame = ttk.LabelFrame(main_frame, text="技术信息", padding=10)
            tech_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            tech_text = tk.Text(tech_frame, height=8, width=50)
            tech_text.pack(fill=tk.BOTH, expand=True)

            tech_info = f"User Agent: {profile.get('user_agent', 'N/A')}\n\n"
            tech_info += f"代理设置: {profile.get('proxy', '无')}\n\n"

            fingerprint = profile.get('fingerprint', {})
            if fingerprint:
                tech_info += "指纹信息:\n"
                for key, value in fingerprint.items():
                    tech_info += f"  {key}: {value}\n"

            tech_text.insert(tk.END, tech_info)
            tech_text.config(state=tk.DISABLED)

            # 按钮
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            ttk.Button(button_frame, text="关闭", command=dialog.destroy).pack(side=tk.RIGHT)
            ttk.Button(button_frame, text="📁 打开文件夹",
                      command=lambda: self.open_profile_folder(profile_name)).pack(side=tk.RIGHT, padx=(0, 10))

        except Exception as e:
            messagebox.showerror("错误", f"显示配置详情失败: {str(e)}")

    def start_browser_for_profile(self, profile_name: str):
        """为指定配置打开浏览器（与autoback.py保持一致）"""
        try:
            def start_and_open():
                try:
                    # 启动浏览器
                    browser_info = self.browser_manager.launch_browser(profile_name)
                    if browser_info:
                        self.root.after(0, self._load_profiles)  # 刷新列表

                        # 等待浏览器启动完成后打开目标网站
                        self.root.after(3000, lambda: self._open_target_website(profile_name))

                        # 显示成功消息
                        self.root.after(0, lambda: self.status_var.set(f"配置已打开: {profile_name} (PID: {browser_info.get('pid', 'N/A')})"))
                    else:
                        self.root.after(0, lambda: messagebox.showerror("错误", "浏览器启动失败"))
                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("错误", f"打开配置失败: {str(e)}"))

            import threading
            threading.Thread(target=start_and_open, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"打开配置失败: {str(e)}")

    def stop_browser_for_profile(self, profile_name: str):
        """停止指定配置的浏览器"""
        try:
            success = self.browser_manager.close_browser(profile_name)
            if success:
                messagebox.showinfo("成功", f"浏览器 {profile_name} 已停止")
                self._load_profiles()  # 刷新列表
            else:
                messagebox.showwarning("警告", f"停止浏览器 {profile_name} 失败")
        except Exception as e:
            messagebox.showerror("错误", f"停止浏览器失败: {str(e)}")

    def open_profile_folder(self, profile_name: str):
        """打开配置文件夹"""
        try:
            profile = self.config_manager.get_profile(profile_name)
            if not profile:
                messagebox.showerror("错误", f"配置 {profile_name} 不存在")
                return

            profile_path = profile.get('path')
            if not profile_path or not os.path.exists(profile_path):
                messagebox.showerror("错误", f"配置文件夹不存在: {profile_path}")
                return

            import subprocess
            import sys

            if sys.platform.startswith('win'):
                os.startfile(profile_path)
            elif sys.platform.startswith('darwin'):
                subprocess.run(['open', profile_path])
            else:
                subprocess.run(['xdg-open', profile_path])

        except Exception as e:
            messagebox.showerror("错误", f"打开文件夹失败: {str(e)}")

    def delete_profile_with_confirm(self, profile_name: str):
        """确认删除配置"""
        try:
            result = messagebox.askyesno(
                "确认删除",
                f"确定要删除配置 '{profile_name}' 吗？\n\n"
                f"此操作将删除配置文件和相关数据，无法撤销！",
                icon='warning'
            )

            if result:
                # 先停止浏览器（如果正在运行）
                self.browser_manager.close_browser(profile_name)

                # 删除配置
                success = self.config_manager.delete_profile(profile_name)
                if success:
                    messagebox.showinfo("成功", f"配置 '{profile_name}' 已删除")
                    self._load_profiles()  # 刷新列表
                else:
                    messagebox.showerror("错误", f"删除配置 '{profile_name}' 失败")

        except Exception as e:
            messagebox.showerror("错误", f"删除配置失败: {str(e)}")

    def _create_profile(self):
        """创建新配置文件"""
        # 创建配置对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("新建配置")
        dialog.geometry("450x400")  # 增加宽度和高度
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        # 创建主框架
        main_frame = ttk.Frame(dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 配置名称
        ttk.Label(main_frame, text="配置名称:").pack(anchor=tk.W, pady=(0, 5))
        name_var = tk.StringVar()

        # 尝试从剪切板获取邮箱格式的内容
        default_name = self._get_email_from_clipboard()
        if default_name:
            name_var.set(default_name)

        name_entry = ttk.Entry(main_frame, textvariable=name_var, width=30)
        name_entry.pack(fill=tk.X, pady=(0, 15))
        name_entry.focus()
        name_entry.select_range(0, tk.END)  # 选中所有文本，方便用户修改

        # 配置说明
        info_frame = ttk.LabelFrame(main_frame, text="配置说明", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 15))

        # 说明文本
        info_text = tk.Text(info_frame, height=5, wrap=tk.WORD, bg=dialog.cget('bg'))
        info_text.pack(fill=tk.X, pady=(5, 0))

        # 检查finger插件是否存在
        finger_plugin_path = os.path.join(os.getcwd(), "finger")
        finger_plugin_exists = os.path.exists(finger_plugin_path) and os.path.exists(os.path.join(finger_plugin_path, "manifest.json"))

        finger_status = "✅ 已集成finger指纹防护插件" if finger_plugin_exists else "⚠️ finger插件未找到"

        info_text.insert(tk.END,
            "🔧 简化配置模式:\n"
            "• 独立用户数据存储（登录状态隔离）\n"
            "• 使用134文件夹Chrome\n"
            f"• {finger_status}\n"
            "• 无复杂指纹伪装（依赖finger插件）")
        info_text.config(state=tk.DISABLED)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        def create_config():
            name = name_var.get().strip()
            if not name:
                messagebox.showerror("错误", "请输入配置名称")
                return

            if name in self.config_manager.get_all_profiles():
                messagebox.showerror("错误", f"配置文件 '{name}' 已存在")
                return

            try:
                # 创建简化配置（只保存用户数据+finger插件）
                if hasattr(self.config_manager, 'create_profile_with_portable_base'):
                    profile = self.config_manager.create_profile_with_portable_base(name)
                    browser_type = "134文件夹Chrome"
                else:
                    profile = self.config_manager.create_profile(name)
                    browser_type = "标准Chrome"

                # 关闭对话框
                dialog.destroy()

                # 验证配置文件夹是否正确创建
                profile_folder = profile['path']
                if os.path.exists(profile_folder):
                    print(f"✅ 用户数据文件夹创建成功: {profile_folder}")
                else:
                    print(f"❌ 配置文件夹创建失败: {profile_folder}")

                # 实例存储验证
                print(f"✅ 实例存储目录创建成功: {profile_folder}")

                # 刷新界面
                self._load_profiles()
                self.status_var.set(f"已创建配置文件: {name}")

                # 显示简化的创建结果
                finger_plugin_exists = profile.get('finger_plugin_enabled', False)
                finger_status = "已启用" if finger_plugin_exists else "未启用"

                result_msg = f"配置 '{name}' 创建成功！\n\n"
                result_msg += f"浏览器: {browser_type}\n"
                result_msg += f"用户数据: {profile['path']}\n"
                result_msg += f"调试端口: {profile['debug_port']}\n"
                result_msg += f"Finger插件: {finger_status}\n"

                if finger_plugin_exists:
                    result_msg += f"\n🛡️ Canvas指纹防护已启用"
                else:
                    result_msg += f"\n⚠️ 无指纹防护（需要finger插件）"

                messagebox.showinfo("创建成功", result_msg)

            except Exception as e:
                messagebox.showerror("错误", f"创建配置文件失败: {e}")
                import traceback
                print(f"创建配置异常: {traceback.format_exc()}")

        def cancel():
            dialog.destroy()

        # 按钮
        ttk.Button(button_frame, text="创建", command=create_config).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=cancel).pack(side=tk.RIGHT)

        # 绑定回车键
        dialog.bind('<Return>', lambda e: create_config())

    def _get_email_from_clipboard(self) -> str:
        """从剪切板获取邮箱格式的内容"""
        try:
            import re
            # 获取剪切板内容
            clipboard_content = self.root.clipboard_get()

            if not clipboard_content:
                return ""

            # 清理内容（去除首尾空白）
            clipboard_content = clipboard_content.strip()

            # 邮箱格式正则表达式
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

            # 检查是否为有效邮箱格式
            if re.match(email_pattern, clipboard_content):
                print(f"✅ 从剪切板获取到邮箱: {clipboard_content}")
                return clipboard_content
            else:
                print(f"⚠️ 剪切板内容不是有效邮箱格式: {clipboard_content[:50]}...")
                return ""

        except Exception as e:
            print(f"⚠️ 读取剪切板失败: {e}")
            return ""

    def _check_portable_chrome(self) -> bool:
        """检查是否有便携版Chrome"""
        try:
            import os
            portable_paths = [
                os.path.join(os.path.dirname(__file__), "browsers", "chrome_config.json"),
                os.path.join(os.getcwd(), "browsers", "chrome_config.json")
            ]

            for path in portable_paths:
                if os.path.exists(path):
                    return True
            return False
        except:
            return False

    def _check_profiles(self):
        """检查配置文件状态"""
        try:
            # 创建检查对话框
            dialog = tk.Toplevel(self.root)
            dialog.title("配置文件检查")
            dialog.geometry("600x400")
            dialog.transient(self.root)
            dialog.grab_set()

            # 居中显示
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
            y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
            dialog.geometry(f"+{x}+{y}")

            # 创建文本框显示检查结果
            text_frame = ttk.Frame(dialog)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 10))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 执行检查
            check_result = self._perform_profile_check()

            # 显示结果
            text_widget.insert(tk.END, check_result)
            text_widget.config(state=tk.DISABLED)

            # 关闭按钮
            ttk.Button(dialog, text="关闭", command=dialog.destroy).pack(pady=10)

        except Exception as e:
            messagebox.showerror("错误", f"检查配置失败: {e}")

    def _perform_profile_check(self) -> str:
        """执行配置文件检查"""
        result = []
        result.append("🔍 配置文件检查报告")
        result.append("=" * 50)

        try:
            # 1. 检查配置管理器状态
            result.append(f"\n📋 配置管理器状态:")
            result.append(f"   配置目录: {self.config_manager.config_dir}")
            result.append(f"   配置文件: {self.config_manager.profiles_file}")

            # 检查目录和文件是否存在
            import os
            if os.path.exists(self.config_manager.config_dir):
                result.append(f"   ✅ 配置目录存在")
            else:
                result.append(f"   ❌ 配置目录不存在")

            if os.path.exists(self.config_manager.profiles_file):
                result.append(f"   ✅ 配置文件存在")

                # 检查文件大小
                file_size = os.path.getsize(self.config_manager.profiles_file)
                result.append(f"   文件大小: {file_size} 字节")
            else:
                result.append(f"   ❌ 配置文件不存在")

            # 2. 检查内存中的配置
            memory_profiles = self.config_manager.get_all_profiles()
            result.append(f"\n📋 内存中的配置 ({len(memory_profiles)} 个):")
            for name, profile in memory_profiles.items():
                result.append(f"   - {name}")
                result.append(f"     路径: {profile.get('path', 'N/A')}")
                result.append(f"     端口: {profile.get('debug_port', 'N/A')}")
                result.append(f"     创建时间: {profile.get('created_at', 'N/A')}")
                if 'portable_base' in profile:
                    result.append(f"     便携版: {profile['portable_base']}")

            # 3. 检查文件中的配置
            if os.path.exists(self.config_manager.profiles_file):
                import json
                with open(self.config_manager.profiles_file, 'r', encoding='utf-8') as f:
                    file_profiles = json.load(f)

                result.append(f"\n📋 文件中的配置 ({len(file_profiles)} 个):")
                for name in file_profiles.keys():
                    result.append(f"   - {name}")

                # 4. 比较内存和文件中的配置
                result.append(f"\n🔄 同步状态检查:")
                memory_names = set(memory_profiles.keys())
                file_names = set(file_profiles.keys())

                if memory_names == file_names:
                    result.append(f"   ✅ 内存和文件配置完全同步")
                else:
                    result.append(f"   ⚠️ 内存和文件配置不同步")

                    only_in_memory = memory_names - file_names
                    if only_in_memory:
                        result.append(f"   仅在内存中: {list(only_in_memory)}")

                    only_in_file = file_names - memory_names
                    if only_in_file:
                        result.append(f"   仅在文件中: {list(only_in_file)}")

            # 5. 检查便携版集成状态
            result.append(f"\n🔧 便携版集成状态:")
            has_portable = self._check_portable_chrome()
            result.append(f"   便携版Chrome: {'✅ 可用' if has_portable else '❌ 不可用'}")

            portable_configs = [name for name, profile in memory_profiles.items()
                              if profile.get('portable_base', False)]
            result.append(f"   便携版配置数量: {len(portable_configs)}")
            for name in portable_configs:
                result.append(f"   - {name}")

            result.append(f"\n✅ 检查完成")

        except Exception as e:
            result.append(f"\n❌ 检查过程异常: {e}")
            import traceback
            result.append(f"详细错误:\n{traceback.format_exc()}")

        return "\n".join(result)

    def _delete_profile(self):
        """删除配置文件"""
        profile_name = self._get_selected_profile()
        if not profile_name:
            messagebox.showinfo("提示", "请先选择一个配置文件")
            return

        if messagebox.askyesno("确认删除", f"确定要删除配置文件 '{profile_name}' 吗？\n此操作不可撤销。"):
            try:
                # 先关闭浏览器
                self.browser_manager.close_browser(profile_name)

                # 删除配置文件
                self.config_manager.delete_profile(profile_name)
                self._load_profiles()
                self.status_var.set(f"已删除配置文件: {profile_name}")
                messagebox.showinfo("成功", f"配置文件 '{profile_name}' 删除成功")
            except Exception as e:
                messagebox.showerror("错误", f"删除配置文件失败: {e}")

    def _delete_used_profiles(self):
        """删除已使用的配置文件到回收站"""
        try:
            # 导入必要的模块
            import os
            from profile_usage_tracker import ProfileUsageTracker

            # 获取使用统计信息
            usage_tracker = ProfileUsageTracker()
            stats = usage_tracker.get_usage_stats()

            # 找出已使用的配置
            used_profiles = []
            for profile in stats['profiles']:
                if profile['status'] == '已使用':
                    used_profiles.append(profile['name'])

            if not used_profiles:
                messagebox.showinfo("提示", "没有找到已使用的配置文件")
                return

            # 确认删除
            profile_list = "\n".join([f"• {name}" for name in used_profiles])
            confirm_msg = f"找到 {len(used_profiles)} 个已使用的配置文件:\n\n{profile_list}\n\n确定要删除这些配置到回收站吗？"

            if not messagebox.askyesno("确认删除", confirm_msg):
                return

            # 执行删除操作
            deleted_count = 0
            failed_profiles = []

            for profile_name in used_profiles:
                try:
                    # 根据配置名称构建文件夹路径
                    profile_folder_path = os.path.join("browser_profiles", profile_name)

                    # 检查文件夹是否存在
                    if not os.path.exists(profile_folder_path):
                        print(f"⚠️ 配置文件夹不存在: {profile_folder_path}")
                        failed_profiles.append(f"{profile_name}: 文件夹不存在")
                        continue

                    # 删除到回收站
                    try:
                        import send2trash
                        send2trash.send2trash(profile_folder_path)
                        print(f"🗑️ 删除到回收站: {profile_folder_path}")
                        deleted_count += 1
                    except ImportError:
                        # 如果没有send2trash模块，使用系统命令
                        import subprocess
                        import sys
                        if sys.platform == "win32":
                            # Windows系统使用PowerShell删除到回收站
                            cmd = f'powershell.exe "Add-Type -AssemblyName Microsoft.VisualBasic; [Microsoft.VisualBasic.FileIO.FileSystem]::DeleteDirectory(\'{profile_folder_path}\', \'OnlyErrorDialogs\', \'SendToRecycleBin\')"'
                            subprocess.run(cmd, shell=True, check=True)
                            print(f"🗑️ 删除到回收站: {profile_folder_path}")
                            deleted_count += 1
                        else:
                            # 非Windows系统提示安装send2trash
                            failed_profiles.append(f"{profile_name}: 需要安装send2trash模块")
                            continue

                except Exception as e:
                    print(f"❌ 删除配置失败: {profile_name} - {e}")
                    failed_profiles.append(f"{profile_name}: {str(e)}")

            # 刷新配置列表
            self._load_profiles()

            # 显示结果
            if deleted_count > 0:
                success_msg = f"✅ 成功删除 {deleted_count} 个配置到回收站"
                if failed_profiles:
                    success_msg += f"\n\n⚠️ 失败的配置:\n" + "\n".join(failed_profiles)
                messagebox.showinfo("删除完成", success_msg)
                self.status_var.set(f"已删除 {deleted_count} 个已使用配置到回收站")
            else:
                messagebox.showwarning("删除失败", "没有成功删除任何配置文件")

        except Exception as e:
            print(f"❌ 删除已使用配置异常: {e}")
            messagebox.showerror("错误", f"删除已使用配置失败: {e}")

    def _launch_browser(self):
        """启动浏览器（与autoback.py保持一致的方法）"""
        profile_name = self._get_selected_profile()
        if not profile_name:
            messagebox.showinfo("提示", "请先选择一个配置文件")
            return

        # 防重复点击检查
        if hasattr(self, '_browser_launching') and self._browser_launching:
            self.status_var.set("浏览器正在启动中，请稍候...")
            return

        try:
            self._browser_launching = True
            self.status_var.set(f"正在启动浏览器: {profile_name}")

            def launch_and_open():
                try:
                    # 检查配置是否存在
                    profile = self.config_manager.get_profile(profile_name)
                    if not profile:
                        raise ValueError(f"配置 {profile_name} 不存在")

                    print(f"🚀 启动配置: {profile_name}")
                    print(f"📁 配置路径: {profile.get('path', 'N/A')}")
                    print(f"🎭 Stealth模式: {profile.get('stealth_enabled', False)}")

                    # 检查是否已经在运行
                    if profile_name in self.browser_manager.running_browsers:
                        # 验证是否真的在运行
                        is_running = self.browser_manager._comprehensive_browser_check(
                            self.browser_manager.running_browsers[profile_name]
                        )
                        if is_running:
                            print(f"✅ 浏览器已在运行，直接使用: {profile_name}")
                            self.root.after(0, lambda: self.status_var.set(f"配置已打开: {profile_name}"))
                            self.root.after(0, self._load_profiles)
                            return
                        else:
                            print(f"🧹 清理无效的浏览器记录: {profile_name}")
                            del self.browser_manager.running_browsers[profile_name]

                    # 启动浏览器（会自动恢复上次会话）
                    browser_info = self.browser_manager.launch_browser(profile_name)

                    if browser_info:
                        stealth_mode = browser_info.get('stealth_mode', False)
                        pid = browser_info.get('pid', 'N/A')

                        print(f"✅ 浏览器启动成功: {profile_name}")
                        print(f"🔧 模式: {'Stealth' if stealth_mode else '标准'}")
                        print(f"🆔 PID: {pid}")

                        # 更新UI状态
                        status_msg = f"配置已打开: {profile_name}"
                        if stealth_mode:
                            status_msg += " (Stealth模式)"

                        self.root.after(0, lambda: self.status_var.set(status_msg))
                        self.root.after(0, self._load_profiles)

                        # 对于新启动的浏览器，等待一段时间让会话恢复完成
                        if not browser_info.get('existing_instance', False):
                            print(f"⏳ 等待会话恢复完成...")
                            # 给Chrome更多时间来恢复会话
                            self.root.after(5000, lambda: self._check_session_restore(profile_name))
                        else:
                            print(f"🔄 复用现有实例，会话应该已经存在")
                    else:
                        self.status_var.set("浏览器启动失败")
                        self.root.after(0, lambda: messagebox.showerror("错误", "浏览器启动失败"))

                except Exception as e:
                    error_msg = str(e)
                    self.status_var.set("就绪")
                    self.root.after(0, lambda msg=error_msg: messagebox.showerror("错误", f"启动浏览器失败: {msg}"))
                finally:
                    # 重置启动标志
                    self._browser_launching = False

            # 在后台线程中启动浏览器，避免UI阻塞
            print(f"🚀 开始在后台启动浏览器: {profile_name}")
            self.status_var.set("正在启动浏览器...")

            # 使用daemon线程，确保主程序退出时线程也会退出
            thread = threading.Thread(target=launch_and_open, daemon=True)
            thread.start()

            print(f"✅ 启动线程已创建，浏览器正在后台启动...")

        except Exception as e:
            self._browser_launching = False
            self.status_var.set("就绪")
            messagebox.showerror("错误", f"启动浏览器异常: {e}")

    def _open_target_website(self, profile_name):
        """智能打开目标网站（保护登录状态）"""
        try:
            target_url = "https://app.augmentcode.com/account/team"

            # 首先检查当前页面状态
            driver = self.browser_manager.get_automation_driver(profile_name)
            if not driver:
                self.status_var.set(f"浏览器已启动: {profile_name}")
                return

            current_status = driver.execute_script("""
                const currentUrl = window.location.href;
                const isAugmentSite = currentUrl.includes('augmentcode.com');
                const isLoginPage = currentUrl.includes('login') ||
                                  document.querySelector('input[type="password"]') !== null;
                const isTeamPage = currentUrl.includes('/account/team');

                return {
                    currentUrl: currentUrl,
                    isAugmentSite: isAugmentSite,
                    isLoginPage: isLoginPage,
                    isTeamPage: isTeamPage,
                    pageTitle: document.title
                };
            """)

            if current_status:
                current_url = current_status.get('currentUrl', '')
                is_augment_site = current_status.get('isAugmentSite', False)
                is_login_page = current_status.get('isLoginPage', False)
                is_team_page = current_status.get('isTeamPage', False)

                print(f"🔍 当前页面状态: {current_url}")

                # 如果已经在团队页面，不需要导航
                if is_team_page:
                    print("✅ 已在团队页面，无需导航")
                    self.status_var.set(f"配置已打开: {profile_name} (已在团队页面)")
                    return

                # 如果在登录页面，不强制导航（让用户自己登录）
                if is_login_page:
                    print("⚠️ 当前在登录页面，请手动登录后使用")
                    self.status_var.set(f"配置已打开: {profile_name} (请登录)")
                    return

                # 如果在Augment网站的其他页面，保持当前页面
                if is_augment_site and not is_team_page:
                    print("🔄 检测到已在Augment网站，保持当前页面")
                    self.status_var.set(f"配置已打开: {profile_name} (保持当前页面)")
                    return

            # 只有在非Augment网站或新打开的浏览器时才导航
            print(f"🌐 导航到目标页面: {target_url}")

            # 使用自动化引擎打开网页
            if hasattr(self, 'automation_engine') and self.automation_engine:
                self.automation_engine.navigate_to_url(profile_name, target_url)
                self.status_var.set(f"已打开目标网站: {profile_name}")
            else:
                # 如果没有自动化引擎，尝试直接操作浏览器
                driver.get(target_url)
                self.status_var.set(f"已打开目标网站: {profile_name}")

        except Exception as e:
            print(f"打开目标网站失败: {e}")
            self.status_var.set(f"浏览器已启动: {profile_name}")  # 即使打开网站失败，浏览器也已启动

    def _close_browser(self):
        """关闭浏览器"""
        profile_name = self._get_selected_profile()
        if not profile_name:
            messagebox.showinfo("提示", "请先选择一个配置文件")
            return

        try:
            if self.browser_manager.close_browser(profile_name):
                self.status_var.set(f"已关闭浏览器: {profile_name}")
                self._load_profiles()  # 更新状态
            else:
                messagebox.showinfo("提示", f"浏览器 '{profile_name}' 未在运行")
        except Exception as e:
            messagebox.showerror("错误", f"关闭浏览器失败: {e}")

    def _close_all_browsers(self):
        """关闭所有浏览器"""
        try:
            closed_count = self.browser_manager.close_all_browsers()
            self.status_var.set(f"已关闭 {closed_count} 个浏览器实例")
            self._load_profiles()  # 更新状态
        except Exception as e:
            messagebox.showerror("错误", f"关闭浏览器失败: {e}")

    def _on_profile_double_click(self, event):
        """双击配置文件时打开配置（与autoback.py一致）"""
        self._launch_browser()

    def _open_url(self):
        """打开网页 - 直接打开Augment网址"""
        profile_name = self._get_selected_profile()
        if not profile_name:
            messagebox.showinfo("提示", "请先选择一个配置文件，然后点击'🌐 打开配置'")
            return

        # 检查浏览器是否已启动
        running_browsers = self.browser_manager.get_running_browsers()
        if profile_name not in running_browsers:
            messagebox.showinfo("提示", f"请先打开配置 '{profile_name}'")
            return

        # 直接打开Augment网址，不弹出输入框
        url = "https://app.augmentcode.com/account/team"

        try:
            self.automation_engine.navigate_to_url(profile_name, url)
            self.status_var.set(f"已打开网页: {url}")
        except Exception as e:
            messagebox.showerror("错误", f"打开网页失败: {e}")

    def _open_custom_url(self):
        """打开自定义网页"""
        profile_name = self._get_selected_profile()
        if not profile_name:
            messagebox.showinfo("提示", "请先选择一个配置文件")
            return

        # 默认显示Augment网址，用户可以修改
        default_url = "https://app.augmentcode.com/account/team"

        url = simpledialog.askstring(
            "自定义网页",
            "请输入网址:",
            initialvalue=default_url
        )
        if not url:
            return

        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        try:
            self.automation_engine.navigate_to_url(profile_name, url)
            self.status_var.set(f"已打开自定义网页: {url}")
        except Exception as e:
            messagebox.showerror("错误", f"打开自定义网页失败: {e}")

    def _add_member_click(self):
        """智能按钮点击 - 查找并点击页面上的可点击元素"""
        profile_name = self._get_selected_profile()
        if not profile_name:
            messagebox.showinfo("提示", "请先选择一个配置文件")
            return

        # 检查浏览器是否已启动
        running_browsers = self.browser_manager.get_running_browsers()
        if profile_name not in running_browsers:
            messagebox.showinfo("提示", f"请先打开配置 '{profile_name}'")
            return

        try:
            # 第一步：检查页面是否加载完成
            self.status_var.set("检查页面加载状态...")

            page_status = self.automation_engine.execute_script(profile_name, """
                return {
                    readyState: document.readyState,
                    url: window.location.href,
                    title: document.title,
                    hasContent: document.body && document.body.children.length > 0,
                    loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart
                };
            """)

            ready_state = page_status.get('readyState')
            has_content = page_status.get('hasContent')
            current_url = page_status.get('url', '')

            # 检查页面加载状态
            if ready_state != 'complete':
                self.status_var.set("等待页面加载完成...")
                messagebox.showinfo("等待", f"页面正在加载中 (状态: {ready_state})\n请等待页面完全加载后再试")
                return

            if not has_content:
                self.status_var.set("页面内容为空")
                messagebox.showwarning("提示", "页面似乎没有内容，请确保页面已正确加载")
                return

            if current_url in ['about:blank', 'data:', ''] or current_url.startswith('chrome://'):
                self.status_var.set("无效页面")
                messagebox.showinfo("提示", "当前页面无效，请先访问一个网站")
                return

            # 显示页面加载信息
            load_time = page_status.get('loadTime', 0)
            self.status_var.set(f"页面已加载完成 ({load_time}ms)")

            print(f"页面检查通过:")
            print(f"  - 加载状态: {ready_state}")
            print(f"  - 页面标题: {page_status.get('title', 'N/A')}")
            print(f"  - 当前URL: {current_url}")
            print(f"  - 加载时间: {load_time}ms")

            # 第二步：等待额外时间确保动态内容加载
            import time
            time.sleep(1)  # 等待1秒确保动态内容加载

            # 第三步：智能查找页面上的可点击元素
            self.status_var.set("分析页面元素...")
            result = self.automation_engine.execute_script(profile_name, """
                // 查找页面上所有可能的按钮和链接
                const clickableElements = [];

                // 查找所有按钮
                const buttons = Array.from(document.querySelectorAll('button, input[type="button"], input[type="submit"]'));
                buttons.forEach(btn => {
                    if (btn.offsetParent !== null && btn.textContent.trim()) { // 可见且有文本
                        clickableElements.push({
                            type: 'button',
                            text: btn.textContent.trim(),
                            element: btn,
                            tag: btn.tagName
                        });
                    }
                });

                // 查找所有链接
                const links = Array.from(document.querySelectorAll('a[href]'));
                links.forEach(link => {
                    if (link.offsetParent !== null && link.textContent.trim()) { // 可见且有文本
                        clickableElements.push({
                            type: 'link',
                            text: link.textContent.trim(),
                            element: link,
                            tag: link.tagName
                        });
                    }
                });

                // 优先查找包含特定关键词的元素
                const keywords = ['add', 'member', 'invite', 'create', 'new', '添加', '邀请', '创建', '新建'];
                let targetElement = null;

                for (const keyword of keywords) {
                    const found = clickableElements.find(el =>
                        el.text.toLowerCase().includes(keyword.toLowerCase())
                    );
                    if (found) {
                        targetElement = found;
                        break;
                    }
                }

                // 如果没找到关键词匹配的，返回所有可点击元素供用户选择
                if (targetElement) {
                    // 点击找到的元素
                    targetElement.element.click();
                    return {
                        success: true,
                        clicked: true,
                        element: targetElement.text,
                        type: targetElement.type,
                        tag: targetElement.tag
                    };
                } else {
                    // 返回前10个可点击元素
                    return {
                        success: true,
                        clicked: false,
                        availableElements: clickableElements.slice(0, 10).map(el => ({
                            text: el.text.substring(0, 50),
                            type: el.type,
                            tag: el.tag
                        }))
                    };
                }
            """)

            if result.get('success'):
                if result.get('clicked'):
                    # 成功点击了元素
                    element_text = result.get('element')
                    element_type = result.get('type')
                    self.status_var.set(f"已点击{element_type}: {element_text}")

                    # 第四步：等待弹窗出现并自动输入邮箱
                    self._handle_popup_email_input(profile_name, element_text, element_type)
                else:
                    # 显示可用的元素供用户了解
                    available = result.get('availableElements', [])
                    if available:
                        elements_text = "\n".join([f"• {el['type']}: {el['text']}" for el in available[:5]])
                        self.status_var.set("页面分析完成")
                        messagebox.showinfo("页面分析",
                            f"未找到包含关键词的按钮，但发现以下可点击元素:\n\n{elements_text}\n\n"
                            f"{'...' if len(available) > 5 else ''}"
                            f"\n\n你可以手动点击需要的元素，或使用'执行脚本'功能进行自定义操作。")
                    else:
                        self.status_var.set("未找到可点击元素")
                        messagebox.showinfo("提示", "页面上未找到明显的可点击元素")
            else:
                self.status_var.set("页面分析失败")
                messagebox.showwarning("提示", "无法分析页面元素，请确保页面已完全加载")

        except Exception as e:
            messagebox.showerror("错误", f"智能点击功能失败: {e}")
            self.status_var.set("智能点击功能失败")

    def _handle_popup_email_input(self, profile_name: str, element_text: str, element_type: str):
        """处理弹窗出现后的邮箱输入"""
        try:
            import time

            # 等待弹窗出现
            self.status_var.set("等待弹窗出现...")
            time.sleep(2)  # 等待2秒让弹窗完全显示

            # 查找邮箱输入框
            email_input_result = self.automation_engine.execute_script(profile_name, """
                // 查找邮箱输入框的多种可能选择器
                const selectors = [
                    'div[data-testid="token-input"]',
                    'div.input.tag-input',
                    'div[contenteditable="true"][role="textbox"]',
                    'div[data-placeholder*="@"]',
                    'input[type="email"]',
                    'input[placeholder*="@"]',
                    'div[contenteditable="true"]'
                ];

                let inputElement = null;
                let usedSelector = '';

                // 尝试每个选择器
                for (const selector of selectors) {
                    const elements = document.querySelectorAll(selector);
                    for (const element of elements) {
                        // 检查元素是否可见
                        if (element.offsetParent !== null) {
                            inputElement = element;
                            usedSelector = selector;
                            break;
                        }
                    }
                    if (inputElement) break;
                }

                if (inputElement) {
                    // 聚焦到输入框
                    inputElement.focus();

                    // 清空现有内容
                    if (inputElement.tagName.toLowerCase() === 'input') {
                        inputElement.value = '';
                    } else {
                        inputElement.textContent = '';
                        inputElement.innerHTML = '';
                    }

                    // 输入邮箱地址
                    const email = '<EMAIL>';

                    if (inputElement.tagName.toLowerCase() === 'input') {
                        inputElement.value = email;
                        // 触发input事件
                        inputElement.dispatchEvent(new Event('input', { bubbles: true }));
                        inputElement.dispatchEvent(new Event('change', { bubbles: true }));
                    } else {
                        // 对于contenteditable的div
                        inputElement.textContent = email;
                        inputElement.innerHTML = email;

                        // 触发相关事件
                        inputElement.dispatchEvent(new Event('input', { bubbles: true }));
                        inputElement.dispatchEvent(new Event('change', { bubbles: true }));
                        inputElement.dispatchEvent(new Event('blur', { bubbles: true }));
                    }

                    return {
                        success: true,
                        found: true,
                        selector: usedSelector,
                        tagName: inputElement.tagName,
                        email: email,
                        placeholder: inputElement.getAttribute('data-placeholder') || inputElement.placeholder || '',
                        isContentEditable: inputElement.contentEditable === 'true'
                    };
                } else {
                    // 返回页面上所有可能的输入元素信息
                    const allInputs = Array.from(document.querySelectorAll('input, textarea, div[contenteditable="true"]'));
                    const visibleInputs = allInputs.filter(el => el.offsetParent !== null);

                    return {
                        success: true,
                        found: false,
                        message: '未找到邮箱输入框',
                        availableInputs: visibleInputs.slice(0, 5).map(input => ({
                            tagName: input.tagName,
                            type: input.type || 'N/A',
                            placeholder: input.placeholder || input.getAttribute('data-placeholder') || '',
                            className: input.className,
                            id: input.id,
                            contentEditable: input.contentEditable
                        }))
                    };
                }
            """)

            if email_input_result.get('success'):
                if email_input_result.get('found'):
                    # 成功找到并输入邮箱
                    selector = email_input_result.get('selector')
                    tag_name = email_input_result.get('tagName')
                    email = email_input_result.get('email')
                    is_content_editable = email_input_result.get('isContentEditable')

                    self.status_var.set(f"已输入邮箱: {email}")
                    messagebox.showinfo("成功",
                        f"✅ 成功点击{element_type}: {element_text}\n"
                        f"✅ 找到邮箱输入框: {tag_name}\n"
                        f"✅ 已自动输入邮箱: {email}\n"
                        f"📍 使用选择器: {selector}\n"
                        f"📝 输入方式: {'ContentEditable' if is_content_editable else 'Input Value'}")

                    # 等待邮箱输入完成，然后查找并点击"Add 1 Member"按钮
                    time.sleep(2)  # 增加等待时间，确保按钮状态更新
                    self._click_add_member_button(profile_name)

                else:
                    # 没找到邮箱输入框，显示可用的输入元素
                    available_inputs = email_input_result.get('availableInputs', [])
                    inputs_text = "\n".join([
                        f"• {inp['tagName']} (type: {inp['type']}, class: {inp['className'][:30]})"
                        for inp in available_inputs[:3]
                    ])

                    self.status_var.set("未找到邮箱输入框")
                    messagebox.showinfo("部分成功",
                        f"✅ 成功点击{element_type}: {element_text}\n"
                        f"❌ 未找到邮箱输入框\n\n"
                        f"页面上发现的输入元素:\n{inputs_text}\n\n"
                        f"请手动输入邮箱或检查页面是否完全加载")
            else:
                self.status_var.set("弹窗处理失败")
                messagebox.showwarning("警告", f"点击成功但弹窗处理失败")

        except Exception as e:
            self.status_var.set("弹窗处理出错")
            messagebox.showerror("错误", f"弹窗处理失败: {e}")

    def _click_add_member_button(self, profile_name: str):
        """查找并点击'Add 1 Member'按钮"""
        try:
            import time

            # 等待按钮出现并激活（输入邮箱后按钮可能需要时间激活）
            self.status_var.set("等待Add Member按钮激活...")

            # 多次尝试查找按钮，因为按钮可能需要时间激活
            max_attempts = 5
            for attempt in range(max_attempts):
                print(f"尝试查找Add Member按钮 (第{attempt + 1}次)")

                # 查找"Add 1 Member"按钮
                button_result = self.automation_engine.execute_script(profile_name, """
                    console.log('开始查找Add 1 Member按钮...');

                    // 查找所有按钮
                    const allButtons = Array.from(document.querySelectorAll('button'));
                    console.log('页面上总共找到', allButtons.length, '个按钮');

                    let targetButton = null;
                    let buttonText = '';
                    let buttonClasses = '';
                    let buttonInfo = [];

                    // 记录所有可见按钮的信息
                    allButtons.forEach((button, index) => {
                        if (button.offsetParent !== null) { // 只记录可见按钮
                            const text = button.textContent.trim();
                            const classes = button.className;
                            const isDisabled = button.disabled ||
                                             button.hasAttribute('disabled') ||
                                             button.getAttribute('aria-disabled') === 'true' ||
                                             button.classList.contains('disabled');

                            buttonInfo.push({
                                index: index,
                                text: text,
                                classes: classes,
                                disabled: isDisabled,
                                hasRtButton: classes.includes('rt-Button'),
                                hasDataAccent: button.hasAttribute('data-accent-color')
                            });

                            console.log(`按钮 ${index}: "${text}" | 类名: "${classes}" | 禁用: ${isDisabled}`);
                        }
                    });

                    // 查找策略1: 精确匹配"Add 1 Member"
                    for (const button of allButtons) {
                        if (button.offsetParent === null) continue;

                        const text = button.textContent.trim();
                        if (text === 'Add 1 Member') {
                            targetButton = button;
                            buttonText = text;
                            buttonClasses = button.className;
                            console.log('找到精确匹配的按钮:', text);
                            break;
                        }
                    }

                    // 查找策略2: 匹配包含"Add"和数字的按钮
                    if (!targetButton) {
                        for (const button of allButtons) {
                            if (button.offsetParent === null) continue;

                            const text = button.textContent.trim();
                            if (text.includes('Add') && /\\d/.test(text) && text.includes('Member')) {
                                targetButton = button;
                                buttonText = text;
                                buttonClasses = button.className;
                                console.log('找到包含Add和数字的按钮:', text);
                                break;
                            }
                        }
                    }

                    // 查找策略3: 匹配rt-Button类且包含"Add"的按钮
                    if (!targetButton) {
                        for (const button of allButtons) {
                            if (button.offsetParent === null) continue;

                            const text = button.textContent.trim();
                            const classes = button.className;
                            if (classes.includes('rt-Button') &&
                                classes.includes('rt-variant-solid') &&
                                text.includes('Add')) {
                                targetButton = button;
                                buttonText = text;
                                buttonClasses = classes;
                                console.log('找到rt-Button类的Add按钮:', text);
                                break;
                            }
                        }
                    }

                    // 查找策略4: 匹配有data-accent-color属性且包含"Add"的按钮
                    if (!targetButton) {
                        for (const button of allButtons) {
                            if (button.offsetParent === null) continue;

                            const text = button.textContent.trim();
                            if (button.hasAttribute('data-accent-color') && text.includes('Add')) {
                                targetButton = button;
                                buttonText = text;
                                buttonClasses = button.className;
                                console.log('找到data-accent-color的Add按钮:', text);
                                break;
                            }
                        }
                    }

                    if (targetButton) {
                        // 检查按钮是否可点击
                        const isDisabled = targetButton.disabled ||
                                         targetButton.hasAttribute('disabled') ||
                                         targetButton.getAttribute('aria-disabled') === 'true' ||
                                         targetButton.classList.contains('disabled');

                        console.log('找到目标按钮:', buttonText, '禁用状态:', isDisabled);

                        if (!isDisabled) {
                            // 先聚焦按钮，然后点击
                            targetButton.focus();

                            // 使用多种点击方式确保成功
                            targetButton.click();

                            // 也尝试触发鼠标事件
                            const clickEvent = new MouseEvent('click', {
                                bubbles: true,
                                cancelable: true,
                                view: window
                            });
                            targetButton.dispatchEvent(clickEvent);

                            console.log('按钮点击完成');

                            return {
                                success: true,
                                clicked: true,
                                buttonText: buttonText,
                                buttonClasses: buttonClasses,
                                isDisabled: false,
                                attempt: """ + str(attempt + 1) + """
                            };
                        } else {
                            return {
                                success: true,
                                clicked: false,
                                buttonText: buttonText,
                                buttonClasses: buttonClasses,
                                isDisabled: true,
                                message: '按钮处于禁用状态',
                                attempt: """ + str(attempt + 1) + """
                            };
                        }
                    } else {
                        console.log('未找到目标按钮');
                        return {
                            success: true,
                            clicked: false,
                            found: false,
                            message: '未找到Add Member按钮',
                            availableButtons: buttonInfo,
                            attempt: """ + str(attempt + 1) + """
                        };
                    }
                """)

                if button_result.get('success'):
                    if button_result.get('clicked'):
                        # 成功点击了按钮
                        button_text = button_result.get('buttonText')
                        button_classes = button_result.get('buttonClasses')
                        attempt_num = button_result.get('attempt')

                        self.status_var.set(f"已点击: {button_text}")
                        print(f"✅ 成功点击按钮: {button_text} (第{attempt_num}次尝试)")
                        print(f"   按钮类名: {button_classes}")

                        messagebox.showinfo("完成",
                            f"✅ 邮箱输入完成: <EMAIL>\n"
                            f"✅ 成功点击按钮: {button_text}\n"
                            f"🎉 Add Member操作已完成！\n"
                            f"📊 尝试次数: {attempt_num}")
                        return  # 成功后退出循环

                    elif button_result.get('isDisabled'):
                        # 找到按钮但是禁用状态，继续等待
                        button_text = button_result.get('buttonText')
                        attempt_num = button_result.get('attempt')
                        print(f"⚠️ 按钮找到但禁用 (第{attempt_num}次): {button_text}")

                        if attempt < max_attempts - 1:
                            self.status_var.set(f"按钮禁用，等待激活... ({attempt + 1}/{max_attempts})")
                            time.sleep(2)  # 等待按钮激活
                            continue
                        else:
                            # 最后一次尝试仍然禁用
                            self.status_var.set("按钮持续禁用状态")
                            messagebox.showwarning("提示",
                                f"✅ 邮箱输入完成: <EMAIL>\n"
                                f"⚠️ 找到按钮但持续禁用: {button_text}\n"
                                f"📊 尝试次数: {max_attempts}\n"
                                f"💡 可能原因: 邮箱格式验证未通过或需要更长等待时间")
                            return
                    else:
                        # 没找到按钮
                        attempt_num = button_result.get('attempt')
                        available_buttons = button_result.get('availableButtons', [])
                        print(f"❌ 未找到按钮 (第{attempt_num}次)")

                        if attempt < max_attempts - 1:
                            self.status_var.set(f"未找到按钮，继续查找... ({attempt + 1}/{max_attempts})")
                            time.sleep(2)  # 等待按钮出现
                            continue
                        else:
                            # 最后一次尝试仍然没找到
                            if available_buttons:
                                buttons_text = "\n".join([
                                    f"• {btn['text']} (rt-Button: {btn['hasRtButton']}, disabled: {btn['disabled']})"
                                    for btn in available_buttons[:5]
                                ])

                                self.status_var.set("未找到Add Member按钮")
                                messagebox.showinfo("部分完成",
                                    f"✅ 邮箱输入完成: <EMAIL>\n"
                                    f"❌ 未找到'Add 1 Member'按钮\n"
                                    f"📊 尝试次数: {max_attempts}\n\n"
                                    f"页面上发现的按钮:\n{buttons_text}\n\n"
                                    f"💡 请手动点击相应的按钮完成操作")
                            else:
                                self.status_var.set("页面上无可用按钮")
                                messagebox.showinfo("部分完成",
                                    f"✅ 邮箱输入完成: <EMAIL>\n"
                                    f"❌ 页面上未找到任何可点击的按钮\n"
                                    f"📊 尝试次数: {max_attempts}")
                            return
                else:
                    print(f"❌ 脚本执行失败 (第{attempt + 1}次)")
                    if attempt < max_attempts - 1:
                        time.sleep(2)
                        continue
                    else:
                        self.status_var.set("按钮查找脚本失败")
                        messagebox.showwarning("警告", f"邮箱输入完成但按钮查找脚本执行失败")
                        return

        except Exception as e:
            self.status_var.set("Add Member按钮点击失败")
            print(f"❌ Add Member按钮点击过程中出现异常: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"Add Member按钮点击失败: {e}")

    def _check_for_submit_button(self, profile_name: str):
        """检查是否有提交按钮并询问用户是否要点击"""
        try:
            submit_result = self.automation_engine.execute_script(profile_name, """
                // 查找可能的提交按钮
                const submitSelectors = [
                    'button[type="submit"]',
                    'input[type="submit"]',
                    'button:contains("Submit")',
                    'button:contains("Send")',
                    'button:contains("Invite")',
                    'button:contains("Add")',
                    'button:contains("确定")',
                    'button:contains("发送")',
                    'button:contains("邀请")'
                ];

                const buttons = Array.from(document.querySelectorAll('button, input[type="submit"]'));
                const submitButtons = buttons.filter(btn => {
                    if (btn.offsetParent === null) return false; // 不可见

                    const text = btn.textContent.toLowerCase().trim();
                    const type = btn.type ? btn.type.toLowerCase() : '';

                    return type === 'submit' ||
                           text.includes('submit') ||
                           text.includes('send') ||
                           text.includes('invite') ||
                           text.includes('add') ||
                           text.includes('确定') ||
                           text.includes('发送') ||
                           text.includes('邀请');
                });

                return {
                    found: submitButtons.length > 0,
                    buttons: submitButtons.slice(0, 3).map(btn => ({
                        text: btn.textContent.trim(),
                        type: btn.type || 'button',
                        className: btn.className
                    }))
                };
            """)

            if submit_result.get('found'):
                buttons = submit_result.get('buttons', [])
                buttons_text = "\n".join([f"• {btn['text']}" for btn in buttons])

                # 询问用户是否要点击提交按钮
                response = messagebox.askyesno("发现提交按钮",
                    f"发现以下提交按钮:\n{buttons_text}\n\n是否要自动点击第一个按钮？")

                if response:
                    # 点击第一个提交按钮
                    click_result = self.automation_engine.execute_script(profile_name, """
                        const buttons = Array.from(document.querySelectorAll('button, input[type="submit"]'));
                        const submitButtons = buttons.filter(btn => {
                            if (btn.offsetParent === null) return false;
                            const text = btn.textContent.toLowerCase().trim();
                            const type = btn.type ? btn.type.toLowerCase() : '';
                            return type === 'submit' || text.includes('submit') || text.includes('send') ||
                                   text.includes('invite') || text.includes('add') || text.includes('确定') ||
                                   text.includes('发送') || text.includes('邀请');
                        });

                        if (submitButtons.length > 0) {
                            submitButtons[0].click();
                            return {success: true, buttonText: submitButtons[0].textContent.trim()};
                        }
                        return {success: false};
                    """)

                    if click_result.get('success'):
                        button_text = click_result.get('buttonText')
                        self.status_var.set(f"已点击提交按钮: {button_text}")
                        messagebox.showinfo("完成", f"✅ 邮箱输入完成\n✅ 已点击提交按钮: {button_text}")
                    else:
                        messagebox.showwarning("提示", "点击提交按钮失败")

        except Exception as e:
            print(f"检查提交按钮时出错: {e}")

    def _take_screenshot(self):
        """截图"""
        profile_name = self._get_selected_profile()
        if not profile_name:
            messagebox.showinfo("提示", "请先选择一个配置文件")
            return

        try:
            filename = self.automation_engine.take_screenshot(profile_name)
            self.status_var.set(f"截图已保存: {filename}")
            messagebox.showinfo("成功", f"截图已保存为: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"截图失败: {e}")

    def _execute_script(self):
        """执行脚本"""
        profile_name = self._get_selected_profile()
        if not profile_name:
            messagebox.showinfo("提示", "请先选择一个配置文件")
            return

        script = simpledialog.askstring("执行脚本", "请输入JavaScript代码:")
        if not script:
            return

        try:
            result = self.automation_engine.execute_script(profile_name, script)
            self.status_var.set("脚本执行完成")
            messagebox.showinfo("执行结果", f"脚本执行结果: {result}")
        except Exception as e:
            messagebox.showerror("错误", f"脚本执行失败: {e}")

    def _edit_profile(self):
        """编辑配置"""
        messagebox.showinfo("提示", "配置编辑功能待实现")

    def _export_profile(self):
        """导出配置"""
        messagebox.showinfo("提示", "配置导出功能待实现")

    def _import_profile(self):
        """导入配置"""
        messagebox.showinfo("提示", "配置导入功能待实现")

    def _sync_profiles(self):
        """同步profiles.json与browser_profiles文件夹"""
        try:
            # 显示确认对话框
            result = messagebox.askyesno(
                "确认同步",
                "此操作将根据browser_profiles文件夹同步profiles.json配置：\n\n"
                "• 存在的文件夹名称相关的配置将保留\n"
                "• 不存在的文件夹对应的配置将被删除\n"
                "• 如果存在profiles2.json，将自动合并其内容\n\n"
                "确定要继续吗？",
                icon='question'
            )

            if not result:
                return

            # 执行同步
            self.status_var.set("正在同步配置...")
            sync_result = self.config_manager.sync_profiles_with_folders()

            if sync_result:
                # 刷新配置列表
                self._load_profiles()

                # 显示同步结果
                message = f"配置同步完成！\n\n"
                message += f"删除: {len(sync_result['removed'])} 个\n"
                if sync_result['added']:
                    message += f"从profiles2.json添加: {len(sync_result['added'])} 个\n"
                message += f"保留: {len(sync_result['kept'])} 个\n"
                message += f"总计: {sync_result['total_before']} -> {sync_result['total_after']}"

                if sync_result['removed']:
                    message += f"\n\n删除的配置:\n" + "\n".join(f"• {name}" for name in sync_result['removed'])

                if sync_result['added']:
                    message += f"\n\n从profiles2.json添加的配置:\n" + "\n".join(f"• {name}" for name in sync_result['added'])

                messagebox.showinfo("同步完成", message)
                status_msg = f"配置同步完成 - 删除{len(sync_result['removed'])}个"
                if sync_result['added']:
                    status_msg += f"，从profiles2.json添加{len(sync_result['added'])}个"
                self.status_var.set(status_msg)
            else:
                messagebox.showerror("错误", "配置同步失败，请查看控制台输出")
                self.status_var.set("配置同步失败")

        except Exception as e:
            messagebox.showerror("错误", f"同步配置时发生错误: {str(e)}")
            self.status_var.set("配置同步失败")

    def _update_status(self):
        """定期更新状态 - 非阻塞版本"""
        try:
            import threading
            import time

            def safe_update():
                """安全的后台更新 - 避免阻塞操作"""
                try:
                    # 快速获取基本信息，避免耗时操作
                    profiles = self.config_manager.get_all_profiles()

                    # 快速计算运行中的浏览器数量，不做深度检查
                    running_count = 0
                    for name, browser_info in list(self.browser_manager.running_browsers.items()):
                        try:
                            # 只做最基本的检查
                            process = browser_info.get("process")
                            if process and hasattr(process, 'poll') and process.poll() is None:
                                running_count += 1
                            elif browser_info.get("stealth_mode") and "driver" in browser_info:
                                running_count += 1  # Stealth模式假设还在运行
                        except:
                            pass

                    # 在主线程中更新UI
                    def update_ui():
                        try:
                            self._load_profiles()

                            # 更新状态栏
                            if hasattr(self, 'status_var'):
                                current_status = self.status_var.get()
                                if not current_status.startswith("正在") and not current_status.startswith("配置已打开"):
                                    status_text = f"配置: {len(profiles)} | 运行中: {running_count}"
                                    self.status_var.set(status_text)
                        except Exception as e:
                            print(f"⚠️ 更新UI时出错: {e}")

                    # 在主线程中执行UI更新
                    self.root.after(0, update_ui)

                except Exception as e:
                    print(f"⚠️ 后台更新失败: {e}")
                    # 如果后台更新失败，至少刷新一下界面
                    self.root.after(0, lambda: self._load_profiles())

            # 在后台线程中执行更新
            threading.Thread(target=safe_update, daemon=True).start()

        except Exception as e:
            print(f"⚠️ 启动状态更新失败: {e}")
            # 如果线程启动失败，直接更新
            try:
                self._load_profiles()
            except Exception as e2:
                print(f"⚠️ 直接更新也失败: {e2}")

        # 每5秒更新一次，减少频率避免阻塞
        self.root.after(5000, self._update_status)

    def _check_session_restore(self, profile_name):
        """检查会话恢复状态 - 非阻塞版本"""
        def safe_check():
            """在后台线程中安全检查"""
            try:
                print(f"🔍 后台检查会话恢复状态: {profile_name}")

                # 首先检查浏览器是否还在运行
                if profile_name not in self.browser_manager.running_browsers:
                    print(f"⚠️ 浏览器 {profile_name} 不在运行列表中")
                    self.root.after(0, lambda: self.status_var.set(f"配置已关闭: {profile_name}"))
                    return

                browser_info = self.browser_manager.running_browsers[profile_name]

                # 对于Stealth模式，检查driver是否有效
                if browser_info.get("stealth_mode") and "driver" in browser_info:
                    try:
                        driver = browser_info["driver"]
                        current_url = driver.current_url

                        print(f"📄 Stealth模式当前页面: {current_url}")

                        if current_url and current_url not in ["data:,", "about:blank"]:
                            self.root.after(0, lambda: self.status_var.set(f"Stealth会话已恢复: {profile_name}"))
                        else:
                            self.root.after(0, lambda: self.status_var.set(f"Stealth配置已打开: {profile_name}"))
                        return

                    except Exception as e:
                        print(f"⚠️ Stealth driver检查失败: {e}")
                        self.root.after(0, lambda: self.status_var.set(f"Stealth配置已打开: {profile_name}"))
                        return

                # 对于标准模式，检查debug端口
                debug_port = browser_info.get("debug_port")
                if debug_port:
                    # 快速检查端口是否可达
                    if self.browser_manager._check_debug_port_available(debug_port):
                        print(f"✅ Debug端口 {debug_port} 可达")
                        self.root.after(0, lambda: self.status_var.set(f"配置已打开: {profile_name}"))
                    else:
                        print(f"⚠️ Debug端口 {debug_port} 不可达，浏览器可能已关闭")
                        self.root.after(0, lambda: self.status_var.set(f"配置已关闭: {profile_name}"))
                        # 清理无效记录
                        if profile_name in self.browser_manager.running_browsers:
                            del self.browser_manager.running_browsers[profile_name]
                else:
                    print(f"⚠️ 没有debug端口信息")
                    self.root.after(0, lambda: self.status_var.set(f"配置状态未知: {profile_name}"))

            except Exception as e:
                print(f"⚠️ 后台会话检查失败: {e}")
                self.root.after(0, lambda: self.status_var.set(f"配置已打开: {profile_name}"))

        # 在后台线程中执行检查，避免阻塞UI
        import threading
        threading.Thread(target=safe_check, daemon=True).start()

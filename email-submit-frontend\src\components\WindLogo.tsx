/**
 * 风字Logo组件
 * 漂亮的风车风格设计
 */
import React from 'react';

interface WindLogoProps {
  size?: number;
  color?: string;
  className?: string;
  style?: React.CSSProperties;
}

const WindLogo: React.FC<WindLogoProps> = ({ 
  size = 24, 
  color = '#1890ff', 
  className,
  style 
}) => {
  return (
    <div 
      className={className}
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: size,
        height: size,
        ...style
      }}
    >
      <svg
        width={size}
        height={size}
        viewBox="0 0 100 100"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* 风车背景圆环 */}
        <circle
          cx="50"
          cy="50"
          r="45"
          stroke={color}
          strokeWidth="2"
          fill="none"
          opacity="0.2"
        />
        
        {/* 风车叶片 */}
        <g transform="translate(50,50)">
          {/* 叶片1 */}
          <path
            d="M0,0 L-15,-25 Q-20,-30 -15,-35 Q-10,-30 0,0"
            fill={color}
            opacity="0.8"
          />
          {/* 叶片2 */}
          <path
            d="M0,0 L25,-15 Q30,-20 35,-15 Q30,-10 0,0"
            fill={color}
            opacity="0.9"
          />
          {/* 叶片3 */}
          <path
            d="M0,0 L15,25 Q20,30 15,35 Q10,30 0,0"
            fill={color}
            opacity="0.7"
          />
          {/* 叶片4 */}
          <path
            d="M0,0 L-25,15 Q-30,20 -35,15 Q-30,10 0,0"
            fill={color}
            opacity="0.6"
          />
        </g>
        
        {/* 中心圆点 */}
        <circle
          cx="50"
          cy="50"
          r="4"
          fill={color}
        />
        
        {/* 风字笔画 */}
        <g transform="translate(50,50)" opacity="0.9">
          {/* 外框 */}
          <rect
            x="-18"
            y="-18"
            width="36"
            height="36"
            stroke={color}
            strokeWidth="1.5"
            fill="none"
            rx="2"
          />
          
          {/* 风字内部结构 */}
          {/* 横线1 */}
          <line
            x1="-12"
            y1="-8"
            x2="12"
            y2="-8"
            stroke={color}
            strokeWidth="2"
            strokeLinecap="round"
          />
          
          {/* 横线2 */}
          <line
            x1="-12"
            y1="0"
            x2="8"
            y2="0"
            stroke={color}
            strokeWidth="2"
            strokeLinecap="round"
          />
          
          {/* 横线3 */}
          <line
            x1="-12"
            y1="8"
            x2="12"
            y2="8"
            stroke={color}
            strokeWidth="2"
            strokeLinecap="round"
          />
          
          {/* 竖线 */}
          <line
            x1="8"
            y1="-4"
            x2="8"
            y2="12"
            stroke={color}
            strokeWidth="2"
            strokeLinecap="round"
          />
          
          {/* 飘逸的弧线 */}
          <path
            d="M8,0 Q15,5 12,12"
            stroke={color}
            strokeWidth="1.5"
            fill="none"
            strokeLinecap="round"
          />
        </g>
        
        {/* 装饰性粒子效果 */}
        <g opacity="0.4">
          <circle cx="20" cy="25" r="1" fill={color}>
            <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2s" repeatCount="indefinite" />
          </circle>
          <circle cx="80" cy="30" r="1.5" fill={color}>
            <animate attributeName="opacity" values="0.3;0.7;0.3" dur="2.5s" repeatCount="indefinite" />
          </circle>
          <circle cx="75" cy="70" r="1" fill={color}>
            <animate attributeName="opacity" values="0.5;0.9;0.5" dur="1.8s" repeatCount="indefinite" />
          </circle>
          <circle cx="25" cy="75" r="1.2" fill={color}>
            <animate attributeName="opacity" values="0.2;0.6;0.2" dur="2.2s" repeatCount="indefinite" />
          </circle>
        </g>
        
        {/* 旋转动画的风车叶片 */}
        <g transform="translate(50,50)">
          <animateTransform
            attributeName="transform"
            attributeType="XML"
            type="rotate"
            from="0 0 0"
            to="360 0 0"
            dur="8s"
            repeatCount="indefinite"
          />
          
          {/* 简化的旋转叶片 */}
          <g opacity="0.3">
            <line x1="0" y1="0" x2="0" y2="-20" stroke={color} strokeWidth="1" />
            <line x1="0" y1="0" x2="20" y2="0" stroke={color} strokeWidth="1" />
            <line x1="0" y1="0" x2="0" y2="20" stroke={color} strokeWidth="1" />
            <line x1="0" y1="0" x2="-20" y2="0" stroke={color} strokeWidth="1" />
          </g>
        </g>
      </svg>
    </div>
  );
};

export default WindLogo;

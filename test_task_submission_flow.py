#!/usr/bin/env python3
"""
测试任务提交流程修复效果
"""

import requests
import json
from datetime import datetime

def test_task_submission():
    """测试任务提交流程"""
    print("🧪 测试任务提交流程修复效果")
    print("="*60)
    
    # 测试数据
    test_email = "<EMAIL>"
    fake_token = "Bearer fake_token_for_testing"
    
    print(f"📧 测试邮箱: {test_email}")
    print(f"🔑 测试Token: {fake_token}")
    print()
    
    # 1. 测试生产服务器的任务提交API
    print("1️⃣ 测试生产服务器任务提交 (aug8.xyz/api/submit):")
    try:
        response = requests.post(
            'https://aug8.xyz/api/submit',
            json={'email': test_email},
            headers={'Authorization': fake_token},
            timeout=15
        )
        
        print(f"   📊 状态码: {response.status_code}")
        
        if response.status_code == 401:
            print("   ⚠️ 认证失败（预期的）")
            data = response.json()
            print(f"   📄 响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        elif response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        elif response.status_code == 503:
            data = response.json()
            print(f"   ❌ 服务不可用: {json.dumps(data, indent=2, ensure_ascii=False)}")
            print("   💡 这可能意味着autoback.py没有运行或无法连接")
        else:
            print(f"   ❓ 其他状态: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    print()
    
    # 2. 测试autoback.py的任务提交API
    print("2️⃣ 测试autoback.py任务提交 (localhost:5002/api/submit):")
    try:
        response = requests.post(
            'http://localhost:5002/api/submit',
            json={'email': test_email},
            headers={'Authorization': fake_token},
            timeout=15
        )
        
        print(f"   📊 状态码: {response.status_code}")
        
        if response.status_code == 401:
            print("   ⚠️ 认证失败（预期的）")
            data = response.json()
            print(f"   📄 响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        elif response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   ❓ 其他状态: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("   ❌ 连接失败: autoback.py可能没有运行")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    print()
    
    # 3. 检查服务状态
    print("3️⃣ 检查服务状态:")
    
    # 检查生产服务器
    try:
        response = requests.get('https://aug8.xyz/api/test', timeout=10)
        if response.status_code == 200:
            print("   ✅ 生产服务器 (aug8.xyz) 运行正常")
        else:
            print(f"   ❌ 生产服务器状态异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 生产服务器连接失败: {e}")
    
    # 检查autoback.py
    try:
        response = requests.get('http://localhost:5002/api/test', timeout=5)
        if response.status_code == 200:
            print("   ✅ autoback.py (localhost:5002) 运行正常")
        else:
            print(f"   ❌ autoback.py状态异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ autoback.py连接失败: {e}")

def analyze_flow():
    """分析修复后的流程"""
    print("\n" + "="*60)
    print("🔧 修复后的任务提交流程:")
    print("="*60)
    
    print("1. 前端提交任务 → production_server.py (/api/submit)")
    print("2. production_server.py 验证用户认证")
    print("3. production_server.py 转发任务 → autoback.py (/api/submit)")
    print("4. autoback.py 创建任务记录到SQLite")
    print("5. autoback.py 返回任务ID和队列位置")
    print("6. production_server.py 转发响应给前端")
    print("7. 前端显示任务进度")
    
    print("\n🎯 修复要点:")
    print("- ✅ production_server.py 不再直接操作SQLite")
    print("- ✅ 所有SQLite操作由autoback.py负责")
    print("- ✅ production_server.py 只负责认证和转发")
    print("- ✅ autoback.py 的 /api/submit 路由已重新启用")
    
    print("\n⚠️ 注意事项:")
    print("- autoback.py 必须运行在 localhost:5002")
    print("- production_server.py 必须能连接到 autoback.py")
    print("- 认证token必须有效才能成功提交")

def main():
    """主函数"""
    print("🔧 任务提交流程修复验证")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_task_submission()
    analyze_flow()
    
    print("\n" + "="*60)
    print("💡 如果看到 '服务不可用' 错误，请确保:")
    print("1. autoback.py 正在运行")
    print("2. autoback.py 监听在 localhost:5002")
    print("3. 防火墙没有阻止本地连接")
    print("4. 使用有效的认证token进行测试")
    print("="*60)

if __name__ == "__main__":
    main()

"""
用户认证管理模块
处理用户注册、登录、权限验证等功能
"""
import bcrypt
import jwt
import json
import mysql.connector
from mysql.connector import Error
from datetime import datetime, timedelta
import logging
import time
import threading

class AuthManager:
    def __init__(self, main_db_config, jihuoma_db_config, jwt_secret="your-secret-key"):
        self.main_db_config = main_db_config
        self.jihuoma_db_config = jihuoma_db_config
        self.jwt_secret = jwt_secret
        self.logger = logging.getLogger(__name__)

        # 移除连接池，使用直连方式
        self.logger.info("✅ 认证管理器初始化完成 (直连模式)")



    def get_main_db_connection(self, timeout=10):
        """直接创建数据库连接"""
        try:
            # 直连配置
            connection_config = {
                'host': self.main_db_config['host'],
                'database': self.main_db_config['database'],
                'user': self.main_db_config['user'],
                'password': self.main_db_config['password'],
                'charset': 'utf8mb4',
                'autocommit': True,
                'connection_timeout': timeout,
                'use_unicode': True,
                'raise_on_warnings': False
            }

            connection = mysql.connector.connect(**connection_config)
            self.logger.debug(f"✅ 数据库直连成功")
            return connection

        except Exception as e:
            self.logger.error(f"❌ 数据库连接失败: {e}")
            return None



    def get_jihuoma_db_connection(self, timeout=10):
        """获取激活码数据库连接（直接连接，无连接池）"""
        start_time = time.time()

        try:
            # 激活码数据库使用频率很低，直接连接即可
            config = self.jihuoma_db_config.copy()
            config.update({
                'connection_timeout': timeout,
                'autocommit': True,
                'charset': 'utf8mb4',
                'use_unicode': True
            })

            connection = mysql.connector.connect(**config)

            if connection.is_connected():
                elapsed = time.time() - start_time
                self.logger.debug(f"✅ 激活码数据库连接成功 ({elapsed:.3f}s)")
                return connection
            else:
                raise Error("激活码数据库连接失败")

        except Exception as e:
            elapsed = time.time() - start_time
            self.logger.error(f"❌ 激活码数据库连接失败 ({elapsed:.3f}s): {e}")
            return None

    def hash_password(self, password):
        """密码加密"""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

    def verify_password(self, password, hashed):
        """密码验证"""
        # 确保hashed是bytes类型
        if isinstance(hashed, str):
            hashed = hashed.encode('utf-8')
        return bcrypt.checkpw(password.encode('utf-8'), hashed)

    def generate_jwt_token(self, user_id, username):
        """生成JWT令牌"""
        payload = {
            'user_id': user_id,
            'username': username,
            'exp': datetime.utcnow() + timedelta(days=7)  # 7天过期
        }
        return jwt.encode(payload, self.jwt_secret, algorithm='HS256')

    def verify_jwt_token(self, token):
        """验证JWT令牌"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=['HS256'])
            self.logger.debug(f"JWT token验证成功，用户ID: {payload.get('user_id')}")
            return payload
        except jwt.ExpiredSignatureError:
            self.logger.debug("JWT token已过期")
            return None
        except jwt.InvalidTokenError as e:
            self.logger.debug(f"JWT token无效: {e}")
            return None
        except Exception as e:
            self.logger.error(f"JWT token验证异常: {e}")
            return None

    def get_system_config(self, config_key):
        """获取系统配置"""
        connection = self.get_main_db_connection()
        if not connection:
            return None

        try:
            cursor = connection.cursor()
            query = "SELECT config_value FROM systemconfig WHERE config_key = %s"
            cursor.execute(query, (config_key,))
            result = cursor.fetchone()
            return result[0] if result else None
        except Error as e:
            self.logger.error(f"获取系统配置失败: {e}")
            return None
        finally:
            cursor.close()
            connection.close()

    def register_user(self, username, password):
        """用户注册"""
        # 验证用户名格式 - 移除限制，只要求非空
        if not username or len(username.strip()) == 0:
            return {"success": False, "message": "用户名不能为空"}

        connection = self.get_main_db_connection()
        if not connection:
            return {"success": False, "message": "数据库连接失败"}

        try:
            cursor = connection.cursor()

            # 检查用户名是否已存在
            check_query = "SELECT COUNT(*) FROM users WHERE username = %s"
            cursor.execute(check_query, (username,))
            if cursor.fetchone()[0] > 0:
                return {"success": False, "message": "用户名已存在"}

            # 获取注册赠送次数
            gift_quota = self.get_system_config('register_gift_quota')
            gift_quota = int(gift_quota) if gift_quota else 3

            # 加密密码
            hashed_password = self.hash_password(password)

            # 创建用户
            insert_query = """
                INSERT INTO users (username, password, time_quota)
                VALUES (%s, %s, %s)
            """
            cursor.execute(insert_query, (username, hashed_password, gift_quota))
            connection.commit()

            self.logger.info(f"用户注册成功: {username}")
            return {
                "success": True,
                "message": f"注册成功！赠送 {gift_quota} 次免费使用机会",
                "gift_quota": gift_quota
            }

        except Error as e:
            connection.rollback()
            self.logger.error(f"用户注册失败: {e}")
            return {"success": False, "message": "注册失败，请稍后重试"}
        finally:
            cursor.close()
            connection.close()

    def login_user(self, username, password):
        """用户登录"""
        connection = self.get_main_db_connection()
        if not connection:
            return {"success": False, "message": "数据库连接失败"}

        try:
            cursor = connection.cursor()

            # 获取用户信息
            query = """
                SELECT id, username, password, time_quota, time_count, created_at, lastusetime
                FROM users WHERE username = %s
            """
            cursor.execute(query, (username,))
            user = cursor.fetchone()

            if not user:
                return {"success": False, "message": "用户名或密码错误"}

            # 验证密码
            if not self.verify_password(password, user[2]):
                return {"success": False, "message": "用户名或密码错误"}

            # 更新最后登录时间
            update_query = "UPDATE users SET lastusetime = CURRENT_TIMESTAMP WHERE id = %s"
            cursor.execute(update_query, (user[0],))
            connection.commit()

            # 生成JWT令牌
            token = self.generate_jwt_token(user[0], user[1])

            # 返回用户信息
            user_info = {
                "id": user[0],
                "username": user[1],
                "time_quota": user[3],
                "time_count": user[4],
                "created_at": user[5].strftime('%Y-%m-%d %H:%M:%S') if user[5] else None,
                "lastusetime": user[6].strftime('%Y-%m-%d %H:%M:%S') if user[6] else None
            }

            self.logger.info(f"用户登录成功: {username}")
            return {
                "success": True,
                "message": "登录成功",
                "token": token,
                "user": user_info
            }

        except Error as e:
            self.logger.error(f"用户登录失败: {e}")
            return {"success": False, "message": "登录失败，请稍后重试"}
        finally:
            cursor.close()
            connection.close()

    def get_user_info(self, user_id):
        """获取用户信息"""
        connection = self.get_main_db_connection()
        if not connection:
            self.logger.error("获取用户信息失败: 数据库连接失败")
            return None

        try:
            cursor = connection.cursor()
            query = """
                SELECT id, username, time_quota, time_count, created_at, lastusetime
                FROM users WHERE id = %s
            """
            cursor.execute(query, (user_id,))
            user = cursor.fetchone()

            if user:
                user_info = {
                    "id": user[0],
                    "username": user[1],
                    "time_quota": user[2],
                    "time_count": user[3],
                    "created_at": user[4].strftime('%Y-%m-%d %H:%M:%S') if user[4] else None,
                    "lastusetime": user[5].strftime('%Y-%m-%d %H:%M:%S') if user[5] else None
                }
                self.logger.debug(f"获取用户信息成功: {user_info['username']} (ID: {user_info['id']})")
                return user_info
            else:
                self.logger.debug(f"用户不存在: user_id={user_id}")
                return None

        except Error as e:
            self.logger.error(f"获取用户信息失败: {e}")
            return None
        finally:
            cursor.close()
            connection.close()

    def check_user_quota(self, user_id):
        """检查用户剩余次数"""
        connection = self.get_main_db_connection()
        if not connection:
            return False

        try:
            cursor = connection.cursor()
            query = "SELECT time_quota FROM users WHERE id = %s"
            cursor.execute(query, (user_id,))
            result = cursor.fetchone()
            return result[0] > 0 if result else False

        except Error as e:
            self.logger.error(f"检查用户次数失败: {e}")
            return False
        finally:
            cursor.close()
            connection.close()

    def consume_user_quota(self, user_id):
        """消费用户次数（任务成功后调用）"""
        connection = self.get_main_db_connection()
        if not connection:
            return False

        try:
            cursor = connection.cursor()
            query = """
                UPDATE users
                SET time_quota = time_quota - 1,
                    time_count = time_count + 1,
                    lastusetime = CURRENT_TIMESTAMP
                WHERE id = %s AND time_quota > 0
            """
            cursor.execute(query, (user_id,))
            connection.commit()

            # 检查是否成功更新
            if cursor.rowcount > 0:
                self.logger.info(f"用户 {user_id} 次数消费成功")
                return True
            else:
                self.logger.warning(f"用户 {user_id} 次数不足或不存在")
                return False

        except Error as e:
            connection.rollback()
            self.logger.error(f"消费用户次数失败: {e}")
            return False
        finally:
            cursor.close()
            connection.close()

    def recharge_with_activation_code(self, user_id, activation_code):
        """使用激活码充值"""
        main_conn = None
        jihuoma_conn = None

        try:
            # 获取数据库连接
            main_conn = self.get_main_db_connection()
            jihuoma_conn = self.get_jihuoma_db_connection()

            if not main_conn or not jihuoma_conn:
                return {"success": False, "message": "数据库连接失败"}

            # 开始事务
            main_conn.start_transaction()
            jihuoma_conn.start_transaction()

            # 1. 验证激活码是否存在且未使用，并且remark必须是augactive
            jihuoma_cursor = jihuoma_conn.cursor()
            check_query = """
                SELECT code, use_times, is_used
                FROM activation_codes
                WHERE code = %s AND is_used = 0 AND remark = 'augactive'
            """
            jihuoma_cursor.execute(check_query, (activation_code,))
            code_info = jihuoma_cursor.fetchone()

            if not code_info:
                return {"success": False, "message": "激活码不存在、已被使用或不适用于此产品"}

            use_times = code_info[1]

            # 2. 检查用户是否已使用过此激活码
            main_cursor = main_conn.cursor()
            user_query = "SELECT activecode FROM users WHERE id = %s"
            main_cursor.execute(user_query, (user_id,))
            user_result = main_cursor.fetchone()

            if user_result and user_result[0]:
                used_codes = json.loads(user_result[0]) if user_result[0] else []
                if activation_code in used_codes:
                    return {"success": False, "message": "您已使用过此激活码"}

            # 3. 更新用户次数和激活码记录
            update_user_query = """
                UPDATE users
                SET time_quota = time_quota + %s,
                    activecode = JSON_ARRAY_APPEND(IFNULL(activecode, '[]'), '$', %s)
                WHERE id = %s
            """
            main_cursor.execute(update_user_query, (use_times, activation_code, user_id))

            # 4. 标记激活码已使用并记录使用时间
            update_code_query = "UPDATE activation_codes SET is_used = 1, used_at = NOW() WHERE code = %s"
            jihuoma_cursor.execute(update_code_query, (activation_code,))

            # 提交事务
            main_conn.commit()
            jihuoma_conn.commit()

            self.logger.info(f"用户 {user_id} 使用激活码 {activation_code} 充值成功，获得 {use_times} 次")
            return {
                "success": True,
                "message": f"充值成功！获得 {use_times} 次使用机会",
                "added_quota": use_times
            }

        except Error as e:
            # 回滚事务
            if main_conn:
                main_conn.rollback()
            if jihuoma_conn:
                jihuoma_conn.rollback()

            self.logger.error(f"激活码充值失败: {e}")
            return {"success": False, "message": "充值失败，请稍后重试"}

        finally:
            # 关闭连接
            if main_conn:
                if 'main_cursor' in locals():
                    main_cursor.close()
                main_conn.close()
            if jihuoma_conn:
                if 'jihuoma_cursor' in locals():
                    jihuoma_cursor.close()
                jihuoma_conn.close()

    def get_activation_code_url(self):
        """获取激活码购买链接"""
        return self.get_system_config('activation_code_url') or "https://example.com/buy-code"

    def require_auth(self, token):
        """权限验证辅助函数 - 改进错误处理"""
        try:
            if not token:
                self.logger.debug("认证失败: 没有提供token")
                return None

            # 移除Bearer前缀
            if token.startswith('Bearer '):
                token = token[7:]

            payload = self.verify_jwt_token(token)
            if not payload:
                self.logger.debug("认证失败: JWT token验证失败")
                return None

            # 获取最新用户信息，添加超时保护
            try:
                user_info = self.get_user_info(payload['user_id'])
                if not user_info:
                    self.logger.debug(f"认证失败: 无法获取用户信息，user_id={payload.get('user_id')}")
                    return None

                self.logger.debug(f"认证成功: 用户 {user_info['username']} (ID: {user_info['id']})")
                return user_info

            except Exception as db_error:
                # 数据库连接问题时，记录详细错误但不抛出异常
                error_msg = str(db_error)
                if 'MySQL Connection not available' in error_msg:
                    self.logger.warning("认证失败: MySQL连接不可用，可能是网络问题")
                elif 'timeout' in error_msg.lower():
                    self.logger.warning("认证失败: 数据库连接超时")
                else:
                    self.logger.warning(f"认证失败: 数据库错误 - {error_msg}")
                return None

        except Exception as e:
            self.logger.error(f"认证过程异常: {e}")
            return None


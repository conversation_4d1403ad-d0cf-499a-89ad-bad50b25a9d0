"""
配置管理模块
负责浏览器配置文件的保存、加载和管理
"""
import os
import json
import time
import random
import socket
from typing import Dict, List, Optional


class ConfigManager:
    """配置文件管理器"""

    def __init__(self, config_dir: str = None):
        """初始化配置管理器"""
        if config_dir is None:
            # 使用程序所在目录作为配置目录
            script_dir = os.path.dirname(os.path.abspath(__file__))
            config_dir = os.path.join(script_dir, "browser_profiles")

        self.config_dir = config_dir
        self.profiles_file = os.path.join(config_dir, "profiles.json")
        self.old_profiles_dir = os.path.join(config_dir, "old")

        # 确保配置目录存在
        os.makedirs(config_dir, exist_ok=True)

        # 注意：不再自动创建old目录，只有在需要时才创建

        # 加载现有配置
        self.profiles = self._load_profiles()

    def _load_profiles(self) -> Dict:
        """加载配置文件"""
        if os.path.exists(self.profiles_file):
            try:
                with open(self.profiles_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                return {}
        return {}

    def _save_profiles(self) -> bool:
        """保存配置文件"""
        try:
            with open(self.profiles_file, 'w', encoding='utf-8') as f:
                json.dump(self.profiles, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False

    def create_profile(self, name: str) -> Dict:
        """创建新的浏览器配置文件（简化版本：只保存用户数据+finger插件）"""
        if name in self.profiles:
            raise ValueError(f"配置文件 '{name}' 已存在")

        # 创建用户数据存储目录
        profile_path = os.path.join(self.config_dir, name)
        os.makedirs(profile_path, exist_ok=True)
        print(f"✅ 创建用户数据目录: {profile_path}")

        # 检查finger插件状态
        finger_plugin_path = os.path.join(os.getcwd(), "finger")
        finger_plugin_exists = os.path.exists(finger_plugin_path) and os.path.exists(os.path.join(finger_plugin_path, "manifest.json"))

        # 生成简化配置信息
        profile = {
            "name": name,
            "path": profile_path,
            "debug_port": self._find_free_port(),
            "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            "last_used": None,
            "proxy": None,
            "finger_plugin_enabled": finger_plugin_exists,
            "storage_type": "instance"
        }

        print(f"✅ 配置 '{name}' 创建完成")
        print(f"📁 用户数据路径: {profile_path}")
        print(f"🛡️ Finger插件: {'已启用' if finger_plugin_exists else '未启用'}")

        # 保存配置
        self.profiles[name] = profile
        self._save_profiles()

        return profile



    def _has_portable_chrome(self) -> bool:
        """检查是否有便携版Chrome"""
        portable_paths = [
            os.path.join(os.path.dirname(__file__), "browsers", "chrome_config.json"),
            os.path.join(os.getcwd(), "browsers", "chrome_config.json"),
            "browsers/chrome_config.json"
        ]

        for path in portable_paths:
            if os.path.exists(path):
                return True
        return False

    def _get_portable_user_data_dir(self) -> Optional[str]:
        """获取便携版Chrome的用户数据目录"""
        config_paths = [
            os.path.join(os.path.dirname(__file__), "browsers", "chrome_config.json"),
            os.path.join(os.getcwd(), "browsers", "chrome_config.json")
        ]

        for config_path in config_paths:
            if os.path.exists(config_path):
                try:
                    import json
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        user_data_dir = config.get('user_data_dir')
                        if user_data_dir and os.path.exists(user_data_dir):
                            return user_data_dir
                except Exception as e:
                    print(f"⚠️ 读取便携版配置失败: {e}")

        return None

    def create_profile_with_portable_base(self, name: str, use_portable_base: bool = False,
                                         enable_stealth: bool = False, fingerprint: Dict = None,
                                         enable_native_simulation: bool = False) -> Dict:
        """创建新的浏览器配置文件（简化版本：只保存用户数据+finger插件）"""
        if name in self.profiles:
            raise ValueError(f"配置文件 '{name}' 已存在")

        # 创建用户数据存储目录
        profile_path = os.path.join(self.config_dir, name)
        os.makedirs(profile_path, exist_ok=True)
        print(f"✅ 创建用户数据目录: {profile_path}")

        # 检查finger插件状态
        finger_plugin_path = os.path.join(os.getcwd(), "finger")
        finger_plugin_exists = os.path.exists(finger_plugin_path) and os.path.exists(os.path.join(finger_plugin_path, "manifest.json"))

        if finger_plugin_exists:
            print(f"🛡️ 检测到finger插件，将自动启用")
        else:
            print(f"⚠️ 未检测到finger插件")

        # 生成简化配置信息（只保存必要信息）
        profile = {
            "name": name,
            "path": profile_path,
            "debug_port": self._find_free_port(),
            "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            "last_used": None,
            "proxy": None,
            "finger_plugin_enabled": finger_plugin_exists,
            "storage_type": "instance",
            "chrome_source": "134_folder"
        }

        print(f"✅ 配置 '{name}' 创建完成")
        print(f"📁 用户数据路径: {profile_path}")
        print(f"🛡️ Finger插件: {'已启用' if finger_plugin_exists else '未启用'}")

        # 保存配置
        self.profiles[name] = profile
        self._save_profiles()

        return profile

    def _create_native_simulation(self, name: str, profile_path: str):
        """为配置创建原生环境模拟"""
        try:
            # 导入原生环境模拟器
            from native_environment_simulator import NativeEnvironmentSimulator

            # 创建模拟器实例
            simulator = NativeEnvironmentSimulator()

            # 创建模拟环境
            simulated_path = simulator.create_simulated_profile(name, os.path.dirname(profile_path))

            print(f"✅ 原生环境模拟创建成功: {simulated_path}")

        except ImportError:
            print("⚠️ 原生环境模拟器不可用")
            raise
        except Exception as e:
            print(f"❌ 创建原生环境模拟失败: {e}")
            raise

    def get_profile(self, name: str) -> Optional[Dict]:
        """获取指定配置文件"""
        return self.profiles.get(name)

    def get_all_profiles(self) -> Dict:
        """获取所有配置文件"""
        return self.profiles.copy()

    def reload_profiles(self) -> bool:
        """重新加载配置文件（从磁盘重新读取）"""
        try:
            self.profiles = self._load_profiles()
            return True
        except Exception as e:
            print(f"重新加载配置文件失败: {e}")
            return False

    def delete_profile(self, name: str) -> bool:
        """删除配置文件"""
        if name not in self.profiles:
            return False

        # 删除配置文件目录
        profile_path = self.profiles[name]["path"]
        try:
            import shutil
            if os.path.exists(profile_path):
                shutil.rmtree(profile_path)
        except Exception as e:
            print(f"删除配置文件目录失败: {e}")

        # 从配置中移除
        del self.profiles[name]
        self._save_profiles()

        return True

    def update_profile(self, name: str, updates: Dict) -> bool:
        """更新配置文件"""
        if name not in self.profiles:
            return False

        self.profiles[name].update(updates)
        return self._save_profiles()



    def _find_free_port(self) -> int:
        """查找可用的网络端口"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            return s.getsockname()[1]





    def move_profile_to_old(self, name: str) -> bool:
        """将配置移动到old文件夹 - 仅用于手动操作，自动化流程不再使用此方法"""
        try:
            if name not in self.profiles:
                print(f"配置 {name} 不存在")
                return False

            profile_config = self.profiles[name]
            old_profile_path = profile_config['path']

            if not os.path.exists(old_profile_path):
                print(f"配置文件夹不存在: {old_profile_path}")
                return False

            # 移动到old文件夹（只有在需要时才创建old目录）
            os.makedirs(self.old_profiles_dir, exist_ok=True)
            new_profile_path = os.path.join(self.old_profiles_dir, name)

            # 如果目标已存在，先删除
            if os.path.exists(new_profile_path):
                shutil.rmtree(new_profile_path)

            shutil.move(old_profile_path, new_profile_path)

            # 从活跃配置中移除
            del self.profiles[name]
            self._save_profiles()

            print(f"✅ 配置 {name} 已移动到old文件夹")
            return True

        except Exception as e:
            print(f"❌ 移动配置失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def get_next_available_profile(self) -> str:
        """获取下一个可用的配置"""
        if not self.profiles:
            return None

        # 返回第一个可用的配置
        return list(self.profiles.keys())[0]

    def get_profile_stats(self) -> dict:
        """获取配置统计信息"""
        try:
            active_count = len(self.profiles)

            # 统计old文件夹中的配置（如果存在的话）
            old_count = 0
            if os.path.exists(self.old_profiles_dir):
                try:
                    old_items = [item for item in os.listdir(self.old_profiles_dir)
                               if os.path.isdir(os.path.join(self.old_profiles_dir, item))]
                    old_count = len(old_items)
                except Exception:
                    old_count = 0

            return {
                'active_count': active_count,
                'used_count': old_count,
                'total_count': active_count + old_count,
                'active_profiles': list(self.profiles.keys())
            }

        except Exception as e:
            print(f"获取配置统计失败: {e}")
            return {
                'active_count': 0,
                'used_count': 0,
                'total_count': 0,
                'active_profiles': []
            }

    def restore_profile_from_old(self, name: str) -> bool:
        """从old文件夹恢复配置"""
        try:
            # 检查old文件夹是否存在
            if not os.path.exists(self.old_profiles_dir):
                print(f"old文件夹不存在")
                return False

            old_profile_path = os.path.join(self.old_profiles_dir, name)

            if not os.path.exists(old_profile_path):
                print(f"old文件夹中不存在配置: {name}")
                return False

            # 移动回主目录
            new_profile_path = os.path.join(self.config_dir, name)

            # 如果目标已存在，先删除
            if os.path.exists(new_profile_path):
                shutil.rmtree(new_profile_path)

            shutil.move(old_profile_path, new_profile_path)

            # 添加到活跃配置
            self.profiles[name] = {
                'name': name,
                'path': new_profile_path,
                'port': self._find_free_port(),
                'created_at': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            self._save_profiles()

            print(f"✅ 配置 {name} 已从old文件夹恢复")
            return True

        except Exception as e:
            print(f"❌ 恢复配置失败: {e}")
            return False

    def sync_profiles_with_folders(self) -> Dict:
        """根据browser_profiles文件夹同步profiles.json配置"""
        try:
            # 获取browser_profiles文件夹中的所有文件夹名称
            existing_folders = set()
            if os.path.exists(self.config_dir):
                for item in os.listdir(self.config_dir):
                    item_path = os.path.join(self.config_dir, item)
                    # 只考虑文件夹，排除profiles.json、profiles2.json和old文件夹
                    if (os.path.isdir(item_path) and
                        item != "old" and
                        item != "profiles.json" and
                        item != "profiles2.json" and
                        not item.startswith('.')):
                        existing_folders.add(item)

            # 获取当前profiles.json中的配置名称
            current_profiles = set(self.profiles.keys())

            # 找出需要删除的配置（在profiles.json中但文件夹不存在）
            profiles_to_remove = current_profiles - existing_folders

            sync_result = {
                'removed': [],
                'added': [],
                'kept': [],
                'total_before': len(current_profiles),
                'total_after': 0
            }

            # 删除不存在文件夹的配置
            for profile_name in profiles_to_remove:
                if profile_name in self.profiles:
                    del self.profiles[profile_name]
                    sync_result['removed'].append(profile_name)
                    print(f"🗑️ 删除配置: {profile_name} (文件夹不存在)")

            # 检查是否存在profiles2.json，如果存在则合并其内容
            profiles2_file = os.path.join(self.config_dir, "profiles2.json")
            if os.path.exists(profiles2_file):
                try:
                    with open(profiles2_file, 'r', encoding='utf-8') as f:
                        profiles2_data = json.load(f)

                    # 将profiles2.json中的配置添加到当前配置中（如果不存在的话）
                    for profile_name, profile_data in profiles2_data.items():
                        if profile_name not in self.profiles:
                            self.profiles[profile_name] = profile_data
                            sync_result['added'].append(profile_name)
                            print(f"➕ 从profiles2.json添加配置: {profile_name}")

                    print(f"✅ 已合并profiles2.json中的 {len([p for p in profiles2_data.keys() if p not in current_profiles])} 个配置")

                except Exception as e:
                    print(f"⚠️ 读取profiles2.json失败: {e}")

            # 记录保留的配置
            sync_result['kept'] = list(existing_folders & current_profiles)
            sync_result['total_after'] = len(self.profiles)

            # 保存更新后的配置
            if self._save_profiles():
                print(f"✅ 配置同步完成:")
                print(f"   删除: {len(sync_result['removed'])} 个")
                if sync_result['added']:
                    print(f"   从profiles2.json添加: {len(sync_result['added'])} 个")
                print(f"   保留: {len(sync_result['kept'])} 个")
                print(f"   总计: {sync_result['total_before']} -> {sync_result['total_after']}")
                return sync_result
            else:
                print("❌ 保存配置文件失败")
                return None

        except Exception as e:
            print(f"❌ 同步配置失败: {e}")
            import traceback
            traceback.print_exc()
            return None

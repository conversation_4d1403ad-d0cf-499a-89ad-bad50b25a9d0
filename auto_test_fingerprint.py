"""
自动化指纹测试脚本
自动运行指纹测试并生成报告
"""
import time
import json
import os
from datetime import datetime
from stealth_browser_manager import StealthBrowserManager

class AutoFingerprintTester:
    """自动化指纹测试器"""
    
    def __init__(self):
        self.manager = StealthBrowserManager()
        self.test_results = []
        
        # 测试配置
        self.test_profiles = ['auto_test_1', 'auto_test_2', 'auto_test_3']
        self.test_url = 'https://browserleaks.com/javascript'
        
    def run_auto_test(self):
        """运行自动化测试"""
        print("🤖 启动自动化指纹测试")
        print("=" * 50)
        
        print(f"📋 测试配置: {len(self.test_profiles)} 个")
        print(f"🌐 测试网站: {self.test_url}")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 逐个测试配置
        for i, profile in enumerate(self.test_profiles, 1):
            print(f"\n🧪 测试 {i}/{len(self.test_profiles)}: {profile}")
            print("-" * 30)
            
            result = self._test_single_profile(profile)
            if result:
                self.test_results.append(result)
                print(f"✅ {profile} 测试完成")
            else:
                print(f"❌ {profile} 测试失败")
            
            # 测试间隔
            if i < len(self.test_profiles):
                print("⏳ 等待 3 秒...")
                time.sleep(3)
        
        # 生成测试报告
        self._generate_report()
        
    def _test_single_profile(self, profile_name):
        """测试单个配置"""
        try:
            # 启动浏览器
            driver = self.manager.launch_stealth_browser(profile_name)
            if not driver:
                return None
            
            # 访问测试网站
            driver.get(self.test_url)
            time.sleep(5)  # 等待页面加载
            
            # 收集指纹信息
            fingerprint = {
                'profile': profile_name,
                'timestamp': datetime.now().isoformat(),
                'url': self.test_url,
                'title': driver.title,
                'user_agent': driver.execute_script('return navigator.userAgent;'),
                'screen_resolution': driver.execute_script('return [screen.width, screen.height];'),
                'language': driver.execute_script('return navigator.language;'),
                'languages': driver.execute_script('return navigator.languages;'),
                'platform': driver.execute_script('return navigator.platform;'),
                'hardware_concurrency': driver.execute_script('return navigator.hardwareConcurrency;'),
                'device_memory': driver.execute_script('return navigator.deviceMemory;'),
                'color_depth': driver.execute_script('return screen.colorDepth;'),
                'pixel_ratio': driver.execute_script('return window.devicePixelRatio;'),
                'timezone': driver.execute_script('return Intl.DateTimeFormat().resolvedOptions().timeZone;'),
                'webgl_vendor': driver.execute_script('return (function(){var gl=document.createElement("canvas").getContext("webgl");return gl?gl.getParameter(gl.VENDOR):"N/A";})();'),
                'webgl_renderer': driver.execute_script('return (function(){var gl=document.createElement("canvas").getContext("webgl");return gl?gl.getParameter(gl.RENDERER):"N/A";})();')
            }
            
            # 显示关键信息
            print(f"🌐 User-Agent: {fingerprint['user_agent'][:60]}...")
            print(f"📱 分辨率: {fingerprint['screen_resolution']}")
            print(f"🗣️ 语言: {fingerprint['language']}")
            print(f"💻 平台: {fingerprint['platform']}")
            print(f"🔧 CPU: {fingerprint['hardware_concurrency']} 核")
            print(f"💾 内存: {fingerprint['device_memory']}GB")
            
            return fingerprint
            
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            return None
        finally:
            # 关闭浏览器
            self.manager.close_browser(profile_name)
    
    def _generate_report(self):
        """生成测试报告"""
        if not self.test_results:
            print("\n❌ 没有测试结果，无法生成报告")
            return
        
        print(f"\n📊 测试报告")
        print("=" * 50)
        
        # 基本统计
        total_tests = len(self.test_results)
        print(f"📋 总测试数: {total_tests}")
        print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 分析指纹差异
        print(f"\n🔍 指纹差异分析:")
        print("-" * 30)
        
        fingerprint_keys = [
            'user_agent', 'screen_resolution', 'language', 'platform',
            'hardware_concurrency', 'device_memory', 'color_depth', 
            'pixel_ratio', 'webgl_vendor', 'webgl_renderer'
        ]
        
        different_count = 0
        
        for key in fingerprint_keys:
            values = [str(result.get(key, 'N/A')) for result in self.test_results]
            unique_values = set(values)
            
            if len(unique_values) == 1:
                print(f"❌ {key}: 相同")
            else:
                print(f"✅ {key}: 不同 ({len(unique_values)}个变体)")
                different_count += 1
        
        # 计算差异率
        difference_rate = (different_count / len(fingerprint_keys)) * 100
        print(f"\n📈 指纹差异率: {difference_rate:.1f}% ({different_count}/{len(fingerprint_keys)})")
        
        # 评估效果
        if difference_rate >= 80:
            print("🎉 指纹伪装效果: 优秀 ⭐⭐⭐⭐⭐")
        elif difference_rate >= 60:
            print("👍 指纹伪装效果: 良好 ⭐⭐⭐⭐")
        elif difference_rate >= 40:
            print("⚠️ 指纹伪装效果: 一般 ⭐⭐⭐")
        elif difference_rate >= 20:
            print("😐 指纹伪装效果: 较差 ⭐⭐")
        else:
            print("❌ 指纹伪装效果: 很差 ⭐")
        
        # 保存详细报告
        self._save_detailed_report()
        
        print(f"\n🎯 测试完成！")
        print(f"💡 建议: 如果差异率低于60%，可能需要进一步优化指纹伪装")
    
    def _save_detailed_report(self):
        """保存详细报告"""
        try:
            # 创建报告目录
            os.makedirs('test_reports', exist_ok=True)
            
            # 生成报告文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = f'test_reports/fingerprint_test_{timestamp}.json'
            
            # 保存报告
            report_data = {
                'test_info': {
                    'timestamp': datetime.now().isoformat(),
                    'total_tests': len(self.test_results),
                    'test_url': self.test_url,
                    'profiles': self.test_profiles
                },
                'results': self.test_results
            }
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 详细报告已保存: {report_file}")
            
        except Exception as e:
            print(f"⚠️ 保存报告失败: {e}")

def main():
    """主函数"""
    print("🎭 自动化浏览器指纹测试")
    print("=" * 40)
    
    try:
        tester = AutoFingerprintTester()
        tester.run_auto_test()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

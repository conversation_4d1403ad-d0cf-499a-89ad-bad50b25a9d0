#!/usr/bin/env python3
"""
测试前端刷新状态功能
模拟前端点击"刷新状态"按钮的操作
"""

import requests
import time
import threading
from datetime import datetime

def test_system_status():
    """测试系统状态API"""
    try:
        response = requests.get('https://aug8.xyz/api/system/status', timeout=15)
        return {
            'success': response.status_code == 200,
            'status_code': response.status_code,
            'response_time': response.elapsed.total_seconds(),
            'data': response.json() if response.status_code == 200 else None
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'response_time': None
        }

def test_queue_list():
    """测试队列列表API（需要认证）"""
    try:
        # 使用测试token（这会失败，但能测试连接）
        headers = {'Authorization': 'Bearer test_token'}
        response = requests.get('https://aug8.xyz/api/queue/list', 
                              headers=headers, timeout=15)
        return {
            'success': response.status_code in [200, 401],  # 401是预期的
            'status_code': response.status_code,
            'response_time': response.elapsed.total_seconds(),
            'data': response.json() if response.status_code == 200 else None
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'response_time': None
        }

def simulate_refresh_click():
    """模拟前端点击刷新状态按钮"""
    print(f"🔄 模拟刷新状态点击 - {datetime.now().strftime('%H:%M:%S')}")
    
    results = {}
    start_time = time.time()
    
    # 创建线程同时调用两个API（模拟前端行为）
    def call_system_status():
        results['system_status'] = test_system_status()
    
    def call_queue_list():
        results['queue_list'] = test_queue_list()
    
    # 同时启动两个请求
    thread1 = threading.Thread(target=call_system_status)
    thread2 = threading.Thread(target=call_queue_list)
    
    thread1.start()
    thread2.start()
    
    # 等待两个请求完成
    thread1.join()
    thread2.join()
    
    total_time = time.time() - start_time
    
    # 分析结果
    system_ok = results['system_status']['success']
    queue_ok = results['queue_list']['success']
    
    print(f"   📊 系统状态API: {'✅' if system_ok else '❌'} "
          f"({results['system_status'].get('response_time', 0):.3f}s)")
    print(f"   📊 队列列表API: {'✅' if queue_ok else '❌'} "
          f"({results['queue_list'].get('response_time', 0):.3f}s)")
    print(f"   ⏱️ 总耗时: {total_time:.3f}s")
    
    if not system_ok:
        print(f"   ❌ 系统状态错误: {results['system_status'].get('error', '未知')}")
    if not queue_ok:
        print(f"   ❌ 队列列表错误: {results['queue_list'].get('error', '未知')}")
    
    return system_ok and queue_ok

def stress_test_refresh(num_clicks=10, interval=2):
    """压力测试：连续多次刷新"""
    print(f"🧪 开始压力测试：{num_clicks}次刷新，间隔{interval}秒")
    print("=" * 60)
    
    success_count = 0
    total_count = 0
    
    for i in range(num_clicks):
        print(f"\n第 {i+1}/{num_clicks} 次刷新:")
        
        success = simulate_refresh_click()
        total_count += 1
        if success:
            success_count += 1
        
        if i < num_clicks - 1:  # 最后一次不需要等待
            time.sleep(interval)
    
    print("\n" + "=" * 60)
    print(f"📊 压力测试结果:")
    print(f"   ✅ 成功: {success_count}/{total_count}")
    print(f"   ❌ 失败: {total_count - success_count}/{total_count}")
    print(f"   📈 成功率: {(success_count/total_count)*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有刷新操作都成功！连接池问题已解决！")
    else:
        print("⚠️ 仍有部分刷新操作失败，可能需要进一步优化")

def rapid_fire_test(num_rapid=5):
    """快速连击测试：模拟用户快速点击刷新按钮"""
    print(f"⚡ 快速连击测试：{num_rapid}次快速刷新（无间隔）")
    print("=" * 60)
    
    results = []
    threads = []
    
    def single_refresh(index):
        print(f"🔄 快速刷新 #{index+1}")
        success = simulate_refresh_click()
        results.append(success)
    
    # 同时启动多个刷新操作
    start_time = time.time()
    for i in range(num_rapid):
        thread = threading.Thread(target=single_refresh, args=(i,))
        threads.append(thread)
        thread.start()
    
    # 等待所有操作完成
    for thread in threads:
        thread.join()
    
    total_time = time.time() - start_time
    success_count = sum(results)
    
    print("\n" + "=" * 60)
    print(f"⚡ 快速连击测试结果:")
    print(f"   ✅ 成功: {success_count}/{num_rapid}")
    print(f"   ❌ 失败: {num_rapid - success_count}/{num_rapid}")
    print(f"   ⏱️ 总耗时: {total_time:.3f}s")
    print(f"   📈 成功率: {(success_count/num_rapid)*100:.1f}%")
    
    if success_count == num_rapid:
        print("🎉 快速连击测试通过！系统能够处理并发刷新！")
    else:
        print("⚠️ 快速连击测试发现问题，系统可能无法处理高并发")

def main():
    """主函数"""
    print("🧪 前端刷新状态功能测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试目标: aug8.xyz")
    print("=" * 60)
    
    # 1. 单次刷新测试
    print("\n1️⃣ 单次刷新测试:")
    success = simulate_refresh_click()
    if success:
        print("✅ 单次刷新测试通过")
    else:
        print("❌ 单次刷新测试失败，停止后续测试")
        return
    
    time.sleep(3)
    
    # 2. 压力测试
    print("\n2️⃣ 压力测试:")
    stress_test_refresh(num_clicks=5, interval=1)
    
    time.sleep(5)
    
    # 3. 快速连击测试
    print("\n3️⃣ 快速连击测试:")
    rapid_fire_test(num_rapid=3)
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print("如果所有测试都通过，说明连接池耗尽问题已经解决！")
    print("如果仍有失败，可能需要进一步优化系统架构。")
    print("=" * 60)

if __name__ == "__main__":
    main()

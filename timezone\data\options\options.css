body {
  border: 0;
  padding: 0;
  margin: auto;
  font-size: 13px;
  max-width: 1200px;
  align-items: center;
  font-family: system-ui, sans-serif;
}

table {
  width: 100%;
  color: #333;
  margin: 0 auto;
  font-size: 13px;
  table-layout: fixed;
  border-spacing: 10px;
  font-family: system-ui, sans-serif;
}

table tr td {
  padding: 0;
  text-align: right;
  vertical-align: middle;
}

input[type='text'] {
  padding: 0;
  width: 100%;
  color: #333;
  height: 42px;
  outline: none;
  font-size: 14px;
  font-weight: 600;
  box-shadow: none;
  text-shadow: none;
  text-indent: 10px;
  font-family: monospace;
  border: solid 1px rgba(0,0,0,0.25);
}

input[type='button'] {
  margin: 0;
  padding: 0;
  width: 64px;
  height: 44px;
  outline: none;
  cursor: pointer;
  font-size: 21px;
  box-shadow: none;
  font-weight: 600;
  vertical-align: middle;
  font-family: monospace;
  transition: 300ms ease all;
  background: #fff !important;
  border: solid 1px rgba(0,0,0,0.25);
}

select {
  padding: 0;
  width: 100%;
  color: #333;
  height: 44px;
  outline: none;
  font-size: 14px;
  font-weight: 600;
  box-shadow: none;
  text-shadow: none;
  text-indent: 10px;
  font-family: monospace;
  border: solid 1px rgba(0,0,0,0.25);
}

.container {
  border: 0;
  padding: 0;
  margin: auto;
  box-sizing: border-box;
}

.select {
  height: 150px;
}

.title {
  padding: 0;
  height: 38px;
  text-align: left;
  text-indent: 5px;
  line-height: 18px;
}

.note {
  padding: 0;
  color: #555;
  height: 32px;
  text-indent: 5px;
  text-align: left;
}

.input table tbody .title {
  font-weight: 600;
}

.input .note:last-child,
.input .title:last-child {
  text-align: right;
}

.toolbar {
  top: 0;
  right: 0;
  z-index: 11;
  width: 42px;
  position: fixed;
}

.toolbar table {
  border-spacing: 0;
  table-layout: fixed;
}

.toolbar table tr td {
  padding: 0;
  width: 100%;
  height: 42px;
  font-size: 13px;
  cursor: pointer;
  user-select: none;
  text-align: center;
  transition: 300ms ease all;
  font-family: arial,sans-serif;
}

.toolbar table tr td:hover {
  background-color: rgba(0,0,0,0.1);
}

.toolbar table tr td svg {
  fill: #555;
  pointer-events: none;
  vertical-align: middle;
}

.logo {
  width: 100%;
  height: 128px;
  border-bottom: solid 1px rgba(0,0,0,0.1);
  background: url('../icons/128.png') no-repeat center center;
  background-size: 64px;
}

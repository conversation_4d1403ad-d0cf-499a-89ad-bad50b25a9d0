# ========================================
# Python 后端核心依赖
# ========================================

# Web框架
Flask==3.0.0

# 数据库连接
mysql-connector-python==8.2.0

# 浏览器自动化
selenium==4.15.2
webdriver-manager==4.0.1
selenium-stealth==1.0.6
fake-useragent==1.4.0

# 密码加密和JWT认证
bcrypt==4.1.2
PyJWT==2.8.0

# Excel文件处理
openpyxl==3.1.2

# 注意: 以下是Python内置模块，无需安装
# tkinter, sqlite3, os, sys, subprocess, platform
# time, threading, json, datetime, typing, logging

# ========================================
# 前端依赖 (Node.js/npm)
# ========================================
#
# 前端项目位于: email-submit-frontend/
#
# 安装前端依赖:
# cd email-submit-frontend
# npm install
#
# 主要依赖 (已在package.json中定义):
# - React 19.1.0 + TypeScript
# - Ant Design 5.26.2 (UI组件)
# - Axios 1.10.0 (HTTP客户端)
# - React Router 6.30.1 (路由)
# - Zustand 5.0.6 (状态管理)

# ========================================
# 快速安装指南
# ========================================

# 1. 环境要求:
#    - Python 3.8+
#    - Node.js 16+
#    - Google Chrome 浏览器

# 2. 安装步骤:
#    pip install -r requirements.txt
#    cd email-submit-frontend && npm install

# 3. 启动应用:
#    python autoback.py  (后端)
#    python main.py      (配置管理)

# 4. 前端构建:
#    cd email-submit-frontend
#    npm run build       (生产构建)
#    npm start           (开发模式)

# ========================================
# 系统要求
# ========================================

# 操作系统: Windows 10+, macOS 10.15+, Ubuntu 18.04+
# 内存: 4GB+ RAM (推荐 8GB+)
# 存储: 2GB+ 可用空间
# 网络: 稳定的互联网连接

# ========================================
# 可选依赖 (开发环境)
# ========================================

# 开发工具 (可选):
# requests==2.31.0          # HTTP请求库
# beautifulsoup4==4.12.2    # HTML解析
# pytest==7.4.3            # 测试框架
# black==23.11.0            # 代码格式化

# ========================================
# 项目信息
# ========================================
# 版本: v1.3.1
# 更新: 2025-06-30
# Python: 3.8+, Node.js: 16+

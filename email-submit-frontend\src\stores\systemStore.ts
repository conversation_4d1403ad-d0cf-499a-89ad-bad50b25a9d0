/**
 * 系统状态管理Store
 * 管理系统状态、可用配置数量等信息
 */
import React from 'react';
import { create } from 'zustand';
import { emailAPI, SystemStatus } from '../services/api';

interface SystemState {
  systemStatus: SystemStatus | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: number | null;
}

interface SystemActions {
  fetchSystemStatus: () => Promise<void>;
  clearError: () => void;
  startPolling: () => void;
  stopPolling: () => void;
}

interface SystemStore extends SystemState, SystemActions {}

// 轮询间隔（毫秒）
const POLLING_INTERVAL = 15000; // 增加到15秒，减少服务器压力
const MAX_CONSECUTIVE_FAILURES = 3; // 最大连续失败次数

let pollingTimer: NodeJS.Timeout | null = null;
let consecutiveFailures = 0;

export const useSystemStore = create<SystemStore>((set, get) => ({
  // 初始状态
  systemStatus: null,
  isLoading: false,
  error: null,
  lastUpdated: null,

  // 获取系统状态 - 改进错误处理和失败计数
  fetchSystemStatus: async () => {
    try {
      set({ isLoading: true, error: null });

      const status = await emailAPI.getSystemStatus();

      // 成功时重置失败计数
      consecutiveFailures = 0;

      set({
        systemStatus: status,
        isLoading: false,
        lastUpdated: Date.now()
      });

    } catch (error: any) {
      console.error('获取系统状态失败:', error);
      consecutiveFailures++;

      // 不要在轮询时显示错误，避免界面闪烁
      const { lastUpdated } = get();
      const isFirstLoad = !lastUpdated;

      // 如果连续失败次数过多，暂停轮询
      if (consecutiveFailures >= MAX_CONSECUTIVE_FAILURES) {
        console.warn(`连续失败${consecutiveFailures}次，暂停轮询30秒`);
        if (pollingTimer) {
          clearInterval(pollingTimer);
          pollingTimer = null;
        }

        // 30秒后重新开始轮询
        setTimeout(() => {
          consecutiveFailures = 0;
          get().startPolling();
        }, 30000);
      }

      set({
        error: isFirstLoad ? (error.message || '获取系统状态失败') : null,
        isLoading: false
      });
    }
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },

  // 开始轮询
  startPolling: () => {
    const { fetchSystemStatus } = get();

    // 立即获取一次
    fetchSystemStatus();

    // 停止现有轮询
    if (pollingTimer) {
      clearInterval(pollingTimer);
    }

    // 开始新的轮询
    pollingTimer = setInterval(() => {
      fetchSystemStatus();
    }, POLLING_INTERVAL);
  },

  // 停止轮询
  stopPolling: () => {
    if (pollingTimer) {
      clearInterval(pollingTimer);
      pollingTimer = null;
    }
  }
}));

// 导出hooks
export const useSystemState = () => {
  const { systemStatus, isLoading, error, lastUpdated } = useSystemStore();
  return { systemStatus, isLoading, error, lastUpdated };
};

export const useSystemActions = () => {
  const { fetchSystemStatus, clearError, startPolling, stopPolling } = useSystemStore();
  return { fetchSystemStatus, clearError, startPolling, stopPolling };
};

// 自动开始轮询的hook
export const useSystemPolling = () => {
  const { startPolling, stopPolling } = useSystemActions();

  // 组件挂载时开始轮询，卸载时停止
  React.useEffect(() => {
    startPolling();

    return () => {
      stopPolling();
    };
  }, [startPolling, stopPolling]);
};



﻿<!DOCTYPE html>
<html>
<head>

  <title>Download with IDM</title>

  <script>

    var agent = typeof browser == 'undefined' ? chrome : browser;
    var istop = window.self === window.top;
    var param = {};

    for (var item of location.search.slice(1).split('&'))
    {
        item = item.split('=',2);
        param[item[0]] = item[1];
    }

    if ((param.register || param.back) && istop)
    {
        history.length > 1 ? history.back() : agent.runtime.sendMessage([38]);
    }

    function onContentLoad(event)
    {
        if (param.register)
        {
            msgDownload.style.display = 'none';
            msgRegister.style.display = '';
        }
        else if (param.host)
        {
            strHostname.innerText = param.host;
            msgDownload.style.display = 'none';
            msgDnldFrom.style.display = '';
        }
        
        textThisYear.innerText = new Date().getFullYear();
    }

    document.addEventListener('DOMContentLoaded', onContentLoad);
    
  </script>
  
  <style>

    body { height: 100%; margin: 0; background-color: #FAFDFE; font-size: large; font-family: Arial, sans-serif; }
    #layoutWrapper { height: auto !important; min-height: 100%; margin: 0 auto -6em; }
    #layoutContent { margin: 0 5em; }
    #layoutFooter  { margin: 0 5em; text-align: center; font-style: italic; font-size: smaller; }
    #layoutFooter, #layoutSpacer { height: 6em; }

  </style>

</head>
<body>

  <div id="layoutWrapper">

    <div style="height: 61px; background-image: url(images/headBkgd.gif);">
      <img src="images/headTitle.gif" alt="Internet Download Manager" width="466" height="61"/>
    </div>

    <div id="layoutContent">

      <p><br/></p>
      <h3>
        <span id="msgDownload">File download has been transferred to Internet Download Manager.</span>
        <span id="msgDnldFrom" style="display: none">File download from <b id="strHostname">host</b> has been transferred to Internet Download Manager.</span>
        <span id="msgRegister" style="display: none">Internet Download Manager will now show IDM registration dialog.<br/><br/>
            Please verify your information on this dialog, and press OK button to register IDM.</span>
      </h3>

    </div>

    <div id="layoutSpacer"></div>

  </div>

  <div id="layoutFooter">

    <hr width="100%" color="darkBlue" size="1"/>

    <p>Internet Download Manager, Tonec FZE<br/>
      <img id="logoFooter" src="images/logoTonec.gif" width="68" height="22"
            style="float: left; display: block; position: absolute;"/><br/>
      &copy;&nbsp;1999-<span id="textThisYear">2025</span>. All rights reserved.</p>

  </div>

</body>
</html>

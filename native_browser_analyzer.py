"""
原生浏览器环境分析器
检测和分析系统原生Chrome/Edge环境，为模拟做准备
"""
import os
import json
import sqlite3
import shutil
import winreg
from pathlib import Path
import time
from datetime import datetime

class NativeBrowserAnalyzer:
    def __init__(self):
        self.chrome_paths = self._find_chrome_installations()
        self.edge_paths = self._find_edge_installations()
        self.system_info = self._collect_system_info()
        
    def _find_chrome_installations(self):
        """查找系统中的Chrome安装"""
        possible_paths = [
            os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe"),
            os.path.expandvars(r"%PROGRAMFILES%\Google\Chrome\Application\chrome.exe"),
            os.path.expandvars(r"%PROGRAMFILES(X86)%\Google\Chrome\Application\chrome.exe"),
        ]
        
        chrome_info = {}
        for path in possible_paths:
            if os.path.exists(path):
                version = self._get_chrome_version(path)
                user_data = os.path.dirname(path).replace("Application", "User Data")
                chrome_info[path] = {
                    'executable': path,
                    'version': version,
                    'user_data': user_data if os.path.exists(user_data) else None
                }
                
        return chrome_info
    
    def _find_edge_installations(self):
        """查找系统中的Edge安装"""
        possible_paths = [
            os.path.expandvars(r"%PROGRAMFILES(X86)%\Microsoft\Edge\Application\msedge.exe"),
            os.path.expandvars(r"%PROGRAMFILES%\Microsoft\Edge\Application\msedge.exe"),
        ]
        
        edge_info = {}
        for path in possible_paths:
            if os.path.exists(path):
                version = self._get_edge_version(path)
                user_data = os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data")
                edge_info[path] = {
                    'executable': path,
                    'version': version,
                    'user_data': user_data if os.path.exists(user_data) else None
                }
                
        return edge_info
    
    def _get_chrome_version(self, chrome_path):
        """获取Chrome版本"""
        try:
            import subprocess
            result = subprocess.run([chrome_path, '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                return result.stdout.strip().split()[-1]
        except:
            pass
        return "unknown"
    
    def _get_edge_version(self, edge_path):
        """获取Edge版本"""
        try:
            import subprocess
            result = subprocess.run([edge_path, '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                return result.stdout.strip().split()[-1]
        except:
            pass
        return "unknown"
    
    def _collect_system_info(self):
        """收集系统信息"""
        import platform
        import locale
        
        return {
            'os': platform.system(),
            'os_version': platform.version(),
            'architecture': platform.architecture()[0],
            'processor': platform.processor(),
            'locale': locale.getdefaultlocale(),
            'timezone': time.tzname,
            'screen_resolution': self._get_screen_resolution()
        }
    
    def _get_screen_resolution(self):
        """获取屏幕分辨率"""
        try:
            import tkinter as tk
            root = tk.Tk()
            width = root.winfo_screenwidth()
            height = root.winfo_screenheight()
            root.destroy()
            return f"{width}x{height}"
        except:
            return "1920x1080"  # 默认分辨率
    
    def analyze_chrome_profile(self, user_data_path):
        """分析Chrome用户配置"""
        if not os.path.exists(user_data_path):
            return None
            
        analysis = {
            'user_data_path': user_data_path,
            'profiles': {},
            'local_state': None,
            'installed_extensions': [],
            'bookmarks_count': 0,
            'history_count': 0,
            'cookies_count': 0
        }
        
        # 分析Local State
        local_state_path = os.path.join(user_data_path, 'Local State')
        if os.path.exists(local_state_path):
            try:
                with open(local_state_path, 'r', encoding='utf-8') as f:
                    analysis['local_state'] = json.load(f)
            except:
                pass
        
        # 分析Default配置文件
        default_profile = os.path.join(user_data_path, 'Default')
        if os.path.exists(default_profile):
            analysis['profiles']['Default'] = self._analyze_single_profile(default_profile)
        
        # 查找其他配置文件
        for item in os.listdir(user_data_path):
            if item.startswith('Profile ') and os.path.isdir(os.path.join(user_data_path, item)):
                profile_path = os.path.join(user_data_path, item)
                analysis['profiles'][item] = self._analyze_single_profile(profile_path)
        
        return analysis
    
    def _analyze_single_profile(self, profile_path):
        """分析单个配置文件"""
        profile_info = {
            'path': profile_path,
            'preferences': None,
            'bookmarks': None,
            'history_entries': 0,
            'cookies_count': 0,
            'extensions': [],
            'local_storage_count': 0
        }
        
        # 分析Preferences
        prefs_path = os.path.join(profile_path, 'Preferences')
        if os.path.exists(prefs_path):
            try:
                with open(prefs_path, 'r', encoding='utf-8') as f:
                    profile_info['preferences'] = json.load(f)
            except:
                pass
        
        # 分析书签
        bookmarks_path = os.path.join(profile_path, 'Bookmarks')
        if os.path.exists(bookmarks_path):
            try:
                with open(bookmarks_path, 'r', encoding='utf-8') as f:
                    bookmarks = json.load(f)
                    profile_info['bookmarks'] = bookmarks
                    profile_info['bookmarks_count'] = self._count_bookmarks(bookmarks)
            except:
                pass
        
        # 分析历史记录
        history_path = os.path.join(profile_path, 'History')
        if os.path.exists(history_path):
            try:
                # 复制数据库文件以避免锁定
                temp_history = history_path + '.temp'
                shutil.copy2(history_path, temp_history)
                
                conn = sqlite3.connect(temp_history)
                cursor = conn.execute('SELECT COUNT(*) FROM urls')
                profile_info['history_entries'] = cursor.fetchone()[0]
                conn.close()
                
                os.remove(temp_history)
            except:
                pass
        
        # 分析Cookie
        cookies_path = os.path.join(profile_path, 'Cookies')
        if os.path.exists(cookies_path):
            try:
                temp_cookies = cookies_path + '.temp'
                shutil.copy2(cookies_path, temp_cookies)
                
                conn = sqlite3.connect(temp_cookies)
                cursor = conn.execute('SELECT COUNT(*) FROM cookies')
                profile_info['cookies_count'] = cursor.fetchone()[0]
                conn.close()
                
                os.remove(temp_cookies)
            except:
                pass
        
        # 分析扩展
        extensions_path = os.path.join(profile_path, 'Extensions')
        if os.path.exists(extensions_path):
            for ext_id in os.listdir(extensions_path):
                ext_path = os.path.join(extensions_path, ext_id)
                if os.path.isdir(ext_path):
                    profile_info['extensions'].append(ext_id)
        
        # 分析Local Storage
        local_storage_path = os.path.join(profile_path, 'Local Storage')
        if os.path.exists(local_storage_path):
            try:
                profile_info['local_storage_count'] = len(os.listdir(local_storage_path))
            except:
                pass
        
        return profile_info
    
    def _count_bookmarks(self, bookmarks_data):
        """递归计算书签数量"""
        count = 0
        
        def count_recursive(node):
            nonlocal count
            if node.get('type') == 'url':
                count += 1
            elif node.get('type') == 'folder':
                for child in node.get('children', []):
                    count_recursive(child)
        
        for root_folder in bookmarks_data.get('roots', {}).values():
            if isinstance(root_folder, dict):
                count_recursive(root_folder)
        
        return count
    
    def generate_analysis_report(self):
        """生成分析报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'system_info': self.system_info,
            'chrome_installations': {},
            'edge_installations': {},
            'recommendations': []
        }
        
        # 分析Chrome安装
        for chrome_path, chrome_info in self.chrome_paths.items():
            if chrome_info['user_data']:
                analysis = self.analyze_chrome_profile(chrome_info['user_data'])
                report['chrome_installations'][chrome_path] = {
                    'info': chrome_info,
                    'analysis': analysis
                }
        
        # 分析Edge安装
        for edge_path, edge_info in self.edge_paths.items():
            if edge_info['user_data']:
                analysis = self.analyze_chrome_profile(edge_info['user_data'])  # Edge使用相同结构
                report['edge_installations'][edge_path] = {
                    'info': edge_info,
                    'analysis': analysis
                }
        
        # 生成建议
        self._generate_recommendations(report)
        
        return report
    
    def _generate_recommendations(self, report):
        """生成模拟建议"""
        recommendations = []
        
        # 检查是否有可用的原生浏览器
        if report['chrome_installations']:
            recommendations.append("发现Chrome安装，建议使用Chrome环境进行模拟")
        
        if report['edge_installations']:
            recommendations.append("发现Edge安装，可作为备选模拟环境")
        
        # 检查数据丰富度
        for browser_type, installations in [('chrome', report['chrome_installations']), 
                                           ('edge', report['edge_installations'])]:
            for path, data in installations.items():
                analysis = data.get('analysis')
                if analysis:
                    for profile_name, profile_data in analysis.get('profiles', {}).items():
                        if profile_data['history_entries'] > 100:
                            recommendations.append(f"配置文件 {profile_name} 有丰富的浏览历史，适合模拟")
                        if len(profile_data['extensions']) > 3:
                            recommendations.append(f"配置文件 {profile_name} 有多个扩展，增强真实性")
        
        report['recommendations'] = recommendations

def main():
    """主函数"""
    print("🔍 开始分析原生浏览器环境...")
    
    analyzer = NativeBrowserAnalyzer()
    report = analyzer.generate_analysis_report()
    
    # 保存分析报告
    with open('native_browser_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print("✅ 分析完成！")
    print(f"📊 发现Chrome安装: {len(report['chrome_installations'])}")
    print(f"📊 发现Edge安装: {len(report['edge_installations'])}")
    print(f"💡 建议数量: {len(report['recommendations'])}")
    
    print("\n📋 主要建议:")
    for i, rec in enumerate(report['recommendations'][:5], 1):
        print(f"  {i}. {rec}")
    
    print(f"\n📄 详细报告已保存到: native_browser_analysis.json")

if __name__ == "__main__":
    main()

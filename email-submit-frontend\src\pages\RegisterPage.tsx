/**
 * 注册页面
 */
import React, { useState } from 'react';
import { Form, Input, Button, Card, Alert, Typography, Space, Divider, message } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate, Link } from 'react-router-dom';
import { useUserActions, useUserState } from '../stores/userStore';
import { AuthLoading } from '../components/LoadingScreen';
import WindLogo from '../components/WindLogo';

const { Title, Text } = Typography;

const RegisterPage: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { register, clearError } = useUserActions();
  const { isLoading, error } = useUserState();
  const [showAuthLoading, setShowAuthLoading] = useState(false);

  const handleSubmit = async (values: { username: string; password: string; confirmPassword: string }) => {
    setShowAuthLoading(true);
    try {
      const success = await register(values.username, values.password);
      if (!success) {
        setShowAuthLoading(false);
      }
      // 如果成功，等待AuthLoading组件的onComplete回调
    } catch (error) {
      setShowAuthLoading(false);
    }
  };

  const handleRegisterComplete = () => {
    setShowAuthLoading(false);
    message.success('注册成功！请登录使用系统');
    navigate('/login');
  };

  const handleErrorClose = () => {
    clearError();
  };

  const handleCancelLoading = () => {
    setShowAuthLoading(false);
    // 这里可以添加取消注册请求的逻辑
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: 380,
          boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
          borderRadius: '16px',
          border: 'none',
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)'
        }}
      >
        {/* 页面头部 */}
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <div style={{
            width: 64,
            height: 64,
            borderRadius: '50%',
            background: 'linear-gradient(135deg, #1890ff, #722ed1)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 20px',
            boxShadow: '0 4px 20px rgba(24, 144, 255, 0.3)'
          }}>
            <WindLogo size={32} color="#fff" />
          </div>
          <Title level={2} style={{ margin: '0 0 8px 0', color: '#1890ff', fontWeight: 600 }}>
            风车AUG云激活
            <span style={{
              color: '#faad14',
              fontSize: '16px',
              fontWeight: 'bold',
              marginLeft: '8px',
              padding: '2px 6px',
              background: 'rgba(250, 173, 20, 0.1)',
              borderRadius: '4px',
              border: '1px solid rgba(250, 173, 20, 0.3)'
            }}>
              7X24h
            </span>
          </Title>
          <Text type="secondary" style={{ fontSize: 16 }}>注册</Text>
        </div>



        {/* 错误提示 */}
        {error && (
          <Alert
            message="注册失败"
            description={error}
            type="error"
            closable
            onClose={handleErrorClose}
            style={{ marginBottom: 24 }}
          />
        )}

        {/* 注册表单 */}
        <Form
          form={form}
          name="register"
          onFinish={handleSubmit}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 6, message: '用户名至少6个字符' }
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="请输入用户名"
              autoComplete="username"
              style={{
                borderRadius: 8,
                height: 44,
                fontSize: 14
              }}
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="密码"
            rules={[
              { required: true, message: '请输入密码' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入密码"
              autoComplete="new-password"
              style={{
                borderRadius: 8,
                height: 44,
                fontSize: 14
              }}
            />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="确认密码"
            dependencies={['password']}
            rules={[
              { required: true, message: '请确认密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请再次输入密码"
              autoComplete="new-password"
              style={{
                borderRadius: 8,
                height: 44,
                fontSize: 14
              }}
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={isLoading || showAuthLoading}
              disabled={showAuthLoading}
              block
              style={{
                height: 48,
                borderRadius: 8,
                fontSize: 16,
                fontWeight: 500,
                background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                border: 'none',
                boxShadow: '0 4px 15px rgba(24, 144, 255, 0.3)'
              }}
            >
              {isLoading || showAuthLoading ? '注册中...' : '立即注册'}
            </Button>
          </Form.Item>
        </Form>

        <Divider />

        {/* 登录链接 */}
        <div style={{ textAlign: 'center' }}>
          <Space direction="vertical" size="small">
            <Text type="secondary">已有账号？</Text>
            <Link to="/login">
              <Button type="link" style={{ padding: 0 }}>
                立即登录
              </Button>
            </Link>
          </Space>
        </div>
      </Card>

      {/* 注册加载动画 */}
      <AuthLoading
        visible={showAuthLoading}
        type="register"
        onCancel={handleCancelLoading}
        onComplete={handleRegisterComplete}
      />
    </div>
  );
};

export default RegisterPage;

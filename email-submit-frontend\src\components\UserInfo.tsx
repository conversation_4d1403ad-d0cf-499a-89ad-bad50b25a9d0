/**
 * 用户信息显示组件
 * 在页面头部显示用户信息和操作按钮
 */
import React, { useState } from 'react';
import { Space, Button, Dropdown, Badge, Typography, Modal } from 'antd';
import { UserOutlined, SettingOutlined, LogoutOutlined, CreditCardOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { useUserState, useUserActions } from '../stores/userStore';
import UserCenter from './UserCenter';

const { Text } = Typography;

const UserInfo: React.FC = () => {
  const { userInfo } = useUserState();
  const { logout } = useUserActions();
  const [userCenterVisible, setUserCenterVisible] = useState(false);

  if (!userInfo) {
    return null;
  }

  const handleLogout = () => {
    Modal.confirm({
      title: '确认登出',
      content: '确定要退出登录吗？',
      onOk: () => {
        logout();
      },
    });
  };

  const menuItems: MenuProps['items'] = [
    {
      key: 'userCenter',
      label: '用户中心',
      icon: <SettingOutlined />,
      onClick: () => setUserCenterVisible(true),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      label: '退出登录',
      icon: <LogoutOutlined />,
      onClick: handleLogout,
    },
  ];

  return (
    <>
      <Space size="large">
        {/* 剩余次数显示 */}
        <Space>
          <Text type="secondary" style={{ fontSize: '14px' }}>
            激活剩余次数:
          </Text>
          <Badge
            count={userInfo.time_quota}
            showZero={true}
            style={{
              backgroundColor: userInfo.time_quota > 0 ? '#52c41a' : '#ff4d4f',
            }}
          />
        </Space>

        {/* 用户菜单 */}
        <Dropdown menu={{ items: menuItems }} placement="bottomRight">
          <Button type="text" style={{ padding: '4px 8px' }}>
            <Space>
              <UserOutlined />
              <Text style={{ fontSize: '14px' }}>{userInfo.username}</Text>
            </Space>
          </Button>
        </Dropdown>

        {/* 快速充值按钮 */}
        {userInfo.time_quota <= 0 && (
          <Button
            type="primary"
            size="small"
            icon={<CreditCardOutlined />}
            onClick={() => setUserCenterVisible(true)}
          >
            充值
          </Button>
        )}
      </Space>

      {/* 用户中心弹窗 */}
      <Modal
        title="用户中心"
        open={userCenterVisible}
        onCancel={() => setUserCenterVisible(false)}
        footer={null}
        width={700}
        centered
      >
        <UserCenter />
      </Modal>
    </>
  );
};

export default UserInfo;

/**
 * 应用加载状态管理Hook
 * 管理应用初始化过程中的各个步骤和状态
 */
import { useState, useEffect, useCallback } from 'react';
import { LoadingStep } from '../components/LoadingScreen';
import { emailAPI } from '../services/api';
import { useSystemStore } from '../stores/systemStore';
import { useUserStore } from '../stores/userStore';

interface UseAppLoadingReturn {
  isLoading: boolean;
  steps: LoadingStep[];
  error: string | null;
  startLoading: () => Promise<void>;
  resetLoading: () => void;
}

// 定义加载步骤
const LOADING_STEPS: Omit<LoadingStep, 'status' | 'progress' | 'error'>[] = [
  { id: 'config', name: '加载配置文件' },
  { id: 'connection', name: '连接服务器' },
  { id: 'database', name: '初始化数据库' },
  { id: 'user-auth', name: '验证用户认证' },
  { id: 'system-status', name: '获取系统状态' },
  { id: 'complete', name: '初始化完成' }
];

export const useAppLoading = (): UseAppLoadingReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [steps, setSteps] = useState<LoadingStep[]>([]);
  const [error, setError] = useState<string | null>(null);

  // 初始化步骤
  const initializeSteps = useCallback(() => {
    setSteps(LOADING_STEPS.map(step => ({
      ...step,
      status: 'waiting' as const,
      progress: 0
    })));
  }, []);

  // 更新步骤状态
  const updateStep = useCallback((stepId: string, updates: Partial<LoadingStep>) => {
    setSteps(prevSteps => 
      prevSteps.map(step => 
        step.id === stepId ? { ...step, ...updates } : step
      )
    );
  }, []);

  // 执行单个步骤
  const executeStep = useCallback(async (stepId: string): Promise<boolean> => {
    updateStep(stepId, { status: 'loading', progress: 0 });

    try {
      switch (stepId) {
        case 'config':
          // 加载配置文件
          updateStep(stepId, { progress: 30 });
          const { initConfig } = await import('../config');
          await initConfig();
          updateStep(stepId, { progress: 100 });
          break;

        case 'connection':
          // 测试服务器连接
          updateStep(stepId, { progress: 20 });
          let connected = false;
          let retryCount = 0;
          const maxRetries = 3;

          while (!connected && retryCount < maxRetries) {
            try {
              connected = await emailAPI.testConnection();
              if (!connected) {
                retryCount++;
                updateStep(stepId, { progress: 20 + (retryCount * 20) });
                if (retryCount < maxRetries) {
                  await new Promise(resolve => setTimeout(resolve, 1000));
                }
              }
            } catch (err) {
              retryCount++;
              updateStep(stepId, { progress: 20 + (retryCount * 20) });
              if (retryCount < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, 1000));
              }
            }
          }

          if (!connected) {
            throw new Error('无法连接到服务器，请检查autoback.py是否运行');
          }
          updateStep(stepId, { progress: 100 });
          break;

        case 'database':
          // 初始化数据库（通过获取系统状态来验证数据库连接）
          updateStep(stepId, { progress: 50 });
          await emailAPI.getSystemStatus();
          updateStep(stepId, { progress: 100 });
          break;

        case 'user-auth':
          // 验证用户认证
          updateStep(stepId, { progress: 30 });
          const userStore = useUserStore.getState();
          if (userStore.isLoggedIn && userStore.token) {
            try {
              await userStore.refreshUserInfo();
              updateStep(stepId, { progress: 100 });
            } catch (err) {
              console.warn('用户认证验证失败，自动登出');
              userStore.logout();
              updateStep(stepId, { progress: 100 });
            }
          } else {
            updateStep(stepId, { progress: 100 });
          }
          break;

        case 'system-status':
          // 获取系统状态
          updateStep(stepId, { progress: 50 });
          const systemStore = useSystemStore.getState();
          await systemStore.fetchSystemStatus();
          updateStep(stepId, { progress: 100 });
          break;

        case 'complete':
          // 完成初始化
          updateStep(stepId, { progress: 100 });
          break;

        default:
          throw new Error(`未知的步骤: ${stepId}`);
      }

      updateStep(stepId, { status: 'success' });
      return true;
    } catch (err: any) {
      const errorMessage = err.message || '执行失败';
      updateStep(stepId, { 
        status: 'error', 
        error: errorMessage 
      });
      return false;
    }
  }, [updateStep]);

  // 开始加载流程
  const startLoading = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    setError(null);
    initializeSteps();

    try {
      // 按顺序执行每个步骤
      for (const step of LOADING_STEPS) {
        const success = await executeStep(step.id);
        if (!success) {
          throw new Error(`步骤 "${step.name}" 执行失败`);
        }
        
        // 在步骤之间添加小延迟，让用户看到进度
        if (step.id !== 'complete') {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }

      // 所有步骤完成后，延迟一下再隐藏加载屏幕
      setTimeout(() => {
        setIsLoading(false);
      }, 800);

    } catch (err: any) {
      setError(err.message || '初始化失败');
      // 即使失败也要隐藏加载屏幕，让用户能看到错误信息
      setTimeout(() => {
        setIsLoading(false);
      }, 2000);
    }
  }, [initializeSteps, executeStep]);

  // 重置加载状态
  const resetLoading = useCallback(() => {
    setIsLoading(false);
    setSteps([]);
    setError(null);
  }, []);

  // 组件挂载时初始化步骤
  useEffect(() => {
    initializeSteps();
  }, [initializeSteps]);

  return {
    isLoading,
    steps,
    error,
    startLoading,
    resetLoading
  };
};

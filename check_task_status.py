#!/usr/bin/env python3
"""
检查数据库中的任务状态
"""

import sqlite3
import os
from datetime import datetime

def check_task_database():
    """检查任务数据库"""
    db_path = 'tasks.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        with sqlite3.connect(db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            # 检查任务ID为2的任务
            print("🔍 检查任务ID为2的任务:")
            cursor = conn.execute('SELECT * FROM task_queue WHERE id = 2')
            task = cursor.fetchone()
            
            if task:
                print(f"   📋 任务ID: {task['id']}")
                print(f"   📧 邮箱: {task['email']}")
                print(f"   📊 状态: {task['status']}")
                print(f"   👤 用户ID: {task['user_id']}")
                print(f"   📅 创建时间: {task['created_at']}")
                print(f"   ✅ 完成时间: {task['completed_at']}")
                print(f"   📝 结果消息: {task['result_message']}")
                print(f"   🔢 队列位置: {task.get('queue_position', 'N/A')}")
                
                # 状态映射
                status_map = {
                    'waiting': 'pending',
                    'processing': 'processing', 
                    'completed': 'success',
                    'failed': 'failed'
                }
                frontend_status = status_map.get(task['status'], task['status'])
                print(f"   🎯 前端状态: {frontend_status}")
                
                # 分析问题
                if task['status'] == 'waiting':
                    print("   ⚠️ 问题分析: 任务仍在等待处理状态")
                    print("   💡 建议: 检查autoback.py是否正在处理任务")
                elif task['status'] == 'processing':
                    print("   ⚠️ 问题分析: 任务正在处理中")
                    print("   💡 建议: 等待处理完成或检查处理进程")
                elif task['status'] in ['completed', 'failed']:
                    print("   ✅ 任务已完成，前端应该显示100%进度")
                    print("   ⚠️ 问题: 前端可能没有正确更新状态")
                
            else:
                print("   ❌ 任务ID为2的任务不存在")
            
            print("\n" + "="*60)
            
            # 检查所有pending状态的任务
            print("🔍 检查所有pending状态的任务:")
            cursor = conn.execute('''
                SELECT id, email, status, user_id, created_at, completed_at 
                FROM task_queue 
                WHERE status = 'waiting' 
                ORDER BY created_at DESC 
                LIMIT 10
            ''')
            pending_tasks = cursor.fetchall()
            
            if pending_tasks:
                print(f"   📊 找到 {len(pending_tasks)} 个等待处理的任务:")
                for task in pending_tasks:
                    print(f"   - ID:{task['id']} 邮箱:{task['email']} 用户:{task['user_id']} 创建:{task['created_at']}")
            else:
                print("   ✅ 没有等待处理的任务")
            
            print("\n" + "="*60)
            
            # 检查最近的任务
            print("🔍 检查最近的10个任务:")
            cursor = conn.execute('''
                SELECT id, email, status, user_id, created_at, completed_at 
                FROM task_queue 
                ORDER BY created_at DESC 
                LIMIT 10
            ''')
            recent_tasks = cursor.fetchall()
            
            if recent_tasks:
                print("   📊 最近的任务:")
                for task in recent_tasks:
                    status_icon = {
                        'waiting': '⏳',
                        'processing': '🔄', 
                        'completed': '✅',
                        'failed': '❌'
                    }.get(task['status'], '❓')
                    
                    print(f"   {status_icon} ID:{task['id']} {task['email']} [{task['status']}] {task['created_at']}")
            else:
                print("   ❌ 没有找到任务")
                
            print("\n" + "="*60)
            
            # 统计信息
            print("📊 任务统计:")
            cursor = conn.execute('SELECT status, COUNT(*) as count FROM task_queue GROUP BY status')
            stats = cursor.fetchall()
            
            total = 0
            for stat in stats:
                print(f"   {stat['status']}: {stat['count']} 个")
                total += stat['count']
            print(f"   总计: {total} 个任务")
            
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")

def check_autoback_processing():
    """检查autoback.py是否在处理任务"""
    print("\n" + "="*60)
    print("🔍 检查autoback.py处理状态:")
    
    # 检查是否有processing状态的任务
    try:
        with sqlite3.connect('tasks.db') as conn:
            cursor = conn.execute('SELECT COUNT(*) as count FROM task_queue WHERE status = "processing"')
            processing_count = cursor.fetchone()[0]
            
            if processing_count > 0:
                print(f"   🔄 有 {processing_count} 个任务正在处理中")
                print("   💡 建议: 等待处理完成")
            else:
                print("   ✅ 没有正在处理的任务")
                
                # 检查是否有等待的任务
                cursor = conn.execute('SELECT COUNT(*) as count FROM task_queue WHERE status = "waiting"')
                waiting_count = cursor.fetchone()[0]
                
                if waiting_count > 0:
                    print(f"   ⏳ 有 {waiting_count} 个任务等待处理")
                    print("   ⚠️ 可能问题: autoback.py没有在处理任务")
                    print("   💡 建议: 检查autoback.py是否正常运行")
                else:
                    print("   ✅ 没有等待处理的任务")
                    
    except Exception as e:
        print(f"   ❌ 检查失败: {e}")

def main():
    """主函数"""
    print("🔍 任务状态检查工具")
    print("="*60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    check_task_database()
    check_autoback_processing()
    
    print("\n" + "="*60)
    print("🎯 总结:")
    print("如果任务ID为2的状态是'waiting'，说明任务还在等待处理")
    print("如果任务状态是'completed'或'failed'，但前端显示20%，说明前端没有正确更新")
    print("建议检查:")
    print("1. autoback.py是否正在运行并处理任务")
    print("2. 前端是否正确调用了刷新API")
    print("3. 前端任务状态更新逻辑是否正常")
    print("="*60)

if __name__ == "__main__":
    main()

{"update_url": "https://clients2.google.com/service/update2/crx", "version": "0.2.2", "manifest_version": 3, "offline_enabled": true, "name": "Canvas Fingerprint Defender", "homepage_url": "https://mybrowseraddon.com/canvas-defender.html", "description": "Defending against Canvas fingerprinting by reporting a fake value.", "commands": {"_execute_action": {}}, "background": {"service_worker": "background.js"}, "permissions": ["storage", "contextMenus", "notifications"], "action": {"default_popup": "data/popup/popup.html", "default_title": "Canvas Fingerprint Defender", "default_icon": {"16": "data/icons/16.png", "32": "data/icons/32.png", "48": "data/icons/48.png", "64": "data/icons/64.png"}}, "content_scripts": [{"world": "MAIN", "all_frames": true, "matches": ["*://*/*"], "match_about_blank": true, "run_at": "document_start", "match_origin_as_fallback": true, "js": ["data/content_script/page_context/inject.js"]}, {"world": "ISOLATED", "all_frames": true, "matches": ["*://*/*"], "match_about_blank": true, "run_at": "document_start", "match_origin_as_fallback": true, "js": ["data/content_script/inject.js"]}], "icons": {"16": "data/icons/16.png", "32": "data/icons/32.png", "48": "data/icons/48.png", "64": "data/icons/64.png", "128": "data/icons/128.png"}}
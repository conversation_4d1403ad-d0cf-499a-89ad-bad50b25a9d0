{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["LAAAACUAAABodHRwczovL2Nocm9tZXdlYnN0b3JlLmdvb2dsZWFwaXMuY29tAAAA", false], "broken_count": 1, "broken_until": "1741956198", "host": "chromewebstore.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false], "broken_count": 3, "broken_until": "1741956199", "host": "android.clients.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2RvdWJhby5jb20AAA==", false], "broken_count": 1, "broken_until": "1741956203", "host": "www.google-analytics.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true], "broken_count": 1, "broken_until": "1741956216", "host": "ogads-pa.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true], "broken_count": 1, "broken_until": "1741956217", "host": "play.google.com", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL2d2dDEuY29t", false], "server": "https://redirector.gvt1.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL2d2dDEuY29t", false], "server": "https://dl.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false], "server": "https://clients2.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL2dvb2dsZXVzZXJjb250ZW50LmNvbQAAAA==", false], "server": "https://clients2.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["LAAAACUAAABodHRwczovL2Nocm9tZXdlYnN0b3JlLmdvb2dsZWFwaXMuY29tAAAA", false], "server": "https://chromewebstore.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2RvdWJhby5jb20AAA==", false], "server": "https://p9-flow-imagex-sign.byteimg.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2RvdWJhby5jb20AAA==", false], "server": "https://lf3-short.ibytedapm.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13389021502140875", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false], "server": "https://android.clients.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13389021503563617", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2RvdWJhby5jb20AAA==", false], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2RvdWJhby5jb20AAA==", false], "server": "https://p3-flow-imagex-sign.byteimg.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2RvdWJhby5jb20AAA==", false], "server": "https://lf3-config.bytetcc.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2RvdWJhby5jb20AAA==", false], "server": "https://lf3-static.bytednsdoc.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2RvdWJhby5jb20AAA==", false], "server": "https://mcs.zijieapi.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2RvdWJhby5jb20AAA==", false], "server": "https://lf-flow-web-cdn.doubao.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13389021508731301", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2RvdWJhby5jb20AAA==", false], "server": "https://www.google-analytics.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2RvdWJhby5jb20AAA==", false], "server": "https://www.doubao.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2RvdWJhby5jb20AAA==", false], "server": "https://mssdk.bytedance.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2RvdWJhby5jb20AAA==", false], "server": "https://opt.doubao.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2RvdWJhby5jb20AAA==", false], "server": "https://mcs.doubao.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13389021515553381", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false], "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13389021515753729", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13389021516067265", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true], "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13389021516212287", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true], "server": "https://ogads-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13389021517278716", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true], "server": "https://play.google.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAESABiAgICA+P////8B": "4G"}}}
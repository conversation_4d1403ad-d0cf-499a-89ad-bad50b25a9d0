"""
原生环境模拟器
基于分析结果创建模拟原生浏览器环境的配置
"""
import os
import json
import sqlite3
import shutil
import random
import time
from pathlib import Path
from datetime import datetime, timedelta
import uuid

class NativeEnvironmentSimulator:
    def __init__(self, analysis_file='native_browser_analysis.json'):
        self.analysis = self._load_analysis(analysis_file)
        self.template_data = self._prepare_templates()

    def _load_analysis(self, analysis_file):
        """加载分析结果"""
        if os.path.exists(analysis_file):
            with open(analysis_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None

    def _prepare_templates(self):
        """准备模拟模板"""
        if not self.analysis:
            return self._create_default_templates()

        # 从分析结果中提取最佳模板
        best_template = self._select_best_template()
        return best_template

    def _select_best_template(self):
        """选择最佳的模拟模板"""
        best_score = 0
        best_template = None

        # 评估Chrome安装
        for path, data in self.analysis.get('chrome_installations', {}).items():
            analysis = data.get('analysis')
            if analysis:
                for profile_name, profile_data in analysis.get('profiles', {}).items():
                    score = self._calculate_template_score(profile_data)
                    if score > best_score:
                        best_score = score
                        best_template = {
                            'browser_type': 'chrome',
                            'source_path': path,
                            'profile_data': profile_data,
                            'local_state': analysis.get('local_state'),
                            'score': score
                        }

        # 评估Edge安装
        for path, data in self.analysis.get('edge_installations', {}).items():
            analysis = data.get('analysis')
            if analysis:
                for profile_name, profile_data in analysis.get('profiles', {}).items():
                    score = self._calculate_template_score(profile_data)
                    if score > best_score:
                        best_score = score
                        best_template = {
                            'browser_type': 'edge',
                            'source_path': path,
                            'profile_data': profile_data,
                            'local_state': analysis.get('local_state'),
                            'score': score
                        }

        return best_template or self._create_default_templates()

    def _calculate_template_score(self, profile_data):
        """计算模板质量分数"""
        score = 0

        # 历史记录分数
        history_count = profile_data.get('history_entries', 0)
        if history_count > 1000:
            score += 30
        elif history_count > 100:
            score += 20
        elif history_count > 10:
            score += 10

        # 书签分数
        bookmarks_count = profile_data.get('bookmarks_count', 0)
        if bookmarks_count > 50:
            score += 20
        elif bookmarks_count > 10:
            score += 15
        elif bookmarks_count > 0:
            score += 10

        # 扩展分数
        extensions_count = len(profile_data.get('extensions', []))
        if extensions_count > 5:
            score += 25
        elif extensions_count > 2:
            score += 20
        elif extensions_count > 0:
            score += 10

        # Cookie分数
        cookies_count = profile_data.get('cookies_count', 0)
        if cookies_count > 500:
            score += 15
        elif cookies_count > 100:
            score += 10
        elif cookies_count > 0:
            score += 5

        # Local Storage分数
        ls_count = profile_data.get('local_storage_count', 0)
        if ls_count > 20:
            score += 10
        elif ls_count > 5:
            score += 5

        return score

    def _create_default_templates(self):
        """创建默认模板（当没有原生浏览器时）"""
        return {
            'browser_type': 'chrome',
            'source_path': None,
            'profile_data': None,
            'local_state': None,
            'score': 0,
            'is_default': True
        }

    def create_simulated_profile(self, profile_name, target_dir):
        """创建模拟的浏览器配置文件"""
        print(f"🎭 开始创建模拟环境: {profile_name}")

        # 创建目标目录
        profile_path = os.path.join(target_dir, profile_name)
        os.makedirs(profile_path, exist_ok=True)

        # 创建User Data结构
        user_data_path = os.path.join(profile_path, 'User Data')
        default_profile_path = os.path.join(user_data_path, 'Default')
        os.makedirs(default_profile_path, exist_ok=True)

        simulation_info = {
            'profile_name': profile_name,
            'created_at': datetime.now().isoformat(),
            'template_source': self.template_data.get('source_path', 'default'),
            'browser_type': self.template_data.get('browser_type', 'chrome'),
            'simulation_score': self.template_data.get('score', 0)
        }

        # 1. 创建Local State
        self._create_local_state(user_data_path, simulation_info)

        # 2. 创建Preferences
        self._create_preferences(default_profile_path, simulation_info)

        # 3. 创建书签
        self._create_bookmarks(default_profile_path, simulation_info)

        # 4. 创建历史记录
        self._create_history(default_profile_path, simulation_info)

        # 5. 创建Cookie数据库
        self._create_cookies(default_profile_path, simulation_info)

        # 6. 创建扩展目录
        self._create_extensions(default_profile_path, simulation_info)

        # 7. 创建其他必要文件
        self._create_additional_files(user_data_path, default_profile_path, simulation_info)

        # 保存模拟信息
        with open(os.path.join(profile_path, 'simulation_info.json'), 'w', encoding='utf-8') as f:
            json.dump(simulation_info, f, indent=2, ensure_ascii=False)

        print(f"✅ 模拟环境创建完成: {profile_path}")
        return profile_path

    def _create_local_state(self, user_data_path, simulation_info):
        """创建Local State文件"""
        if self.template_data.get('local_state'):
            # 基于模板创建
            local_state = self.template_data['local_state'].copy()
        else:
            # 创建默认Local State
            local_state = {
                "background_mode": {"enabled": False},
                "browser": {
                    "enabled_labs_experiments": [],
                    "has_seen_welcome_page": True,
                    "check_default_browser": False
                },
                "default_apps_install_state": 3,
                "distribution": {
                    "import_bookmarks": False,
                    "import_history": False,
                    "import_search_engine": False,
                    "make_chrome_default": False,
                    "make_chrome_default_for_user": False,
                    "suppress_first_run_default_browser_prompt": True
                },
                "dns_prefetching": {"enabled": True},
                "profile": {
                    "info_cache": {
                        "Default": {
                            "active_time": time.time(),
                            "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_0",
                            "background_apps": False,
                            "gaia_given_name": "",
                            "gaia_name": "",
                            "gaia_picture_file_name": "",
                            "is_consented_primary_account": False,
                            "is_ephemeral": False,
                            "is_using_default_avatar": True,
                            "is_using_default_name": True,
                            "name": "Person 1",
                            "user_name": ""
                        }
                    },
                    "last_used": "Default",
                    "metrics": {"reporting_enabled": False},
                    "profiles_created": 1
                }
            }

        # 随机化一些标识符
        local_state["user_experience_metrics"] = {
            "client_id": str(uuid.uuid4()),
            "machine_id": random.randint(100000, 999999)
        }

        local_state_path = os.path.join(user_data_path, 'Local State')
        with open(local_state_path, 'w', encoding='utf-8') as f:
            json.dump(local_state, f, indent=2)

        print("📄 已创建Local State")

    def _create_preferences(self, profile_path, simulation_info):
        """创建Preferences文件"""
        if self.template_data.get('profile_data', {}).get('preferences'):
            # 基于模板创建
            preferences = self.template_data['profile_data']['preferences'].copy()
            # 清理敏感数据
            self._sanitize_preferences(preferences)
        else:
            # 创建默认Preferences
            preferences = self._create_default_preferences()

        # 随机化指纹相关设置
        self._randomize_fingerprint_settings(preferences)

        prefs_path = os.path.join(profile_path, 'Preferences')
        with open(prefs_path, 'w', encoding='utf-8') as f:
            json.dump(preferences, f, indent=2)

        print("⚙️ 已创建Preferences")

    def _create_default_preferences(self):
        """创建默认的Preferences配置"""
        return {
            "alternate_error_pages": {"enabled": True},
            "autofill": {
                "enabled": True,
                "profile_enabled": True
            },
            "browser": {
                "show_home_button": True,
                "check_default_browser": False,
                "has_seen_welcome_page": True
            },
            "distribution": {
                "import_bookmarks": False,
                "import_history": False,
                "make_chrome_default": False,
                "suppress_first_run_bubble": True
            },
            "dns_prefetching": {"enabled": True},
            "download": {
                "default_directory": os.path.expanduser("~/Downloads"),
                "prompt_for_download": False
            },
            "extensions": {
                "alerts": {"initialized": True},
                "settings": {}
            },
            "homepage": "https://www.google.com",
            "homepage_is_newtabpage": True,
            "profile": {
                "avatar_index": 0,
                "content_settings": {
                    "exceptions": {},
                    "pattern_pairs": {}
                },
                "default_content_setting_values": {
                    "geolocation": 1,
                    "media_stream": 1,
                    "notifications": 1,
                    "ppapi_broker": 1
                },
                "exit_type": "Normal",
                "exited_cleanly": True,
                "name": "Person 1"
            },
            "search": {
                "suggest_enabled": True
            },
            "session": {
                "restore_on_startup": 1,
                "startup_urls": []
            },
            "translate": {"enabled": True}
        }

    def _sanitize_preferences(self, preferences):
        """清理Preferences中的敏感数据"""
        # 移除个人信息
        if 'profile' in preferences:
            profile = preferences['profile']
            if 'name' in profile and not profile['name'].startswith('Person'):
                profile['name'] = f"Person {random.randint(1, 9)}"

            # 清理账户信息
            profile.pop('gaia_id', None)
            profile.pop('user_name', None)
            profile.pop('gaia_name', None)

        # 清理同步设置
        preferences.pop('sync', None)
        preferences.pop('signin', None)

        # 清理密码和支付信息
        preferences.pop('password_manager', None)
        preferences.pop('autofill', None)

    def _randomize_fingerprint_settings(self, preferences):
        """随机化指纹相关设置"""
        # 随机化一些用户偏好
        if 'profile' not in preferences:
            preferences['profile'] = {}

        # 随机头像
        preferences['profile']['avatar_index'] = random.randint(0, 25)

        # 随机化一些设置
        preferences['enable_do_not_track'] = random.choice([True, False])
        preferences['safebrowsing'] = {"enabled": random.choice([True, False])}

        # 随机化语言设置
        languages = ['en-US', 'en-GB', 'zh-CN', 'zh-TW', 'ja', 'ko']
        preferences['intl'] = {
            "accept_languages": random.choice(languages) + ",en"
        }

    def _create_bookmarks(self, profile_path, simulation_info):
        """创建书签文件"""
        if self.template_data.get('profile_data', {}).get('bookmarks'):
            # 基于模板创建（清理个人信息）
            bookmarks = self.template_data['profile_data']['bookmarks'].copy()
            self._sanitize_bookmarks(bookmarks)
        else:
            # 创建默认书签
            bookmarks = self._create_default_bookmarks()

        bookmarks_path = os.path.join(profile_path, 'Bookmarks')
        with open(bookmarks_path, 'w', encoding='utf-8') as f:
            json.dump(bookmarks, f, indent=2, ensure_ascii=False)

        print("🔖 已创建书签")

    def _create_default_bookmarks(self):
        """创建默认书签"""
        return {
            "checksum": "0123456789abcdef",
            "roots": {
                "bookmark_bar": {
                    "children": [
                        {
                            "date_added": str(int(time.time() * 1000000)),
                            "id": "1",
                            "name": "Google",
                            "type": "url",
                            "url": "https://www.google.com/"
                        },
                        {
                            "date_added": str(int(time.time() * 1000000)),
                            "id": "2",
                            "name": "YouTube",
                            "type": "url",
                            "url": "https://www.youtube.com/"
                        },
                        {
                            "children": [
                                {
                                    "date_added": str(int(time.time() * 1000000)),
                                    "id": "4",
                                    "name": "GitHub",
                                    "type": "url",
                                    "url": "https://github.com/"
                                }
                            ],
                            "date_added": str(int(time.time() * 1000000)),
                            "date_modified": str(int(time.time() * 1000000)),
                            "id": "3",
                            "name": "开发工具",
                            "type": "folder"
                        }
                    ],
                    "date_added": str(int(time.time() * 1000000)),
                    "date_modified": str(int(time.time() * 1000000)),
                    "id": "1",
                    "name": "书签栏",
                    "type": "folder"
                },
                "other": {
                    "children": [],
                    "date_added": str(int(time.time() * 1000000)),
                    "date_modified": str(int(time.time() * 1000000)),
                    "id": "2",
                    "name": "其他书签",
                    "type": "folder"
                },
                "synced": {
                    "children": [],
                    "date_added": str(int(time.time() * 1000000)),
                    "date_modified": str(int(time.time() * 1000000)),
                    "id": "3",
                    "name": "移动设备书签",
                    "type": "folder"
                }
            },
            "version": 1
        }

    def _sanitize_bookmarks(self, bookmarks):
        """清理书签中的个人信息"""
        def clean_node(node):
            if isinstance(node, dict):
                # 移除可能的个人标识
                if node.get('type') == 'url':
                    url = node.get('url', '')
                    # 移除包含个人信息的URL
                    personal_domains = ['mail.', 'drive.', 'docs.', 'photos.']
                    if any(domain in url for domain in personal_domains):
                        return None

                # 递归处理子节点
                if 'children' in node:
                    cleaned_children = []
                    for child in node['children']:
                        cleaned_child = clean_node(child)
                        if cleaned_child:
                            cleaned_children.append(cleaned_child)
                    node['children'] = cleaned_children

                return node
            return node

        for root_name, root_folder in bookmarks.get('roots', {}).items():
            clean_node(root_folder)

    def _create_history(self, profile_path, simulation_info):
        """创建历史记录数据库"""
        history_path = os.path.join(profile_path, 'History')

        # 创建SQLite数据库
        conn = sqlite3.connect(history_path)

        # 创建表结构
        conn.executescript('''
            CREATE TABLE urls(
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                url LONGVARCHAR,
                title LONGVARCHAR,
                visit_count INTEGER DEFAULT 0 NOT NULL,
                typed_count INTEGER DEFAULT 0 NOT NULL,
                last_visit_time INTEGER NOT NULL,
                hidden INTEGER DEFAULT 0 NOT NULL
            );

            CREATE TABLE visits(
                id INTEGER PRIMARY KEY,
                url INTEGER NOT NULL,
                visit_time INTEGER NOT NULL,
                from_visit INTEGER,
                transition INTEGER DEFAULT 0 NOT NULL,
                segment_id INTEGER,
                visit_duration INTEGER DEFAULT 0 NOT NULL
            );

            CREATE INDEX visits_url_index ON visits (url);
            CREATE INDEX visits_from_index ON visits (from_visit);
            CREATE INDEX visits_time_index ON visits (visit_time);
        ''')

        # 插入模拟历史记录
        self._insert_simulated_history(conn)

        conn.close()
        print("📚 已创建历史记录")

    def _insert_simulated_history(self, conn):
        """插入模拟的历史记录"""
        # 常见网站列表
        common_sites = [
            ('https://www.google.com/', 'Google'),
            ('https://www.youtube.com/', 'YouTube'),
            ('https://www.facebook.com/', 'Facebook'),
            ('https://www.twitter.com/', 'Twitter'),
            ('https://www.instagram.com/', 'Instagram'),
            ('https://www.linkedin.com/', 'LinkedIn'),
            ('https://www.reddit.com/', 'Reddit'),
            ('https://www.wikipedia.org/', 'Wikipedia'),
            ('https://www.amazon.com/', 'Amazon'),
            ('https://www.netflix.com/', 'Netflix'),
            ('https://www.github.com/', 'GitHub'),
            ('https://stackoverflow.com/', 'Stack Overflow'),
            ('https://www.baidu.com/', '百度'),
            ('https://www.zhihu.com/', '知乎'),
            ('https://www.bilibili.com/', 'bilibili'),
            ('https://www.taobao.com/', '淘宝'),
        ]

        # 生成历史记录
        base_time = int(time.time() * 1000000)  # Chrome使用微秒
        url_id = 1
        visit_id = 1

        for i, (url, title) in enumerate(common_sites):
            # 随机访问次数
            visit_count = random.randint(1, 50)
            typed_count = random.randint(0, min(5, visit_count))

            # 最后访问时间（最近30天内）
            days_ago = random.randint(0, 30)
            last_visit = base_time - (days_ago * 24 * 60 * 60 * 1000000)

            # 插入URL记录
            conn.execute('''
                INSERT INTO urls (id, url, title, visit_count, typed_count, last_visit_time, hidden)
                VALUES (?, ?, ?, ?, ?, ?, 0)
            ''', (url_id, url, title, visit_count, typed_count, last_visit))

            # 插入访问记录
            for j in range(min(visit_count, 10)):  # 限制访问记录数量
                visit_time = last_visit - (j * random.randint(1, 7) * 24 * 60 * 60 * 1000000)
                transition = random.choice([0, 1, 2])  # 不同的访问方式
                duration = random.randint(30, 300) * 1000000  # 30秒到5分钟

                conn.execute('''
                    INSERT INTO visits (id, url, visit_time, from_visit, transition, visit_duration)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (visit_id, url_id, visit_time, None, transition, duration))

                visit_id += 1

            url_id += 1

        conn.commit()

    def _create_cookies(self, profile_path, simulation_info):
        """创建Cookie数据库"""
        cookies_path = os.path.join(profile_path, 'Cookies')

        conn = sqlite3.connect(cookies_path)

        # 创建Cookie表结构
        conn.executescript('''
            CREATE TABLE cookies(
                creation_utc INTEGER NOT NULL,
                host_key TEXT NOT NULL,
                name TEXT NOT NULL,
                value TEXT NOT NULL,
                path TEXT NOT NULL DEFAULT '/',
                expires_utc INTEGER NOT NULL DEFAULT 0,
                is_secure INTEGER NOT NULL DEFAULT 0,
                is_httponly INTEGER NOT NULL DEFAULT 0,
                last_access_utc INTEGER NOT NULL DEFAULT 0,
                has_expires INTEGER NOT NULL DEFAULT 1,
                is_persistent INTEGER NOT NULL DEFAULT 1,
                priority INTEGER NOT NULL DEFAULT 1,
                encrypted_value BLOB DEFAULT '',
                samesite INTEGER NOT NULL DEFAULT -1,
                source_scheme INTEGER NOT NULL DEFAULT 0,
                source_port INTEGER NOT NULL DEFAULT -1,
                is_same_party INTEGER NOT NULL DEFAULT 0
            );

            CREATE UNIQUE INDEX domain_cookies ON cookies(host_key, name, path);
        ''')

        # 插入一些基本Cookie
        self._insert_simulated_cookies(conn)

        conn.close()
        print("🍪 已创建Cookie数据库")

    def _insert_simulated_cookies(self, conn):
        """插入模拟Cookie"""
        base_time = int(time.time() * 1000000)

        # 基本的功能性Cookie
        cookies = [
            ('.google.com', 'NID', 'random_google_id_' + str(random.randint(100000, 999999))),
            ('.youtube.com', 'VISITOR_INFO1_LIVE', 'random_visitor_' + str(random.randint(100000, 999999))),
            ('.baidu.com', 'BAIDUID', 'random_baidu_' + str(random.randint(100000, 999999))),
        ]

        for host, name, value in cookies:
            creation_time = base_time - random.randint(1, 30) * 24 * 60 * 60 * 1000000
            expires_time = base_time + 365 * 24 * 60 * 60 * 1000000  # 1年后过期

            conn.execute('''
                INSERT INTO cookies (
                    creation_utc, host_key, name, value, path, expires_utc,
                    is_secure, is_httponly, last_access_utc, has_expires, is_persistent
                ) VALUES (?, ?, ?, ?, '/', ?, 0, 0, ?, 1, 1)
            ''', (creation_time, host, name, value, expires_time, creation_time))

        conn.commit()

    def _create_extensions(self, profile_path, simulation_info):
        """创建扩展目录"""
        extensions_path = os.path.join(profile_path, 'Extensions')
        os.makedirs(extensions_path, exist_ok=True)

        # 创建一些常见扩展的目录结构（空目录，模拟已安装）
        common_extensions = [
            'nkbihfbeogaeaoehlefnkodbefgpgknn',  # MetaMask
            'cjpalhdlnbpafiamejdnhcphjbkeiagm',  # uBlock Origin
            'gighmmpiobklfepjocnamgkkbiglidom',  # AdBlock
            'bhhhlbepdkbapadjdnnojkbgioiodbic',  # Solflare
        ]

        for ext_id in random.sample(common_extensions, random.randint(1, 3)):
            ext_dir = os.path.join(extensions_path, ext_id)
            os.makedirs(ext_dir, exist_ok=True)

            # 创建版本目录
            version_dir = os.path.join(ext_dir, '1.0.0_0')
            os.makedirs(version_dir, exist_ok=True)

            # 创建基本的manifest文件
            manifest = {
                "manifest_version": 2,
                "name": f"Extension {ext_id[:8]}",
                "version": "1.0.0"
            }

            with open(os.path.join(version_dir, 'manifest.json'), 'w') as f:
                json.dump(manifest, f)

        print("🧩 已创建扩展目录")

    def _create_additional_files(self, user_data_path, profile_path, simulation_info):
        """创建其他必要文件"""
        # 创建First Run文件
        first_run_path = os.path.join(user_data_path, 'First Run')
        with open(first_run_path, 'w') as f:
            f.write('')

        # 创建Last Version文件
        last_version_path = os.path.join(user_data_path, 'Last Version')
        with open(last_version_path, 'w') as f:
            f.write('120.0.6099.109')  # 模拟Chrome版本

        # 创建Local Storage目录
        local_storage_path = os.path.join(profile_path, 'Local Storage')
        os.makedirs(local_storage_path, exist_ok=True)

        # 创建Session Storage目录
        session_storage_path = os.path.join(profile_path, 'Session Storage')
        os.makedirs(session_storage_path, exist_ok=True)

        # 创建Web Data文件（自动填充数据）
        web_data_path = os.path.join(profile_path, 'Web Data')
        conn = sqlite3.connect(web_data_path)
        conn.executescript('''
            CREATE TABLE autofill (
                name VARCHAR, value VARCHAR, value_lower VARCHAR,
                date_created INTEGER DEFAULT 0, date_last_used INTEGER DEFAULT 0,
                count INTEGER DEFAULT 1
            );
            CREATE TABLE credit_cards (
                guid VARCHAR PRIMARY KEY,
                name_on_card VARCHAR,
                expiration_month INTEGER,
                expiration_year INTEGER,
                card_number_encrypted BLOB,
                date_modified INTEGER NOT NULL DEFAULT 0
            );
        ''')
        conn.close()

        print("📁 已创建其他必要文件")

def main():
    """主函数"""
    print("🎭 原生环境模拟器")
    print("=" * 50)

    # 检查分析文件
    if not os.path.exists('native_browser_analysis.json'):
        print("⚠️ 未找到分析文件，请先运行 native_browser_analyzer.py")
        return

    simulator = NativeEnvironmentSimulator()

    # 显示模板信息
    template = simulator.template_data
    print(f"📊 使用模板: {template.get('browser_type', 'default')}")
    print(f"📊 模板评分: {template.get('score', 0)}")
    print(f"📊 源路径: {template.get('source_path', 'default')}")

    # 创建测试配置
    test_profile = "native_sim_test"
    target_dir = "simulated_profiles"

    profile_path = simulator.create_simulated_profile(test_profile, target_dir)

    print(f"\n✅ 测试配置创建完成!")
    print(f"📁 配置路径: {profile_path}")
    print(f"💡 可以在browser_manager.py中使用此配置进行测试")

if __name__ == "__main__":
    main()

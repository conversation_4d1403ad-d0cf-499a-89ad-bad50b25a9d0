/**
 * 用户状态管理 - 使用Zustand
 */
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { getApiBaseUrlSync } from '../config';

export interface UserInfo {
  id: number;
  username: string;
  time_quota: number;
  time_count: number;
  created_at: string;
  lastusetime?: string;
}

interface UserStore {
  // 状态
  isLoggedIn: boolean;
  userInfo: UserInfo | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;

  // 操作
  login: (username: string, password: string) => Promise<boolean>;
  register: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  refreshUserInfo: () => Promise<void>;
  recharge: (activationCode: string) => Promise<boolean>;
  clearError: () => void;
}

// API请求封装
const apiRequest = async (endpoint: string, options: RequestInit = {}) => {
  // 动态获取API基础URL，确保使用最新配置
  const API_BASE_URL = getApiBaseUrlSync();

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  const data = await response.json();

  if (!response.ok) {
    throw new Error(data.message || '请求失败');
  }

  return data;
};

// 带认证的API请求
const authenticatedRequest = async (endpoint: string, token: string, options: RequestInit = {}) => {
  return apiRequest(endpoint, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers,
    },
  });
};

export const useUserStore = create<UserStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      isLoggedIn: false,
      userInfo: null,
      token: null,
      isLoading: false,
      error: null,

      // 用户登录
      login: async (username: string, password: string) => {
        set({ isLoading: true, error: null });

        try {
          const response = await apiRequest('/auth/login', {
            method: 'POST',
            body: JSON.stringify({ username, password }),
          });

          if (response.success) {
            set({
              isLoggedIn: true,
              userInfo: response.user,
              token: response.token,
              isLoading: false,
              error: null,
            });
            return true;
          } else {
            set({ error: response.message, isLoading: false });
            return false;
          }
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          return false;
        }
      },

      // 用户注册
      register: async (username: string, password: string) => {
        set({ isLoading: true, error: null });

        try {
          const response = await apiRequest('/auth/register', {
            method: 'POST',
            body: JSON.stringify({ username, password }),
          });

          if (response.success) {
            set({ isLoading: false, error: null });
            return true;
          } else {
            set({ error: response.message, isLoading: false });
            return false;
          }
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          return false;
        }
      },

      // 用户登出
      logout: () => {
        set({
          isLoggedIn: false,
          userInfo: null,
          token: null,
          error: null,
        });
      },

      // 刷新用户信息
      refreshUserInfo: async () => {
        const { token } = get();
        if (!token) {
          get().logout();
          return;
        }

        try {
          const response = await authenticatedRequest('/auth/profile', token);

          if (response.success) {
            set({ userInfo: response.user, error: null });
          } else {
            // 如果响应失败，检查是否是认证问题
            console.warn('用户信息获取失败:', response.message);
            // 只有在明确的认证错误时才自动登出
            if (response.message && (
                response.message.includes('登录') ||
                response.message.includes('token') ||
                response.message.includes('认证') ||
                response.message.includes('过期')
            )) {
              get().logout();
            }
          }
        } catch (error: any) {
          console.error('刷新用户信息失败:', error);

          // 检查HTTP状态码和错误信息，只有在明确的认证错误时才自动登出
          const isAuthError = error.message.includes('401') ||
                             error.message.includes('403') ||
                             error.message.includes('Unauthorized') ||
                             error.message.includes('token') ||
                             error.message.includes('认证失败') ||
                             error.message.includes('请先登录');

          if (isAuthError) {
            console.log('检测到认证错误，自动登出');
            get().logout();
          } else {
            // 对于网络错误、服务器错误等，不自动登出，只记录错误
            console.log('网络或服务器错误，保持登录状态:', error.message);
            // 可以选择设置一个非致命错误状态，但不影响登录状态
            // set({ error: '获取用户信息失败，请稍后重试' });
          }

          // 不再重新抛出错误，避免影响调用者
        }
      },

      // 激活码充值
      recharge: async (activationCode: string) => {
        const { token } = get();
        if (!token) {
          set({ error: '请先登录' });
          return false;
        }

        set({ isLoading: true, error: null });

        try {
          const response = await authenticatedRequest('/recharge/activate', token, {
            method: 'POST',
            body: JSON.stringify({ activation_code: activationCode }),
            headers: {
              'Content-Type': 'application/json',
            },
          });

          if (response.success) {
            // 刷新用户信息以获取最新的次数
            await get().refreshUserInfo();
            set({ isLoading: false });
            return true;
          } else {
            set({ error: response.message, isLoading: false });
            return false;
          }
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
          return false;
        }
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      },

      // 注意：用户次数的扣减由后端在任务完成时处理
      // 前端通过 refreshUserInfo() 获取最新的用户信息
    }),
    {
      name: 'user-storage', // 本地存储的key
      partialize: (state) => ({
        isLoggedIn: state.isLoggedIn,
        userInfo: state.userInfo,
        token: state.token,
      }), // 只持久化这些字段
    }
  )
);

// 导出用户状态和操作的hooks
export const useUserState = () => {
  const store = useUserStore();
  return {
    isLoggedIn: store.isLoggedIn,
    userInfo: store.userInfo,
    isLoading: store.isLoading,
    error: store.error,
  };
};

export const useUserActions = () => {
  const store = useUserStore();
  return {
    login: store.login,
    register: store.register,
    logout: store.logout,
    refreshUserInfo: store.refreshUserInfo,
    recharge: store.recharge,
    clearError: store.clearError,
  };
};

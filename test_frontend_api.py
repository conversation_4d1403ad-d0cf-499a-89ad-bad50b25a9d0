#!/usr/bin/env python3
"""
测试前端API调用，检查任务状态返回数据
"""

import requests
import json
from datetime import datetime

def test_queue_api():
    """测试队列API"""
    print("🔍 测试队列API (/api/queue/list)")
    
    try:
        # 使用测试token（会失败，但能看到响应）
        headers = {'Authorization': 'Bearer test_token'}
        response = requests.get('https://aug8.xyz/api/queue/list', 
                              headers=headers, timeout=15)
        
        print(f"   📊 状态码: {response.status_code}")
        print(f"   📝 响应头: {dict(response.headers)}")
        
        if response.status_code == 401:
            print("   ⚠️ 认证失败（预期的）")
            try:
                data = response.json()
                print(f"   📄 响应内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except:
                print(f"   📄 响应内容: {response.text}")
        elif response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   ❌ 其他错误: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")

def test_task_status_api():
    """测试任务状态API"""
    print("\n🔍 测试任务状态API (/api/task/2/status)")
    
    try:
        # 使用测试token（会失败，但能看到响应）
        headers = {'Authorization': 'Bearer test_token'}
        response = requests.get('https://aug8.xyz/api/task/2/status', 
                              headers=headers, timeout=15)
        
        print(f"   📊 状态码: {response.status_code}")
        
        if response.status_code == 401:
            print("   ⚠️ 认证失败（预期的）")
            try:
                data = response.json()
                print(f"   📄 响应内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except:
                print(f"   📄 响应内容: {response.text}")
        elif response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   ❌ 其他错误: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")

def test_system_status_api():
    """测试系统状态API"""
    print("\n🔍 测试系统状态API (/api/system/status)")
    
    try:
        response = requests.get('https://aug8.xyz/api/system/status', timeout=15)
        
        print(f"   📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"   ❌ 错误响应: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")

def check_frontend_local_storage():
    """检查前端可能的本地存储问题"""
    print("\n🔍 前端状态分析:")
    print("   📋 根据日志分析:")
    print("   - 任务ID: 2")
    print("   - 邮箱: <EMAIL>") 
    print("   - 前端状态: pending")
    print("   - 前端进度: 20%")
    print("   - 数据库状态: completed")
    print("   - 数据库完成时间: 2025-07-09 20:36:57")
    print()
    print("   🎯 问题分析:")
    print("   1. 数据库中任务已完成，但前端显示pending")
    print("   2. 前端可能使用了缓存的旧数据")
    print("   3. 刷新队列API可能没有正确更新任务状态")
    print("   4. 前端状态映射可能有问题")
    print()
    print("   💡 可能的原因:")
    print("   - 前端localStorage中有旧的任务数据")
    print("   - 队列API返回的数据格式不正确")
    print("   - 前端状态更新逻辑有bug")
    print("   - 认证问题导致无法获取最新数据")

def simulate_frontend_refresh():
    """模拟前端刷新操作"""
    print("\n🔄 模拟前端刷新操作:")
    
    # 1. 系统状态
    print("1️⃣ 获取系统状态...")
    test_system_status_api()
    
    # 2. 队列状态  
    print("\n2️⃣ 获取队列状态...")
    test_queue_api()
    
    # 3. 特定任务状态
    print("\n3️⃣ 获取任务2状态...")
    test_task_status_api()

def main():
    """主函数"""
    print("🧪 前端API测试工具")
    print("="*60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"目标域名: aug8.xyz")
    print("="*60)
    
    # 模拟前端刷新
    simulate_frontend_refresh()
    
    # 分析问题
    check_frontend_local_storage()
    
    print("\n" + "="*60)
    print("🎯 建议的解决方案:")
    print("1. 清除浏览器localStorage中的任务数据")
    print("2. 检查前端认证token是否有效")
    print("3. 确认队列API返回正确的任务状态")
    print("4. 检查前端状态映射逻辑")
    print("5. 强制刷新前端页面")
    print("="*60)

if __name__ == "__main__":
    main()

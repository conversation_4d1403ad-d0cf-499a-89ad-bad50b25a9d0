# Cloudflare Tunnel 优化配置文件
# 请将此文件内容复制到 C:\Users\<USER>\.cloudflared\config.yml

# Tunnel基本信息
tunnel: aug8
credentials-file: C:\Users\<USER>\.cloudflared\5a9d9f6f-36cc-4206-92ce-af7151654ec3.json

# 使用HTTP/2协议替代QUIC，解决连接超时问题
protocol: http2

# 网络优化配置
retries: 5
heartbeat-interval: 30s
grace-period: 30s
no-autoupdate: true
edge-ip-version: auto

# 日志级别
loglevel: info

# 入口规则配置
ingress:
  # API路由 - 转发 /api/* 到后端服务器 (端口5001)
  - hostname: aug8.xyz
    path: /api/*
    service: http://localhost:5001
    originRequest:
      # 增加超时时间，提高稳定性
      noTLSVerify: true
      connectTimeout: 60s
      tlsTimeout: 60s
      tcpKeepAlive: 60s
      keepAliveTimeout: 120s
      # 使用HTTP/2连接到本地服务
      http2Origin: true

  # 前端路由 - 转发所有其他请求到前端服务器 (端口3000)
  - hostname: aug8.xyz
    service: http://localhost:3000
    originRequest:
      noTLSVerify: true
      connectTimeout: 60s
      tlsTimeout: 60s
      tcpKeepAlive: 60s
      keepAliveTimeout: 120s
      http2Origin: true

  # 默认规则 (必需) - 返回404
  - service: http_status:404

/**
 * 配置管理
 * 统一管理开发和生产环境的配置
 */

// 从根目录读取配置文件
const loadConfig = async () => {
  try {
    // 在开发环境中，从public目录读取配置文件
    // 在生产环境中，配置文件应该被复制到build目录
    const response = await fetch('/config.json');
    const configData = await response.json();

    // 根据环境变量决定使用哪个配置
    const env = process.env.NODE_ENV === 'production' ? 'production' : 'development';
    return configData[env];
  } catch (error) {
    console.warn('加载配置文件失败，使用默认配置:', error);
    // 返回默认配置 - 使用相对路径
    return {
      api: {
        host: window.location.hostname,
        port: 5001,
        base_url: '/api'
      }
    };
  }
};

// 配置缓存
let configCache: any = null;

// 获取配置
export const getConfig = async () => {
  if (!configCache) {
    configCache = await loadConfig();
  }
  return configCache;
};

// 获取API基础URL
export const getApiBaseUrl = async (): Promise<string> => {
  const config = await getConfig();

  // 如果配置中有完整的URL信息，构建完整URL
  if (config.api.host && config.api.port) {
    const protocol = config.api.protocol || (config.api.port === 443 ? 'https' : 'http');
    const port = config.api.port === 443 || config.api.port === 80 ? '' : `:${config.api.port}`;
    const baseUrl = config.api.base_url || '/api';

    return `${protocol}://${config.api.host}${port}${baseUrl}`;
  }

  // 兼容旧的相对路径配置
  let baseUrl = config.api.base_url;
  if (baseUrl.startsWith('/')) {
    const currentProtocol = window.location.protocol;
    const currentHost = window.location.hostname;
    const apiPort = config.api.port || 5001;
    const port = apiPort === 443 || apiPort === 80 ? '' : `:${apiPort}`;
    baseUrl = `${currentProtocol}//${currentHost}${port}${baseUrl}`;
  }

  return baseUrl;
};

// 同步获取API基础URL（用于初始化时）
export const getApiBaseUrlSync = (): string => {
  // 如果配置已缓存，使用配置构建URL
  if (configCache) {
    // 如果配置中有完整的URL信息，构建完整URL
    if (configCache.api.host && configCache.api.port) {
      const protocol = configCache.api.protocol || (configCache.api.port === 443 ? 'https' : 'http');
      const port = configCache.api.port === 443 || configCache.api.port === 80 ? '' : `:${configCache.api.port}`;
      const baseUrl = configCache.api.base_url || '/api';

      return `${protocol}://${configCache.api.host}${port}${baseUrl}`;
    }

    // 兼容旧的相对路径配置
    let baseUrl = configCache.api.base_url;
    if (baseUrl.startsWith('/')) {
      const currentProtocol = window.location.protocol;
      const currentHost = window.location.hostname;
      const apiPort = configCache.api.port || 5001;
      const port = apiPort === 443 || apiPort === 80 ? '' : `:${apiPort}`;
      baseUrl = `${currentProtocol}//${currentHost}${port}${baseUrl}`;
    }

    return baseUrl;
  }

  // 默认构建API地址
  const currentProtocol = window.location.protocol;
  const currentHost = window.location.hostname;

  // 在生产环境中（非localhost），不添加端口号
  if (currentHost !== 'localhost' && currentHost !== '127.0.0.1') {
    return `${currentProtocol}//${currentHost}/api`;
  }

  // 开发环境添加端口号
  const apiPort = parseInt(process.env.REACT_APP_API_PORT || '5001');
  const port = apiPort === 443 || apiPort === 80 ? '' : `:${apiPort}`;

  return `${currentProtocol}//${currentHost}${port}/api`;
};

// 初始化配置（在应用启动时调用）
export const initConfig = async () => {
  await getConfig();
  console.log('配置已加载:', configCache);
};

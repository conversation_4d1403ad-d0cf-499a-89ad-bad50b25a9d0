#!/usr/bin/env python3
"""
生产服务器启动脚本
专门用于启动高性能的API服务器
"""
import os
import sys
import time
import subprocess
import requests
from datetime import datetime

def check_port_available(port):
    """检查端口是否可用"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('127.0.0.1', port))
            return True
    except OSError:
        return False

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖...")
    
    try:
        import waitress
        print("✅ Waitress已安装")
    except ImportError:
        print("❌ Waitress未安装，正在安装...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'waitress'])
        print("✅ Waitress安装完成")
    
    # 检查其他必要文件
    required_files = [
        'production_server.py',
        'config.json',
        'sqlite_pool.py',
        'auth_manager.py',
        'config_manager.py',
        'browser_manager.py',
        'automation_engine.py',
        'profile_usage_tracker.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有依赖检查通过")
    return True

def test_api_server(port=5001, max_retries=30):
    """测试API服务器是否正常启动"""
    print(f"🧪 测试API服务器 (端口:{port})...")
    
    for i in range(max_retries):
        try:
            response = requests.get(f'http://localhost:{port}/api/test', timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ API服务器测试通过 (耗时: {i+1}秒)")
                    print(f"📊 服务器信息: {data.get('message', 'N/A')}")
                    return True
        except requests.exceptions.RequestException:
            pass
        
        if i < max_retries - 1:
            print(f"⏳ 等待服务器启动... ({i+1}/{max_retries})")
            time.sleep(1)
    
    print("❌ API服务器测试失败")
    return False

def show_status():
    """显示服务器状态"""
    print("\n" + "="*60)
    print("🚀 生产服务器状态")
    print("="*60)
    
    # 检查端口占用
    port_5001 = not check_port_available(5001)
    port_5002 = not check_port_available(5002)
    
    print(f"📍 端口 5001 (生产服务器): {'🟢 运行中' if port_5001 else '🔴 未使用'}")
    print(f"📍 端口 5002 (管理界面):   {'🟢 运行中' if port_5002 else '🔴 未使用'}")
    
    # 测试API连接
    if port_5001:
        try:
            response = requests.get('http://localhost:5001/api/test', timeout=3)
            if response.status_code == 200:
                print("🌐 API服务: ✅ 正常响应")
            else:
                print(f"🌐 API服务: ⚠️ 异常响应 (状态码: {response.status_code})")
        except:
            print("🌐 API服务: ❌ 连接失败")
    
    print("\n💡 使用说明:")
    print("  • 生产服务器: 处理前端API请求，24/7运行")
    print("  • 管理界面: 按需启动，用于账号和配置管理")
    print("  • 启动管理界面: python autoback.py")
    print("="*60)

def main():
    """主函数"""
    print("🚀 生产服务器启动器")
    print("="*60)
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，请解决后重试")
        return
    
    # 检查端口
    if not check_port_available(5001):
        print("⚠️ 端口 5001 已被占用")
        choice = input("是否查看当前状态? (y/n): ").lower().strip()
        if choice == 'y':
            show_status()
        return
    
    print("✅ 端口 5001 可用")
    print("\n🚀 启动生产服务器...")
    print("💡 提示: 按 Ctrl+C 停止服务器")
    print("="*60)
    
    try:
        # 启动生产服务器
        process = subprocess.Popen(
            [sys.executable, 'production_server.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 等待服务器启动
        time.sleep(3)
        
        # 测试服务器
        if test_api_server():
            print("\n🎉 生产服务器启动成功!")
            print("📍 API地址: http://localhost:5001")
            print("🔗 测试地址: http://localhost:5001/api/test")
            print("\n💡 管理提示:")
            print("  • 账号管理: 在新终端运行 'python autoback.py'")
            print("  • 状态查看: 在新终端运行 'python start_production.py --status'")
            print("  • 停止服务: 按 Ctrl+C")
            
            # 实时显示日志
            print("\n📋 服务器日志:")
            print("-"*60)
            
            try:
                for line in process.stdout:
                    print(line.strip())
            except KeyboardInterrupt:
                print("\n🔄 正在停止服务器...")
                process.terminate()
                process.wait()
                print("✅ 服务器已停止")
        else:
            print("❌ 生产服务器启动失败")
            process.terminate()
            
    except KeyboardInterrupt:
        print("\n🔄 用户中断启动")
    except Exception as e:
        print(f"❌ 启动异常: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--status':
        show_status()
    else:
        main()

"""
系统监控脚本
监控autoback.py的运行状态，提供详细的诊断信息
"""
import time
import requests
import psutil
import threading
import json
from datetime import datetime, timedelta


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, api_base="http://localhost:5001"):
        self.api_base = api_base
        self.monitoring = False
        self.stats = {
            'start_time': time.time(),
            'requests': [],
            'system_snapshots': [],
            'alerts': []
        }
    
    def get_system_snapshot(self):
        """获取系统快照"""
        try:
            # 系统资源
            memory = psutil.virtual_memory()
            cpu = psutil.cpu_percent(interval=0.1)
            
            # 查找autoback.py进程
            autoback_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info', 'cpu_percent', 'create_time']):
                try:
                    if proc.info['cmdline']:
                        cmdline = ' '.join(proc.info['cmdline'])
                        if 'autoback.py' in cmdline:
                            autoback_processes.append({
                                'pid': proc.pid,
                                'memory_mb': proc.info['memory_info'].rss / 1024 / 1024,
                                'cpu_percent': proc.cpu_percent(),
                                'uptime_hours': (time.time() - proc.info['create_time']) / 3600
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # 网络连接
            connections = []
            for conn in psutil.net_connections():
                if conn.laddr.port == 5001:
                    connections.append({
                        'status': conn.status,
                        'local_addr': f"{conn.laddr.ip}:{conn.laddr.port}",
                        'remote_addr': f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else None
                    })
            
            return {
                'timestamp': time.time(),
                'system': {
                    'memory_percent': memory.percent,
                    'memory_used_gb': memory.used / 1024 / 1024 / 1024,
                    'cpu_percent': cpu
                },
                'autoback_processes': autoback_processes,
                'network_connections': connections
            }
            
        except Exception as e:
            return {
                'timestamp': time.time(),
                'error': str(e)
            }
    
    def test_api_endpoints(self):
        """测试API端点"""
        endpoints = [
            '/api/health',
            '/api/announcements',
            '/api/system/status',
            '/api/test'
        ]
        
        results = {}
        
        for endpoint in endpoints:
            try:
                start_time = time.time()
                response = requests.get(f"{self.api_base}{endpoint}", timeout=10)
                elapsed = time.time() - start_time
                
                results[endpoint] = {
                    'status_code': response.status_code,
                    'response_time': elapsed,
                    'success': response.status_code == 200,
                    'content_length': len(response.content) if response.content else 0
                }
                
                # 检查特定端点的响应内容
                if endpoint == '/api/health' and response.status_code == 200:
                    try:
                        data = response.json()
                        results[endpoint]['health_data'] = data
                    except:
                        pass
                
            except requests.exceptions.Timeout:
                results[endpoint] = {
                    'status_code': 0,
                    'response_time': 10.0,
                    'success': False,
                    'error': 'timeout'
                }
            except requests.exceptions.ConnectionError:
                results[endpoint] = {
                    'status_code': 0,
                    'response_time': 0,
                    'success': False,
                    'error': 'connection_error'
                }
            except Exception as e:
                results[endpoint] = {
                    'status_code': 0,
                    'response_time': 0,
                    'success': False,
                    'error': str(e)
                }
        
        return results
    
    def analyze_performance(self):
        """分析性能趋势"""
        if len(self.stats['requests']) < 2:
            return None
        
        recent_requests = self.stats['requests'][-10:]  # 最近10次请求
        
        # 计算平均响应时间
        response_times = []
        success_count = 0
        
        for req in recent_requests:
            for endpoint, result in req['results'].items():
                if result.get('success'):
                    response_times.append(result['response_time'])
                    success_count += 1
        
        if not response_times:
            return None
        
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        
        # 分析系统资源趋势
        recent_snapshots = self.stats['system_snapshots'][-5:]  # 最近5次快照
        
        if len(recent_snapshots) >= 2:
            memory_trend = []
            cpu_trend = []
            
            for snapshot in recent_snapshots:
                if 'system' in snapshot:
                    memory_trend.append(snapshot['system']['memory_percent'])
                    cpu_trend.append(snapshot['system']['cpu_percent'])
            
            memory_increasing = len(memory_trend) >= 3 and all(
                memory_trend[i] <= memory_trend[i+1] for i in range(len(memory_trend)-1)
            )
            
            return {
                'avg_response_time': avg_response_time,
                'max_response_time': max_response_time,
                'success_rate': success_count / (len(recent_requests) * 4),  # 4个端点
                'memory_increasing': memory_increasing,
                'current_memory': memory_trend[-1] if memory_trend else 0,
                'current_cpu': cpu_trend[-1] if cpu_trend else 0
            }
        
        return {
            'avg_response_time': avg_response_time,
            'max_response_time': max_response_time,
            'success_rate': success_count / (len(recent_requests) * 4)
        }
    
    def check_alerts(self, performance):
        """检查告警条件"""
        alerts = []
        
        if performance:
            # 响应时间告警
            if performance['avg_response_time'] > 5.0:
                alerts.append({
                    'level': 'warning',
                    'message': f"平均响应时间过长: {performance['avg_response_time']:.1f}s"
                })
            
            if performance['max_response_time'] > 10.0:
                alerts.append({
                    'level': 'critical',
                    'message': f"最大响应时间过长: {performance['max_response_time']:.1f}s"
                })
            
            # 成功率告警
            if performance['success_rate'] < 0.9:
                alerts.append({
                    'level': 'critical',
                    'message': f"API成功率过低: {performance['success_rate']*100:.1f}%"
                })
            
            # 内存告警
            if performance.get('current_memory', 0) > 80:
                alerts.append({
                    'level': 'warning',
                    'message': f"内存使用过高: {performance['current_memory']:.1f}%"
                })
            
            if performance.get('memory_increasing'):
                alerts.append({
                    'level': 'warning',
                    'message': "检测到内存使用持续增长，可能存在内存泄漏"
                })
        
        # 保存告警
        for alert in alerts:
            alert['timestamp'] = time.time()
            self.stats['alerts'].append(alert)
        
        # 只保留最近100个告警
        if len(self.stats['alerts']) > 100:
            self.stats['alerts'] = self.stats['alerts'][-100:]
        
        return alerts
    
    def print_status_report(self):
        """打印状态报告"""
        print("\n" + "=" * 60)
        print(f"📊 系统监控报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # 系统快照
        snapshot = self.get_system_snapshot()
        if 'error' not in snapshot:
            print(f"💾 系统内存: {snapshot['system']['memory_percent']:.1f}% "
                  f"({snapshot['system']['memory_used_gb']:.1f}GB)")
            print(f"🖥️ CPU使用: {snapshot['system']['cpu_percent']:.1f}%")
            
            if snapshot['autoback_processes']:
                for proc in snapshot['autoback_processes']:
                    print(f"🐍 autoback.py: PID={proc['pid']}, "
                          f"内存={proc['memory_mb']:.1f}MB, "
                          f"运行时间={proc['uptime_hours']:.1f}小时")
            else:
                print("❌ 未找到autoback.py进程")
            
            print(f"🌐 网络连接: {len(snapshot['network_connections'])} 个")
        
        # API测试结果
        api_results = self.test_api_endpoints()
        print(f"\n🔍 API端点测试:")
        for endpoint, result in api_results.items():
            status = "✅" if result['success'] else "❌"
            print(f"  {endpoint}: {status} {result['response_time']*1000:.0f}ms")
        
        # 性能分析
        performance = self.analyze_performance()
        if performance:
            print(f"\n📈 性能分析:")
            print(f"  平均响应时间: {performance['avg_response_time']*1000:.0f}ms")
            print(f"  最大响应时间: {performance['max_response_time']*1000:.0f}ms")
            print(f"  成功率: {performance['success_rate']*100:.1f}%")
        
        # 告警信息
        alerts = self.check_alerts(performance)
        if alerts:
            print(f"\n🚨 当前告警:")
            for alert in alerts:
                level_icon = "🔴" if alert['level'] == 'critical' else "🟡"
                print(f"  {level_icon} {alert['message']}")
        else:
            print(f"\n✅ 无告警")
        
        # 保存数据
        self.stats['requests'].append({
            'timestamp': time.time(),
            'results': api_results
        })
        self.stats['system_snapshots'].append(snapshot)
        
        # 只保留最近50次记录
        if len(self.stats['requests']) > 50:
            self.stats['requests'] = self.stats['requests'][-50:]
        if len(self.stats['system_snapshots']) > 50:
            self.stats['system_snapshots'] = self.stats['system_snapshots'][-50:]
    
    def continuous_monitoring(self, interval=60):
        """持续监控"""
        print(f"🚀 开始持续监控 (间隔: {interval}秒)")
        print("按 Ctrl+C 停止监控")
        
        self.monitoring = True
        
        try:
            while self.monitoring:
                self.print_status_report()
                time.sleep(interval)
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断监控")
        finally:
            self.monitoring = False
            print("🔚 监控结束")
    
    def save_report(self, filename=None):
        """保存监控报告"""
        if not filename:
            filename = f"monitor_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, indent=2, ensure_ascii=False)
            print(f"📄 监控报告已保存: {filename}")
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")


def main():
    """主函数"""
    print("🚀 autoback.py系统监控")
    print("=" * 50)
    
    monitor = SystemMonitor()
    
    print("\n选择监控模式:")
    print("1. 单次检查")
    print("2. 持续监控 (60秒间隔)")
    print("3. 快速监控 (10秒间隔)")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == '1':
            monitor.print_status_report()
            
        elif choice == '2':
            monitor.continuous_monitoring(60)
            
        elif choice == '3':
            monitor.continuous_monitoring(10)
            
        else:
            print("❌ 无效选择")
            return
        
        # 询问是否保存报告
        save = input("\n是否保存监控报告? (y/n): ").lower().strip()
        if save == 'y':
            monitor.save_report()
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except Exception as e:
        print(f"❌ 监控异常: {e}")


if __name__ == "__main__":
    main()

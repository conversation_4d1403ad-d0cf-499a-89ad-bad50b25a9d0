"""
后端健康监控和自动恢复系统
监控autoback.py的健康状态，在检测到无响应时自动恢复
"""
import time
import requests
import threading
import psutil
import subprocess
import os
import signal
from datetime import datetime
import json


class BackendHealthMonitor:
    """后端健康监控器"""

    def __init__(self, api_base="http://localhost:5001", check_interval=30):
        self.api_base = api_base
        self.check_interval = check_interval
        self.monitoring = False

        # 健康状态
        self.consecutive_failures = 0
        self.max_failures = 3  # 连续3次失败就重启
        self.last_success_time = time.time()

        # 统计信息
        self.stats = {
            'total_checks': 0,
            'successful_checks': 0,
            'failed_checks': 0,
            'restarts': 0,
            'start_time': time.time()
        }

    def check_backend_health(self):
        """检查后端健康状态"""
        try:
            # 1. 基本连接测试
            start_time = time.time()
            response = requests.get(f"{self.api_base}/api/test", timeout=10)
            response_time = time.time() - start_time

            if response.status_code == 200:
                # 2. 检查响应时间
                if response_time > 5.0:
                    return {
                        'healthy': False,
                        'reason': f'响应时间过长: {response_time:.1f}s',
                        'response_time': response_time
                    }

                # 3. 检查响应内容
                try:
                    data = response.json()
                    if not data.get('success'):
                        return {
                            'healthy': False,
                            'reason': f'后端报告状态异常: {data.get("message", "unknown")}',
                            'response_time': response_time
                        }
                except:
                    return {
                        'healthy': False,
                        'reason': '响应格式异常',
                        'response_time': response_time
                    }

                return {
                    'healthy': True,
                    'response_time': response_time,
                    'data': data
                }
            else:
                return {
                    'healthy': False,
                    'reason': f'HTTP错误: {response.status_code}',
                    'response_time': response_time
                }

        except requests.exceptions.Timeout:
            return {
                'healthy': False,
                'reason': '请求超时',
                'response_time': 10.0
            }
        except requests.exceptions.ConnectionError:
            return {
                'healthy': False,
                'reason': '连接失败',
                'response_time': 0
            }
        except Exception as e:
            return {
                'healthy': False,
                'reason': f'检查异常: {e}',
                'response_time': 0
            }

    def check_system_resources(self):
        """检查系统资源使用"""
        try:
            # 查找autoback.py进程
            autoback_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info', 'cpu_percent']):
                try:
                    if proc.info['cmdline']:
                        cmdline = ' '.join(proc.info['cmdline'])
                        if 'autoback.py' in cmdline:
                            autoback_processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if not autoback_processes:
                return {
                    'process_running': False,
                    'reason': 'autoback.py进程未找到'
                }

            # 检查进程资源使用
            for proc in autoback_processes:
                try:
                    memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                    cpu_percent = proc.cpu_percent()

                    # 检查资源使用是否异常
                    if memory_mb > 1000:  # 超过1GB内存
                        return {
                            'process_running': True,
                            'healthy': False,
                            'reason': f'内存使用过高: {memory_mb:.1f}MB',
                            'pid': proc.pid
                        }

                    if cpu_percent > 90:  # CPU使用超过90%
                        return {
                            'process_running': True,
                            'healthy': False,
                            'reason': f'CPU使用过高: {cpu_percent:.1f}%',
                            'pid': proc.pid
                        }

                    return {
                        'process_running': True,
                        'healthy': True,
                        'pid': proc.pid,
                        'memory_mb': memory_mb,
                        'cpu_percent': cpu_percent
                    }

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            return {
                'process_running': False,
                'reason': '无法获取进程信息'
            }

        except Exception as e:
            return {
                'process_running': False,
                'reason': f'系统检查异常: {e}'
            }

    def restart_backend(self):
        """重启后端服务"""
        try:
            self.log("🔄 开始重启后端服务...")

            # 1. 查找并终止现有进程
            terminated_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['cmdline']:
                        cmdline = ' '.join(proc.info['cmdline'])
                        if 'autoback.py' in cmdline:
                            self.log(f"🔄 终止进程 PID: {proc.pid}")
                            proc.terminate()
                            terminated_processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # 2. 等待进程优雅退出
            for proc in terminated_processes:
                try:
                    proc.wait(timeout=10)
                    self.log(f"✅ 进程 {proc.pid} 已优雅退出")
                except psutil.TimeoutExpired:
                    self.log(f"⚠️ 进程 {proc.pid} 退出超时，强制终止")
                    proc.kill()

            # 3. 等待端口释放
            time.sleep(5)

            # 4. 启动新进程
            self.log("🚀 启动新的autoback.py进程...")

            # 在当前目录启动autoback.py
            process = subprocess.Popen(
                ['python', 'autoback.py'],
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
            )

            # 5. 等待服务启动
            self.log("⏳ 等待服务启动...")
            for i in range(30):  # 等待最多30秒
                time.sleep(1)
                try:
                    response = requests.get(f"{self.api_base}/api/test", timeout=5)
                    if response.status_code == 200:
                        self.log(f"✅ 后端服务重启成功 (耗时 {i+1}秒)")
                        self.stats['restarts'] += 1
                        return True
                except:
                    continue

            self.log("❌ 后端服务重启失败")
            return False

        except Exception as e:
            self.log(f"❌ 重启后端服务异常: {e}")
            return False

    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        print(log_message)

        # 保存到日志文件
        try:
            with open('backend_monitor.log', 'a', encoding='utf-8') as f:
                f.write(log_message + '\n')
        except:
            pass

    def monitor_loop(self):
        """监控循环"""
        self.log("🚀 开始监控后端健康状态")
        self.log(f"📊 检查间隔: {self.check_interval}秒")
        self.log(f"🎯 监控目标: {self.api_base}")

        self.monitoring = True

        while self.monitoring:
            try:
                self.stats['total_checks'] += 1

                # 检查后端健康状态
                health_result = self.check_backend_health()

                if health_result['healthy']:
                    self.consecutive_failures = 0
                    self.last_success_time = time.time()
                    self.stats['successful_checks'] += 1

                    response_time = health_result.get('response_time', 0)
                    self.log(f"✅ 后端健康 (响应时间: {response_time*1000:.0f}ms)")

                else:
                    self.consecutive_failures += 1
                    self.stats['failed_checks'] += 1

                    reason = health_result.get('reason', '未知原因')
                    self.log(f"❌ 后端异常 (连续失败: {self.consecutive_failures}): {reason}")

                    # 检查是否需要重启
                    if self.consecutive_failures >= self.max_failures:
                        self.log(f"🚨 连续 {self.consecutive_failures} 次失败，触发自动重启")

                        # 检查系统资源
                        resource_check = self.check_system_resources()
                        if resource_check.get('process_running'):
                            self.log(f"📊 进程状态: {resource_check}")

                        # 执行重启
                        if self.restart_backend():
                            self.consecutive_failures = 0
                            self.last_success_time = time.time()
                        else:
                            self.log("❌ 自动重启失败，继续监控")

                # 每10次检查输出统计信息
                if self.stats['total_checks'] % 10 == 0:
                    uptime = time.time() - self.stats['start_time']
                    success_rate = (self.stats['successful_checks'] / self.stats['total_checks']) * 100

                    self.log(f"📊 统计: 运行{uptime/3600:.1f}小时, 检查{self.stats['total_checks']}次, "
                           f"成功率{success_rate:.1f}%, 重启{self.stats['restarts']}次")

                time.sleep(self.check_interval)

            except KeyboardInterrupt:
                self.log("⚠️ 用户中断监控")
                break
            except Exception as e:
                self.log(f"❌ 监控循环异常: {e}")
                time.sleep(self.check_interval)

        self.monitoring = False
        self.log("🔚 监控结束")

    def start_monitoring(self):
        """启动监控"""
        if not self.monitoring:
            monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
            monitor_thread.start()
            return monitor_thread
        return None

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False


def main():
    """主函数"""
    print("🚀 后端健康监控系统")
    print("=" * 50)

    monitor = BackendHealthMonitor(check_interval=30)  # 30秒检查一次

    try:
        # 初始健康检查
        print("🔍 执行初始健康检查...")
        health = monitor.check_backend_health()

        if health['healthy']:
            print(f"✅ 后端当前状态健康 (响应时间: {health['response_time']*1000:.0f}ms)")
        else:
            print(f"❌ 后端当前状态异常: {health['reason']}")

            # 询问是否立即重启
            restart = input("是否立即重启后端? (y/n): ").lower().strip()
            if restart == 'y':
                monitor.restart_backend()

        # 启动持续监控
        print("\n🔄 启动持续监控...")
        print("按 Ctrl+C 停止监控")

        monitor.monitor_loop()

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except Exception as e:
        print(f"❌ 监控异常: {e}")

    print("🔚 监控结束")


if __name__ == "__main__":
    main()

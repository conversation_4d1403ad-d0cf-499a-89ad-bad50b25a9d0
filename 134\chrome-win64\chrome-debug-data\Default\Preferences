{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "announcement_notification_service_first_run_time": "*****************", "apps": {"shortcuts_arch": "", "shortcuts_version": 0}, "autocomplete": {"retention_policy_last_version": 134}, "autofill": {"last_version_deduped": 134}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 864, "left": 87, "maximized": false, "right": 1061, "top": 34, "work_area_bottom": 1032, "work_area_left": 0, "work_area_right": 1920, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17230, "default_apps_install_state": 2, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "ef29764d-4432-4032-b991-2de275d4e59f", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "cws_info_fetch_error_timestamp": "*****************", "cws_info_timestamp": "13394873228463894", "last_chrome_version": "134.0.6998.35", "settings": {"abcdefghijklmnopqrstuvwxyzabcdef": {"active_permissions": {"api": ["storage", "contextMenus", "notifications"], "explicit_host": [], "manifest_permissions": []}, "creation_flags": 1, "from_webstore": false, "install_time": "13395901800000000", "location": 4, "manifest": {"background": {"service_worker": "background.js"}, "content_scripts": [{"all_frames": true, "js": ["data/content_script/page_context/inject.js"], "match_about_blank": true, "match_origin_as_fallback": true, "matches": ["*://*/*"], "run_at": "document_start", "world": "MAIN"}, {"all_frames": true, "js": ["data/content_script/inject.js"], "match_about_blank": true, "match_origin_as_fallback": true, "matches": ["*://*/*"], "run_at": "document_start", "world": "ISOLATED"}], "description": "Defending against Canvas fingerprinting by reporting a fake value.", "icons": {"16": "data/icons/16.png", "32": "data/icons/32.png", "48": "data/icons/48.png", "64": "data/icons/64.png", "128": "data/icons/128.png"}, "manifest_version": 3, "name": "Canvas Fingerprint Defender", "permissions": ["storage", "contextMenus", "notifications"], "version": "0.2.2"}, "path": "134/chrome-win64/chrome-debug-data/Default/Extensions/abcdefghijklmnopqrstuvwxyzabcdef/0.2.2_0", "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "timezone12345678901234567890123": {"active_permissions": {"api": ["storage", "scripting", "contextMenus", "webNavigation"], "explicit_host": ["*://*/*"], "manifest_permissions": []}, "creation_flags": 1, "from_webstore": false, "install_time": "13395901800000000", "location": 4, "manifest": {"background": {"service_worker": "background.js"}, "description": "Easily change your timezone to a desired value and protect your privacy.", "host_permissions": ["*://*/*"], "icons": {"16": "data/icons/16.png", "32": "data/icons/32.png", "48": "data/icons/48.png", "64": "data/icons/64.png", "128": "data/icons/128.png"}, "manifest_version": 3, "name": "Change Timezone (Time Shift)", "permissions": ["storage", "scripting", "contextMenus", "webNavigation"], "version": "0.1.7"}, "path": "134/chrome-win64/chrome-debug-data/Default/Extensions/timezone12345678901234567890123/0.1.7_0", "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "gaia_cookie": {"changed_time": **********.930106, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.googlechromefortesting.windows"}, "google": {"services": {"signin_scoped_device_id": "75c14693-d427-4c97-9f1a-291c6d5f038d"}}, "history_clusters": {"all_cache": {"all_keywords": {}, "all_timestamp": "*****************"}, "short_cache": {"short_keywords": {}, "short_timestamp": "0"}}, "https_upgrade_fallbacks": {"fallback_events": [], "heuristic_start_timestamp": "*****************"}, "in_product_help": {"recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************", "*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "zh-CN,zh"}, "media": {"device_id_salt": "********************************", "engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "Jd4JdMMUrdV2sQWk1NdSEyrptT9g1N3DXD/Pcrv1IdrDhPIYQWf5XTUZi2Ght2IH1FvwgMC6u6Is7gIV5dJRow=="}, "ntp": {"num_personal_suggestions": 2}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_INSIGHTS": true, "PRICE_TRACKING": true, "SAVED_TAB_GROUP": true, "SHOPPING_DISCOUNTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "plus_addresses": {"preallocation": {"addresses": [], "next": 0}}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true, "first_party_sets_enabled": false}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 8, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"https://www.douyin.com:443,*": {"last_modified": "13394872754940606", "setting": {"https://www.douyin.com/": {"couldShowBannerEvents": 1.338643068245185e+16, "next_install_text_animation": {"delay": "1382400000000", "last_shown": "13394872754940598"}}}}}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]augmentcode.com,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]douyin.com,*": {"last_modified": "*****************", "setting": {"entry_point_animated": true}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "media_engagement": {"https://login.augmentcode.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.augmentcode.com:443,*": {"expiration": "13403677780108210", "last_modified": "13395901780108211", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.douyin.com:443,*": {"expiration": "13403677795353914", "last_modified": "13395901795353916", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 1.3386431207350468e+16, "mediaPlaybacks": 1, "visits": 9}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13395901764807623", "setting": {"lastEngagementTime": 1.3395901764807618e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "https://login.augmentcode.com:443,*": {"last_modified": "13395901794093452", "setting": {"lastEngagementTime": 1.3395901794093448e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 2.1, "rawScore": 2.1}}, "https://www.augmentcode.com:443,*": {"last_modified": "13395901775633606", "setting": {"lastEngagementTime": 1.3395901775633604e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "https://www.douyin.com:443,*": {"last_modified": "13395901760535096", "setting": {"lastEngagementTime": 1.3395901760535086e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 76.73886389885647}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "134.0.6998.35", "creation_time": "13386430665634864", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "13395901794093447", "last_time_obsolete_http_credentials_removed": 1750399251.120521, "last_time_password_store_metrics_reported": 1751428188.765773, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "您的 Chromium", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13395901758", "scout_reporting_enabled_when_deprecated": false, "unhandled_sync_password_reuses": {}}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"deleted_group_ids": {}, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "CuUCChFjcm9zc19kZXZpY2VfdXNlchLPAgrDAg0AAIA/EJrG0/CG8OUXGrACCqcCGqQCChkNAACAPxISTm9Dcm9zc0RldmljZVVzYWdlChgNAAAAQBIRQ3Jvc3NEZXZpY2VNb2JpbGUKGQ0AAEBAEhJDcm9zc0RldmljZURlc2t0b3AKGA0AAIBAEhFDcm9zc0RldmljZVRhYmxldAoiDQAAoEASG0Nyb3NzRGV2aWNlTW9iaWxlQW5kRGVza3RvcAohDQAAwEASGkNyb3NzRGV2aWNlTW9iaWxlQW5kVGFibGV0CiINAADgQBIbQ3Jvc3NEZXZpY2VEZXNrdG9wQW5kVGFibGV0CiANAAAAQRIZQ3Jvc3NEZXZpY2VBbGxEZXZpY2VUeXBlcwoXDQAAEEESEENyb3NzRGV2aWNlT3RoZXISEk5vQ3Jvc3NEZXZpY2VVc2FnZRIEEAcYBCACELDG0/CG8OUXCmAKEXJlc3VtZV9oZWF2eV91c2VyEksKQA0AAAAAELP8rsKN0uUXGi4KJgokDQAAAD8SFlJlc3VtZUhlYXZ5VXNlclNlZ21lbnQaBU90aGVyEgQQDhgEIAIQ2vyuwo3S5RcKUgoNc2hvcHBpbmdfdXNlchJBCjYNAAAAABDtxdPwhvDlFxokChwKGg0AAAA/EgxTaG9wcGluZ1VzZXIaBU90aGVyEgQQAhgEIAMQ+sXT8Ibw5RcKcwoVcGFzc3dvcmRfbWFuYWdlcl91c2VyEloKTw0AAAAAELfH0/CG8OUXGj0KNQozDQAAAD8SE1Bhc3N3b3JkTWFuYWdlclVzZXIaF05vdF9QYXNzd29yZE1hbmFnZXJVc2VyEgQQBxgEIAEQy8fT8Ibw5RcKZAoLc2VhcmNoX3VzZXISVQpKDQAAAAAQ6sfT8Ibw5RcaOAowGi4KCg0AAIA/EgNMb3cKDQ0AAKBAEgZNZWRpdW0KCw0AALBBEgRIaWdoEgROb25lEgQQBxgEIAIQ/cfT8Ibw5RcKagoaY2hyb21lX2xvd191c2VyX2VuZ2FnZW1lbnQSTApBDQAAgD8QhsXT8Ibw5RcaLwonCiUNAAAAPxIXQ2hyb21lTG93VXNlckVuZ2FnZW1lbnQaBU90aGVyEgQQBxgEIAIQosXT8Ibw5Rc=", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13395801599000000", "uma_in_sql_start_time": "13386430665647228"}, "sessions": {"event_log": [{"crashed": false, "time": "13389735612976744", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13389735647148137", "type": 2, "window_count": 1}, {"crashed": false, "time": "13389735755082501", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13389735883479073", "type": 2, "window_count": 1}, {"crashed": false, "time": "13389735891457678", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13389735899924619", "type": 2, "window_count": 1}, {"crashed": false, "time": "13389735961841489", "type": 0}, {"crashed": true, "time": "13389883928434665", "type": 0}, {"crashed": true, "time": "13393101347633834", "type": 0}, {"restore_browser": true, "synchronous": false, "time": "13393101350560108", "type": 5}, {"errored_reading": false, "tab_count": 1, "time": "13393101350564380", "type": 1, "window_count": 1}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393102149143938", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393103092764629", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393104793709265", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394872748547868", "type": 0}, {"crashed": true, "time": "13394872791116259", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394878152240844", "type": 2, "window_count": 1}, {"crashed": false, "time": "13395901758759182", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13395901795344969", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_ignored_count_for_language": {"en": 1}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "updateclientdata": {"apps": {"mfhcmdonhekjhfbjmeacdjbhlfgpjabp": {"cohort": "1::", "cohortname": "", "dlrc": 6756, "installdate": 6648, "pf": "********-9e33-4e68-a880-9ad00e3e67cc"}, "ncennffkjdiamlpmcbajkmaiiiddgioo": {"cohort": "1::", "cohortname": "", "dlrc": 6756, "fp": "1.04d1268da7abac85bcb5330cf6aadaf364e25fa52a3d234e06c3680d0009e8cb", "installdate": 6648, "max_pv": "3.52.5", "pf": "7b15e4ce-9d87-4a73-965f-af337af39336", "pv": "3.52.10"}, "ngpampappnmepgilojfohadhhmbhlaek": {"cohort": "1::", "cohortname": "", "dlrc": 6756, "fp": "1.c3947c99da409218ff7c3a4a3cd4d4fa269c88c72bb5a32c2ae82c87eaa3c0ee", "installdate": 6648, "max_pv": "6.42.18.3", "pf": "7a110f56-461e-4c47-b9af-e5df78ab7f23", "pv": "6.42.32"}}}, "web_apps": {"daily_metrics": {"https://www.douyin.com/": {"background_duration_sec": 0, "captures_links": false, "effective_display_mode": 5, "foreground_duration_sec": 0, "installed": false, "num_sessions": 0, "promotable": true}}, "daily_metrics_date": "13394822400000000", "did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "134"}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[],[],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:suggesteventid\":\"-6878232602323367577\",\"google:suggesttype\":[],\"google:verbatimrelevance\":851}]"}}
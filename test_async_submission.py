#!/usr/bin/env python3
"""
测试异步任务提交修复效果
"""

import requests
import json
import time
from datetime import datetime

def test_async_submission():
    """测试异步任务提交"""
    print("🧪 测试异步任务提交修复效果")
    print("="*60)
    
    # 测试数据
    test_email = "<EMAIL>"
    fake_token = "Bearer fake_token_for_testing"
    
    print(f"📧 测试邮箱: {test_email}")
    print(f"🔑 测试Token: {fake_token}")
    print()
    
    # 测试任务提交响应时间
    print("⏱️ 测试任务提交响应时间:")
    start_time = time.time()
    
    try:
        response = requests.post(
            'https://aug8.xyz/api/submit',
            json={'email': test_email},
            headers={'Authorization': fake_token},
            timeout=35  # 稍微大于服务器的30秒超时
        )
        
        response_time = time.time() - start_time
        
        print(f"   📊 状态码: {response.status_code}")
        print(f"   ⏱️ 响应时间: {response_time:.3f}秒")
        
        if response.status_code == 401:
            print("   ⚠️ 认证失败（预期的）")
            data = response.json()
            print(f"   📄 响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查响应时间
            if response_time < 5:
                print("   ✅ 响应速度很快！异步处理生效")
            elif response_time < 15:
                print("   ✅ 响应速度正常")
            else:
                print("   ⚠️ 响应较慢，可能仍有问题")
                
        elif response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查异步响应格式
            expected_fields = ['success', 'message', 'task_id', 'queue_position', 'status', 'progress']
            missing_fields = [field for field in expected_fields if field not in data]
            
            if not missing_fields:
                print("   ✅ 异步响应格式正确！")
                print(f"   📋 任务ID: {data.get('task_id')}")
                print(f"   📊 初始状态: {data.get('status')}")
                print(f"   📈 初始进度: {data.get('progress')}%")
                print(f"   🔢 队列位置: {data.get('queue_position')}")
            else:
                print(f"   ⚠️ 响应格式缺少字段: {missing_fields}")
                
        elif response.status_code == 503:
            data = response.json()
            print(f"   ❌ 服务不可用: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if 'timeout' in data.get('message', '').lower():
                print("   ❌ 仍然出现超时问题！")
            else:
                print("   💡 可能是连接问题，不是超时问题")
                
        else:
            print(f"   ❓ 其他状态: {response.text}")
            
    except requests.exceptions.Timeout:
        response_time = time.time() - start_time
        print(f"   ❌ 请求超时！耗时: {response_time:.3f}秒")
        print("   💡 可能需要进一步增加超时时间")
    except Exception as e:
        response_time = time.time() - start_time
        print(f"   ❌ 请求失败: {e}")
        print(f"   ⏱️ 失败前耗时: {response_time:.3f}秒")

def analyze_improvements():
    """分析改进效果"""
    print("\n" + "="*60)
    print("🔧 异步处理改进分析:")
    print("="*60)
    
    print("📋 修改内容:")
    print("1. ✅ 超时时间: 10秒 → 30秒")
    print("2. ✅ 异步处理: 立即返回任务接收确认")
    print("3. ✅ 响应格式: 包含任务ID、状态、进度等信息")
    print("4. ✅ 用户体验: 前端立即显示进度条")
    
    print("\n🎯 预期效果:")
    print("- 前端点击'立即激活'后立即看到进度条")
    print("- 不再出现'任务处理服务响应超时'错误")
    print("- 任务在后台正常处理")
    print("- 进度条从20%开始，逐步更新到100%")
    
    print("\n⚠️ 注意事项:")
    print("- 需要重启 production_server.py 来加载修改")
    print("- autoback.py 需要正常运行在 localhost:5002")
    print("- 前端需要有效的认证token才能成功提交")

def test_multiple_submissions():
    """测试多次提交的响应时间"""
    print("\n🔄 测试多次提交的响应时间:")
    
    times = []
    for i in range(3):
        print(f"\n第 {i+1} 次提交:")
        start_time = time.time()
        
        try:
            response = requests.post(
                'https://aug8.xyz/api/submit',
                json={'email': f'test{i+1}@example.com'},
                headers={'Authorization': 'Bearer fake_token'},
                timeout=35
            )
            
            response_time = time.time() - start_time
            times.append(response_time)
            
            print(f"   ⏱️ 响应时间: {response_time:.3f}秒")
            print(f"   📊 状态码: {response.status_code}")
            
        except Exception as e:
            response_time = time.time() - start_time
            times.append(response_time)
            print(f"   ❌ 失败: {e}")
            print(f"   ⏱️ 失败前耗时: {response_time:.3f}秒")
        
        time.sleep(1)  # 间隔1秒
    
    if times:
        avg_time = sum(times) / len(times)
        max_time = max(times)
        min_time = min(times)
        
        print(f"\n📊 响应时间统计:")
        print(f"   平均: {avg_time:.3f}秒")
        print(f"   最快: {min_time:.3f}秒")
        print(f"   最慢: {max_time:.3f}秒")
        
        if max_time < 10:
            print("   ✅ 所有响应都很快！异步处理效果良好")
        elif max_time < 20:
            print("   ✅ 响应时间可接受")
        else:
            print("   ⚠️ 仍有较慢的响应，可能需要进一步优化")

def main():
    """主函数"""
    print("🔧 异步任务提交修复验证")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_async_submission()
    test_multiple_submissions()
    analyze_improvements()
    
    print("\n" + "="*60)
    print("💡 测试完成！")
    print("如果响应时间显著改善且不再出现超时错误，")
    print("说明异步处理修复成功！")
    print("建议用户重启 production_server.py 后测试前端功能。")
    print("="*60)

if __name__ == "__main__":
    main()

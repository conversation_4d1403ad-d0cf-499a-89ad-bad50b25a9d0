"""
浏览器管理模块
负责启动、管理和控制多个浏览器实例
"""
import os
import subprocess
import platform
import time
import random
import json
from typing import Dict, Optional, List
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from config_manager import ConfigManager

# 尝试导入stealth功能
try:
    from selenium_stealth import stealth
    from fake_useragent import UserAgent
    STEALTH_AVAILABLE = True
    print("✅ Stealth功能可用")
except ImportError:
    STEALTH_AVAILABLE = False
    print("⚠️ Stealth功能不可用，请安装: pip install selenium-stealth fake-useragent")

# 尝试导入原生环境模拟器
try:
    from native_environment_simulator import NativeEnvironmentSimulator
    NATIVE_SIMULATION_AVAILABLE = True
    print("✅ 原生环境模拟器已加载")
except ImportError:
    NATIVE_SIMULATION_AVAILABLE = False
    print("⚠️ 原生环境模拟器不可用，将使用标准配置")


class BrowserManager:
    """浏览器实例管理器"""

    def __init__(self, config_manager: ConfigManager = None):
        """初始化浏览器管理器"""
        self.config_manager = config_manager or ConfigManager()
        self.chrome_path = self._get_chrome_path()
        self.running_browsers = {}  # 存储运行中的浏览器实例

        # 初始化原生环境模拟器（不自动初始化）
        if NATIVE_SIMULATION_AVAILABLE and os.path.exists('native_browser_analysis.json'):
            try:
                self.native_simulator = NativeEnvironmentSimulator()
                print("🎭 原生环境模拟器初始化成功")
            except Exception as e:
                print(f"⚠️ 原生环境模拟器初始化失败: {e}")
                self.native_simulator = None
        else:
            self.native_simulator = None
            if NATIVE_SIMULATION_AVAILABLE:
                print("💡 原生环境分析文件不存在，跳过模拟器初始化")

        if not self.chrome_path:
            raise RuntimeError("未找到Chrome浏览器，请确保已安装Chrome")

        # 初始化stealth功能
        self.ua = UserAgent() if STEALTH_AVAILABLE else None
        self.stealth_enabled = STEALTH_AVAILABLE

        # 预定义的随机化配置
        self.screen_resolutions = [
            (1920, 1080), (1366, 768), (1440, 900), (1536, 864),
            (1280, 720), (1600, 900), (1024, 768), (1680, 1050)
        ]

        self.languages = [
            ["en-US", "en"], ["en-GB", "en"], ["en-CA", "en"],
            ["zh-CN", "zh"], ["zh-TW", "zh"], ["ja-JP", "ja"]
        ]

        self.webgl_vendors = [
            "Intel Inc.", "NVIDIA Corporation", "AMD", "Apple Inc."
        ]

        self.webgl_renderers = [
            "Intel Iris OpenGL Engine", "NVIDIA GeForce GTX 1060",
            "AMD Radeon Pro 560", "Apple M1", "Intel HD Graphics 620"
        ]

    def generate_stealth_fingerprint(self):
        """生成Stealth模式的随机指纹配置"""
        import random

        # 随机User-Agent (更真实的组合)
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]

        if self.ua and STEALTH_AVAILABLE:
            try:
                user_agent = self.ua.random
            except:
                user_agent = random.choice(user_agents)
        else:
            user_agent = random.choice(user_agents)

        fingerprint = {
            'user_agent': user_agent,
            'screen_resolution': random.choice(self.screen_resolutions),
            'language': random.choice(self.languages),
            'webgl_vendor': random.choice(self.webgl_vendors),
            'webgl_renderer': random.choice(self.webgl_renderers),
            'platform': random.choice(['Win32', 'MacIntel', 'Linux x86_64']),
            'hardware_concurrency': random.choice([2, 4, 6, 8, 12, 16]),
            'device_memory': random.choice([2, 4, 8, 16, 32]),
            'color_depth': random.choice([24, 30, 32]),
            'pixel_ratio': random.choice([1, 1.25, 1.5, 2, 2.5, 3]),
            'timezone': random.choice([
                "America/New_York", "America/Los_Angeles", "Europe/London",
                "Europe/Berlin", "Asia/Tokyo", "Asia/Shanghai", "Asia/Seoul"
            ])
        }
        return fingerprint

    def generate_random_fingerprint(self):
        """生成随机的浏览器指纹配置"""
        fingerprint = {
            'user_agent': self.ua.random if self.ua else self._get_random_user_agent(),
            'screen_resolution': random.choice(self.screen_resolutions),
            'language': random.choice(self.languages),
            'webgl_vendor': random.choice(self.webgl_vendors),
            'webgl_renderer': random.choice(self.webgl_renderers),
            'platform': random.choice(['Win32', 'MacIntel', 'Linux x86_64']),
            'hardware_concurrency': random.choice([2, 4, 6, 8, 12, 16]),
            'device_memory': random.choice([2, 4, 8, 16, 32]),
            'color_depth': random.choice([24, 30, 32]),
            'pixel_ratio': random.choice([1, 1.25, 1.5, 2, 2.5, 3])
        }
        return fingerprint

    def _get_random_user_agent(self):
        """备用的随机User-Agent生成器"""
        chrome_versions = ['120.0.0.0', '119.0.0.0', '118.0.0.0', '117.0.0.0']
        webkit_versions = ['537.36', '537.35', '537.34']

        chrome_ver = random.choice(chrome_versions)
        webkit_ver = random.choice(webkit_versions)

        return f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{webkit_ver} (KHTML, like Gecko) Chrome/{chrome_ver} Safari/{webkit_ver}"

    def _get_chrome_path(self) -> Optional[str]:
        """获取Chrome浏览器路径（优先使用便携版）"""
        # 1. 优先检查134文件夹中的Chrome
        portable_paths = [
            os.path.join(os.path.dirname(__file__), "134", "chrome-win64", "chrome.exe"),
            os.path.join(os.getcwd(), "134", "chrome-win64", "chrome.exe"),
            "134\\chrome-win64\\chrome.exe",
            os.path.join(os.path.dirname(__file__), "browsers", "chrome", "chrome.exe"),
            os.path.join(os.getcwd(), "browsers", "chrome", "chrome.exe"),
            os.path.join(os.path.dirname(__file__), "portable_chrome", "chrome", "chrome.exe"),
            os.path.join(os.getcwd(), "portable_chrome", "chrome", "chrome.exe"),
            "browsers\\chrome\\chrome.exe",
            "portable_chrome\\chrome\\chrome.exe",
            "chrome_portable\\chrome.exe",
            "chromium_portable\\chrome.exe"
        ]

        for path in portable_paths:
            if os.path.exists(path):
                print(f"✅ 找到便携版Chrome: {path}")
                return path

        # 2. 检查便携版配置文件
        config_paths = [
            os.path.join(os.path.dirname(__file__), "browsers", "chrome_config.json"),
            os.path.join(os.getcwd(), "browsers", "chrome_config.json"),
            os.path.join(os.path.dirname(__file__), "portable_chrome", "chrome_config.json"),
            os.path.join(os.getcwd(), "portable_chrome", "chrome_config.json")
        ]

        for config_path in config_paths:
            if os.path.exists(config_path):
                try:
                    import json
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        chrome_exe = config.get('chrome_exe')
                        if chrome_exe and os.path.exists(chrome_exe):
                            print(f"✅ 从配置文件找到便携版Chrome: {chrome_exe}")
                            return chrome_exe
                except Exception as e:
                    print(f"⚠️ 读取便携版配置失败: {e}")

        # 3. 检查系统安装的Chrome
        system = platform.system()

        if system == "Windows":
            paths = [
                os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"),
                           "Google\\Chrome\\Application\\chrome.exe"),
                os.path.join(os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)"),
                           "Google\\Chrome\\Application\\chrome.exe"),
                os.path.join(os.environ.get("LOCALAPPDATA", ""),
                           "Google\\Chrome\\Application\\chrome.exe")
            ]
        elif system == "Darwin":  # macOS
            paths = ["/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"]
        else:  # Linux
            paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/chromium-browser",
                "/usr/bin/google-chrome-stable"
            ]

        for path in paths:
            if os.path.exists(path):
                print(f"✅ 找到系统Chrome: {path}")
                return path

        print("❌ 未找到Chrome浏览器")
        return None

    def launch_stealth_browser(self, profile_name: str, fingerprint: Dict = None) -> Optional[Dict]:
        """启动带有Stealth伪装的浏览器"""
        if not STEALTH_AVAILABLE:
            print("⚠️ Stealth功能不可用，使用普通模式启动")
            return self.launch_browser(profile_name)

        if profile_name in self.running_browsers:
            print(f"⚠️ 浏览器 {profile_name} 已在运行")
            return self.running_browsers[profile_name]

        # 获取配置
        profile = self.config_manager.get_profile(profile_name)
        if not profile:
            raise ValueError(f"配置 {profile_name} 不存在")

        # 生成或使用指定的指纹
        if not fingerprint:
            fingerprint = self.generate_stealth_fingerprint()

        print(f"🎭 启动Stealth浏览器: {profile_name}")
        print(f"🌐 User-Agent: {fingerprint['user_agent'][:60]}...")
        print(f"📱 分辨率: {fingerprint['screen_resolution']}")
        print(f"🗣️ 语言: {fingerprint['language']}")

        try:
            # 创建用户数据目录
            profile_path = profile['path']
            os.makedirs(profile_path, exist_ok=True)

            # 设置环境变量来减少Chrome错误输出
            env = os.environ.copy()
            env['CHROME_LOG_FILE'] = 'NUL' if os.name == 'nt' else '/dev/null'
            env['CHROME_HEADLESS'] = '0'

            # 创建Chrome选项
            options = self._create_stealth_chrome_options(profile, fingerprint)

            # 获取ChromeDriver路径
            chromedriver_path = self._get_chromedriver_path()
            if not chromedriver_path:
                print("⚠️ 未找到ChromeDriver，尝试使用webdriver-manager")
                from webdriver_manager.chrome import ChromeDriverManager
                chromedriver_path = ChromeDriverManager().install()

            # 启动浏览器 - 完全静默模式
            from selenium.webdriver.chrome.service import Service
            import subprocess

            # 重定向所有输出到空设备，避免阻塞UI
            devnull = open(os.devnull, 'w')

            # 创建简化的静默Service
            service = Service(
                executable_path=chromedriver_path,
                service_args=['--silent']  # 只使用基本的静默参数
            )

            # 更新环境变量抑制Chrome输出
            env['CHROME_LOG_FILE'] = os.devnull
            env['CHROME_HEADLESS'] = '0'

            try:
                # 直接启动WebDriver，输出重定向已在service中设置
                driver = webdriver.Chrome(
                    service=service,
                    options=options
                )

            finally:
                # 关闭devnull文件句柄
                devnull.close()

            # 应用Stealth模式
            stealth(driver,
                languages=fingerprint['language'],
                vendor=fingerprint['webgl_vendor'],
                platform=fingerprint['platform'],
                webgl_vendor=fingerprint['webgl_vendor'],
                renderer=fingerprint['webgl_renderer'],
                fix_hairline=True,
            )

            # 注入额外的指纹伪装脚本
            self._inject_stealth_scripts(driver, fingerprint)

            # 记录运行信息（保持与标准模式兼容）
            browser_info = {
                "driver": driver,
                "process": driver.service.process,  # 添加process字段以兼容现有代码
                "pid": driver.service.process.pid if driver.service.process else None,
                "profile_name": profile_name,
                "fingerprint": fingerprint,
                "stealth_mode": True,
                "started_at": time.time(),
                "debug_port": profile.get('debug_port'),
                "user_agent": fingerprint.get('user_agent', ''),
                "proxy": profile.get('proxy')
            }

            self.running_browsers[profile_name] = browser_info

            # 更新配置文件的最后使用时间和指纹信息
            self.config_manager.update_profile(profile_name, {
                "last_used": time.strftime("%Y-%m-%d %H:%M:%S"),
                "fingerprint": fingerprint,
                "stealth_enabled": True
            })

            print(f"✅ Stealth浏览器启动成功: {profile_name}")
            return browser_info

        except Exception as e:
            print(f"❌ 启动Stealth浏览器失败: {e}")
            raise RuntimeError(f"启动Stealth浏览器失败: {e}")

    def _create_stealth_chrome_options(self, profile: Dict, fingerprint: Dict):
        """创建Stealth模式的Chrome选项（支持原生环境模拟）"""
        from selenium.webdriver.chrome.options import Options

        options = Options()

        # 设置Chrome二进制路径（关键修复）
        if self.chrome_path:
            options.binary_location = self.chrome_path
            print(f"🔧 设置Chrome二进制路径: {self.chrome_path}")

        # 检查是否启用原生环境模拟
        native_simulation = profile.get('native_simulation', False)

        if native_simulation:
            # 原生环境模拟模式 - 使用模拟的用户数据目录
            simulated_user_data = os.path.join(profile["path"], "User Data")
            if os.path.exists(simulated_user_data):
                options.add_argument(f'--user-data-dir={simulated_user_data}')
                print(f"🎭 使用原生环境模拟: {simulated_user_data}")
            else:
                options.add_argument(f'--user-data-dir={profile["path"]}')
                print(f"⚠️ 原生环境模拟目录不存在，使用标准目录")
        else:
            # 标准模式
            options.add_argument(f'--user-data-dir={profile["path"]}')

        # 原生环境模拟的反检测配置（更温和的参数）
        if native_simulation:
            # 原生模式：使用更少的反检测参数，模拟真实浏览器
            options.add_argument('--no-first-run')
            options.add_argument('--no-default-browser-check')
            # 不使用 --disable-blink-features=AutomationControlled
            # 不使用 excludeSwitches 和 useAutomationExtension
            print(f"🎭 原生模拟模式：使用温和的反检测参数")
        else:
            # 标准Stealth模式：使用完整的反检测参数
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            print(f"🛡️ 标准Stealth模式：使用完整反检测参数")

        # 启动参数根据模拟模式调整
        if native_simulation:
            # 原生模拟模式：使用更少的启动参数，模拟真实浏览器
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            # 不使用 --disable-gpu，让浏览器使用真实GPU
            # 不使用 --remote-debugging-port，避免被检测
            print(f"🎭 原生模拟：使用最小启动参数")
        else:
            # 标准模式：使用完整的启动参数
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--remote-debugging-port=0')  # 随机端口

        # 指纹伪装（原生模拟模式下更温和）
        if not native_simulation:
            # 标准模式：强制设置User-Agent
            options.add_argument(f'--user-agent={fingerprint["user_agent"]}')
        # 原生模拟模式：让浏览器使用配置文件中的User-Agent

        width, height = fingerprint['screen_resolution']
        options.add_argument(f'--window-size={width},{height}')

        # 语言设置
        if not native_simulation:
            # 标准模式：强制设置语言
            options.add_argument(f'--lang={fingerprint["language"][0]}')
            options.add_experimental_option('prefs', {
                'intl.accept_languages': ','.join(fingerprint['language'])
            })
        # 原生模拟模式：使用配置文件中的语言设置

        # WebGL伪装和优化
        options.add_argument('--use-gl=swiftshader-webgl')
        options.add_argument('--disable-gpu-sandbox')
        options.add_argument('--enable-unsafe-swiftshader')  # 修复WebGL警告

        # 其他隐私设置
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-ipc-flooding-protection')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-client-side-phishing-detection')
        options.add_argument('--disable-sync')
        options.add_argument('--disable-default-apps')

        # 应用日志优化（减少SSL错误等）
        try:
            from chrome_log_optimizer import apply_log_optimization_to_options
            options = apply_log_optimization_to_options(options, stealth_mode=True)
        except ImportError:
            # 如果优化器不可用，使用基本的日志减少参数
            options.add_argument('--log-level=3')  # 只显示致命错误
            options.add_argument('--silent')
            options.add_argument('--disable-logging')
            options.add_argument('--ignore-ssl-errors')
            options.add_argument('--disable-background-networking')

        # 便携版Chrome特殊优化和数据保存
        if self._is_portable_chrome():
            options.add_argument('--disable-background-networking')
            options.add_argument('--disable-translate')
            options.add_argument('--disable-features=MediaRouter')
            options.add_argument('--force-local-storage')
            options.add_argument('--disable-cloud-import')

        # 数据保存和会话恢复优化
        options.add_argument('--enable-aggressive-domstorage-flushing')
        options.add_argument('--enable-local-storage')
        options.add_argument('--enable-session-storage')
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--restore-last-session')
        options.add_argument('--enable-session-service')
        options.add_argument('--enable-session-restore')
        options.add_argument('--disable-session-crashed-bubble')
        options.add_argument('--disable-infobars')

        return options

    def _inject_stealth_scripts(self, driver, fingerprint: Dict):
        """注入Stealth指纹伪装脚本"""
        script = f"""
        // 移除webdriver标识
        Object.defineProperty(navigator, 'webdriver', {{
            get: () => undefined
        }});

        // 伪装硬件信息
        Object.defineProperty(navigator, 'hardwareConcurrency', {{
            get: () => {fingerprint['hardware_concurrency']}
        }});

        Object.defineProperty(navigator, 'deviceMemory', {{
            get: () => {fingerprint['device_memory']}
        }});

        // 伪装屏幕信息
        Object.defineProperty(screen, 'colorDepth', {{
            get: () => {fingerprint['color_depth']}
        }});

        Object.defineProperty(window, 'devicePixelRatio', {{
            get: () => {fingerprint['pixel_ratio']}
        }});

        // 伪装插件信息
        Object.defineProperty(navigator, 'plugins', {{
            get: () => [{{
                name: 'Chrome PDF Plugin',
                filename: 'internal-pdf-viewer',
                description: 'Portable Document Format'
            }}]
        }});

        // 伪装时区
        try {{
            Intl.DateTimeFormat = function() {{
                return {{
                    resolvedOptions: () => ({{
                        timeZone: '{fingerprint['timezone']}'
                    }})
                }};
            }};
        }} catch(e) {{}}

        console.log('🎭 Stealth指纹伪装脚本已注入');
        """

        try:
            driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': script
            })
            print("✅ Stealth指纹脚本已注入")
        except Exception as e:
            print(f"⚠️ 注入Stealth脚本失败: {e}")

    def launch_browser(self, profile_name: str) -> Dict:
        """启动指定配置文件的浏览器（使用134文件夹中的Chrome）"""
        profile = self.config_manager.get_profile(profile_name)
        if not profile:
            raise ValueError(f"配置文件 '{profile_name}' 不存在")

        print(f"🚀 使用134文件夹Chrome启动配置: {profile_name}")
        return self._launch_134_browser(profile_name)

    def _launch_134_browser(self, profile_name: str) -> Dict:
        """使用134文件夹中的Chrome启动浏览器（简化版本）"""
        profile = self.config_manager.get_profile(profile_name)
        if not profile:
            raise ValueError(f"配置文件 '{profile_name}' 不存在")

        debug_port = profile["debug_port"]

        # 检查是否已经在运行
        if profile_name in self.running_browsers:
            if self._is_browser_running(profile_name):
                print(f"✅ 浏览器已在运行，复用现有实例: {profile_name}")
                return self.running_browsers[profile_name]
            else:
                # 清理已停止的浏览器记录
                del self.running_browsers[profile_name]

        # 构建134文件夹Chrome启动命令
        cmd = self._build_134_chrome_command(profile)

        # 启动浏览器进程
        try:
            print(f"🔧 启动命令: {' '.join(cmd[:3])}...")  # 只显示前几个参数

            # 重定向所有输出到空设备，避免阻塞UI
            devnull = open(os.devnull, 'w')

            # 设置环境变量抑制Chrome输出
            env = os.environ.copy()
            env['CHROME_LOG_FILE'] = os.devnull  # 这是正确的路径字符串
            env['CHROME_HEADLESS'] = '0'

            process = subprocess.Popen(
                cmd,
                stdout=devnull,  # 重定向标准输出
                stderr=devnull,  # 重定向错误输出
                stdin=subprocess.PIPE,  # 允许输入
                env=env,  # 使用修改的环境变量
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0  # Windows下隐藏窗口
            )

            # 关闭devnull文件句柄（进程会保持重定向）
            devnull.close()

            # 等待浏览器启动
            time.sleep(3)

            # 记录运行信息
            browser_info = {
                "pid": process.pid,
                "debug_port": profile["debug_port"],
                "process": process,
                "profile_name": profile_name,
                "started_at": time.time(),
                "chrome_version": "134.0.6998.35"
            }

            self.running_browsers[profile_name] = browser_info

            # 更新配置文件的最后使用时间
            self.config_manager.update_profile(profile_name, {
                "last_used": time.strftime("%Y-%m-%d %H:%M:%S")
            })

            print(f"✅ 134文件夹Chrome启动成功: {profile_name} (PID: {process.pid})")
            return browser_info

        except Exception as e:
            raise RuntimeError(f"启动134文件夹Chrome失败: {e}")

    def _launch_standard_browser(self, profile_name: str) -> Dict:
        """启动标准模式浏览器（原launch_browser逻辑）"""
        profile = self.config_manager.get_profile(profile_name)
        if not profile:
            raise ValueError(f"配置文件 '{profile_name}' 不存在")

        debug_port = profile["debug_port"]

        # 检查是否已经在运行
        if profile_name in self.running_browsers:
            if self._is_browser_running(profile_name):
                print(f"✅ 浏览器已在运行，复用现有实例: {profile_name}")
                return self.running_browsers[profile_name]
            else:
                # 清理已停止的浏览器记录
                del self.running_browsers[profile_name]

        # 智能检查现有浏览器实例
        existing_browser = self._check_existing_browser_instance(profile_name, debug_port)
        if existing_browser:
            print(f"✅ 发现现有浏览器实例，复用: {profile_name}")
            self.running_browsers[profile_name] = existing_browser
            return existing_browser

        # 检查端口是否被占用（仅检查，不清理）
        if self._is_port_in_use(debug_port):
            print(f"⚠️ 端口 {debug_port} 被占用")
            # 检查是否为同配置的浏览器
            if self._is_target_profile_using_port(profile_name, debug_port):
                print(f"✅ 端口被目标配置占用，这是正常的")
            else:
                print(f"⚠️ 端口被其他进程占用，但不进行清理以保护数据")
                # 不进行任何清理操作，保护现有浏览器数据

        # 构建启动命令
        cmd = self._build_chrome_command(profile)

        # 确保Chrome配置支持会话恢复
        self._ensure_session_restore_config(profile)

        # 检查上次是否正常退出
        self._check_last_exit_status(profile)

        # 启动浏览器进程 - 完全静默模式
        try:
            # 重定向所有输出到空设备，避免阻塞UI
            devnull = open(os.devnull, 'w')

            # 设置环境变量抑制Chrome输出
            env = os.environ.copy()
            env['CHROME_LOG_FILE'] = os.devnull
            env['CHROME_HEADLESS'] = '0'

            process = subprocess.Popen(
                cmd,
                stdout=devnull,  # 重定向标准输出
                stderr=devnull,  # 重定向错误输出
                stdin=subprocess.PIPE,  # 允许输入
                env=env,  # 使用修改的环境变量
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0  # Windows下隐藏窗口
            )

            # 关闭devnull文件句柄（进程会保持重定向）
            devnull.close()

            # 等待浏览器启动
            time.sleep(3)

            # 记录运行信息
            browser_info = {
                "pid": process.pid,
                "debug_port": profile["debug_port"],
                "process": process,
                "profile_name": profile_name,
                "started_at": time.time()
            }

            self.running_browsers[profile_name] = browser_info

            # 更新配置文件的最后使用时间
            self.config_manager.update_profile(profile_name, {
                "last_used": time.strftime("%Y-%m-%d %H:%M:%S")
            })

            return browser_info

        except Exception as e:
            raise RuntimeError(f"启动浏览器失败: {e}")

    def _build_134_chrome_command(self, profile: Dict) -> List[str]:
        """构建134文件夹Chrome启动命令（简化版本：用户数据存储+finger插件）"""
        # 确保使用134文件夹中的Chrome
        chrome_134_path = None
        for path in [
            os.path.join(os.getcwd(), "134", "chrome-win64", "chrome.exe"),
            "134\\chrome-win64\\chrome.exe"
        ]:
            if os.path.exists(path):
                chrome_134_path = path
                break

        if not chrome_134_path:
            raise RuntimeError("未找到134文件夹中的Chrome浏览器")

        # 检查可用插件
        available_extensions = []

        # 检查finger插件
        finger_plugin_path = os.path.join(os.getcwd(), "finger")
        finger_plugin_exists = os.path.exists(finger_plugin_path) and os.path.exists(os.path.join(finger_plugin_path, "manifest.json"))
        if finger_plugin_exists:
            available_extensions.append(finger_plugin_path)
            print(f"🛡️ 发现finger插件: {finger_plugin_path}")

        # 检查timezone插件
        timezone_plugin_path = os.path.join(os.getcwd(), "timezone")
        timezone_plugin_exists = os.path.exists(timezone_plugin_path) and os.path.exists(os.path.join(timezone_plugin_path, "manifest.json"))
        if timezone_plugin_exists:
            available_extensions.append(timezone_plugin_path)
            print(f"🕐 发现timezone插件: {timezone_plugin_path}")

        if not available_extensions:
            print(f"⚠️ 未找到任何可用插件")

        # 基础启动命令（只包含必要参数）
        cmd = [
            chrome_134_path,
            f"--user-data-dir={profile['path']}",  # 用户数据目录
            f"--remote-debugging-port={profile['debug_port']}",  # 调试端口
            # 基本设置
            "--no-first-run",
            "--no-default-browser-check",
            # 会话恢复
            "--restore-last-session",
            "--enable-session-service",
            "--disable-session-crashed-bubble",
            # 数据持久化
            "--enable-local-storage",
            "--enable-session-storage",
            "--enable-cookies",
            # 性能优化
            "--disable-background-timer-throttling",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",
            "--disable-component-update",
            "--disable-default-apps",
            "--disable-sync",
            "--disable-translate",
            # 日志优化
            "--log-level=3",
            "--silent"
        ]

        # 加载所有可用插件
        if available_extensions:
            # 构建扩展路径列表
            extension_paths = ",".join(available_extensions)
            cmd.extend([
                f"--load-extension={extension_paths}",
                "--enable-extensions"
            ])

            # 如果只有特定插件，可以使用 --disable-extensions-except
            if len(available_extensions) <= 2:  # 只有finger和/或timezone插件
                cmd.append("--disable-extensions-except=" + extension_paths)

            print(f"✅ 已加载 {len(available_extensions)} 个插件:")
            for ext_path in available_extensions:
                ext_name = os.path.basename(ext_path)
                print(f"   - {ext_name} 插件")
        else:
            cmd.append("--disable-extensions")
            print(f"⚠️ 禁用所有扩展")

        # 添加代理设置（如果有）
        if profile.get("proxy"):
            cmd.append(f"--proxy-server={profile['proxy']}")
            print(f"🌐 使用代理: {profile['proxy']}")

        print(f"🔧 Chrome路径: {chrome_134_path}")
        print(f"📁 用户数据: {profile['path']}")
        return cmd

    def _build_chrome_command(self, profile: Dict) -> List[str]:
        """构建Chrome启动命令 - 使用便携版Chrome优化登录状态保存"""
        # 检查是否使用便携版Chrome
        is_portable = self._is_portable_chrome()

        cmd = [
            self.chrome_path,
            f"--user-data-dir={profile['path']}",
            f"--remote-debugging-port={profile['debug_port']}",
            f"--user-agent={profile['user_agent']}",
            # 基本设置
            "--no-first-run",
            "--no-default-browser-check",
            # 关键：启用会话恢复
            "--restore-last-session",
            "--enable-session-service",
            "--enable-session-restore",
            "--disable-session-crashed-bubble",
            "--disable-infobars",
            # 关键：确保数据持久化
            "--enable-local-storage",
            "--enable-session-storage",
            "--enable-cookies",
            "--enable-database",
            "--enable-indexed-db",
            # 重要：禁用可能影响数据保存的功能
            # 确保正常保存数据
            "--disable-background-timer-throttling",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",
            # 减少干扰，但保持核心功能
            "--disable-component-update",
            "--disable-default-apps",
            "--disable-hang-monitor",
            # 新增：强化数据持久化和会话恢复
            "--enable-aggressive-domstorage-flushing",
            "--disable-extensions-http-throttling",
            "--disable-ipc-flooding-protection",
            # 确保Cookie和会话数据正确保存（使用更兼容的方式）
            # 禁用可能影响数据保存的功能
            "--disable-dev-shm-usage",
            # 会话和数据保存优化
            "--enable-aggressive-domstorage-flushing",
            "--enable-local-storage",
            "--enable-session-storage",
            "--disable-background-timer-throttling",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",
            # 减少网络错误日志
            "--log-level=3",  # 只显示致命错误
            "--silent",
            "--disable-logging",
            "--ignore-ssl-errors",
            "--disable-background-networking",
            "--disable-domain-reliability"
        ]

        # 便携版Chrome特殊优化
        if is_portable:
            cmd.extend([
                # 便携版特殊设置
                "--disable-background-networking",
                "--disable-sync",  # 禁用同步，避免与Google账户冲突
                "--disable-translate",
                "--disable-features=MediaRouter",
                # 强制本地数据保存
                "--force-local-storage",
                "--disable-cloud-import",
                # 减少网络依赖
                "--disable-component-extensions-with-background-pages"
            ])
            print(f"🔧 使用便携版Chrome启动配置")
        else:
            print(f"🔧 使用系统Chrome启动配置")

        # 添加指纹参数
        fingerprint = profile.get("fingerprint", {})
        if fingerprint.get("timezone"):
            cmd.append(f"--timezone={fingerprint['timezone']}")

        # 添加代理设置
        if profile.get("proxy"):
            cmd.append(f"--proxy-server={profile['proxy']}")

        # 不添加默认启动网址，让浏览器恢复上次的会话状态
        # 这样可以保持用户的登录状态和页面状态

        return cmd

    def _ensure_session_restore_config(self, profile):
        """确保Chrome配置支持会话恢复"""
        try:
            import json
            import os

            profile_path = profile['path']
            preferences_file = os.path.join(profile_path, 'Preferences')

            # 增强的会话恢复配置
            session_config = {
                "session": {
                    "restore_on_startup": 1,  # 1 = 恢复上次会话
                    "startup_urls": [],
                    "restore_on_startup_migrated": True,
                    "restore_tabs_on_startup": True
                },
                "profile": {
                    "exit_type": "Normal",  # 正常退出
                    "exited_cleanly": True,
                    "name": profile['name'],
                    "avatar_index": 0,
                    "managed_user_id": "",
                    "info_cache": {}
                },
                "browser": {
                    "show_home_button": True,
                    "check_default_browser": False,
                    "has_seen_welcome_page": True,
                    "show_welcome_page": False,
                    "window_placement": {
                        "maximized": False,
                        "always_on_top": False
                    }
                },
                "signin": {
                    "allowed": True
                },
                "sync": {
                    "suppress_start": True
                },
                "bookmark_bar": {
                    "show_on_all_tabs": True
                },
                "extensions": {
                    "settings": {},
                    "alerts": {
                        "initialized": True
                    }
                },
                "local_state": {
                    "multiple_profile_prefs_version": 3
                },
                "tabs": {
                    "use_vertical_tabs": False
                },
                "privacy": {
                    "clear_on_exit": {
                        "cookies": False,
                        "cache": False,
                        "browsing_history": False,
                        "download_history": False,
                        "form_data": False,
                        "passwords": False
                    }
                }
            }

            # 如果Preferences文件存在，更新配置
            if os.path.exists(preferences_file):
                try:
                    with open(preferences_file, 'r', encoding='utf-8') as f:
                        prefs = json.load(f)

                    # 更新会话恢复设置
                    if 'session' not in prefs:
                        prefs['session'] = {}
                    prefs['session']['restore_on_startup'] = 1

                    if 'profile' not in prefs:
                        prefs['profile'] = {}
                    prefs['profile']['exit_type'] = "Normal"
                    prefs['profile']['exited_cleanly'] = True

                    # 保存更新的配置
                    with open(preferences_file, 'w', encoding='utf-8') as f:
                        json.dump(prefs, f, indent=2)

                    print(f"✅ 已更新Chrome会话恢复配置: {profile['name']}")

                except Exception as e:
                    print(f"⚠️ 更新Chrome配置失败: {e}")
            else:
                # 如果Preferences文件不存在，创建基本配置
                try:
                    os.makedirs(profile_path, exist_ok=True)
                    with open(preferences_file, 'w', encoding='utf-8') as f:
                        json.dump(session_config, f, indent=2)
                    print(f"✅ 已创建Chrome会话恢复配置: {profile['name']}")
                except Exception as e:
                    print(f"⚠️ 创建Chrome配置失败: {e}")

        except Exception as e:
            print(f"⚠️ 配置Chrome会话恢复失败: {e}")

    def get_automation_driver(self, profile_name: str) -> Optional[webdriver.Chrome]:
        """获取自动化操作的WebDriver（使用134文件夹中的ChromeDriver）"""
        if profile_name not in self.running_browsers:
            print(f"⚠️ 配置 {profile_name} 不在运行列表中")
            return None

        browser_info = self.running_browsers[profile_name]
        debug_port = browser_info.get("debug_port")

        if not debug_port:
            print(f"⚠️ 配置 {profile_name} 没有debug端口信息")
            return None

        # 检查端口是否可达
        if not self._check_debug_port_available(debug_port):
            print(f"⚠️ Debug端口 {debug_port} 不可达，浏览器可能已关闭")
            # 清理无效的浏览器记录
            if profile_name in self.running_browsers:
                del self.running_browsers[profile_name]
            return None

        try:
            # 连接到已运行的134文件夹Chrome实例
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{debug_port}")

            # 设置134文件夹Chrome二进制路径
            chrome_134_path = None
            for path in [
                os.path.join(os.getcwd(), "134", "chrome-win64", "chrome.exe"),
                "134\\chrome-win64\\chrome.exe"
            ]:
                if os.path.exists(path):
                    chrome_134_path = path
                    break

            if chrome_134_path:
                chrome_options.binary_location = chrome_134_path
                print(f"🔧 设置134文件夹Chrome路径: {chrome_134_path}")

            # 添加静默选项
            chrome_options.add_argument('--disable-logging')
            chrome_options.add_argument('--log-level=3')
            chrome_options.add_argument('--silent')

            # 使用134文件夹中的ChromeDriver
            chromedriver_134_path = None
            for path in [
                os.path.join(os.getcwd(), "134", "chromedriver.exe"),
                "134\\chromedriver.exe"
            ]:
                if os.path.exists(path):
                    chromedriver_134_path = path
                    break

            if not chromedriver_134_path:
                print(f"⚠️ 未找到134文件夹中的ChromeDriver")
                return None

            # 导入Service类
            from selenium.webdriver.chrome.service import Service

            # 创建静默的Service
            try:
                service = Service(
                    chromedriver_134_path,
                    service_args=['--silent']
                )

                driver = webdriver.Chrome(service=service, options=chrome_options)
                print(f"✅ 使用134文件夹连接到浏览器成功: {profile_name}")
                return driver

            except Exception as e:
                if "cannot connect to chrome" in str(e).lower():
                    print(f"⚠️ 无法连接到Chrome实例，端口 {debug_port} 可能已失效")
                    # 清理无效记录
                    if profile_name in self.running_browsers:
                        del self.running_browsers[profile_name]
                    return None
                else:
                    raise e

        except Exception as e:
            print(f"⚠️ 连接到134文件夹浏览器失败: {e}")
            # 清理可能无效的浏览器记录
            if profile_name in self.running_browsers:
                del self.running_browsers[profile_name]
            return None

    def _check_debug_port_available(self, port: int) -> bool:
        """检查debug端口是否可用"""
        try:
            import socket
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(0.3)  # 0.3秒快速超时，避免阻塞
                result = s.connect_ex(('127.0.0.1', port))
                return result == 0
        except Exception:
            return False

    def _get_chromedriver_path(self) -> Optional[str]:
        """获取ChromeDriver路径（优先使用便携版）"""
        # 1. 优先检查134文件夹中的ChromeDriver
        portable_paths = [
            os.path.join(os.path.dirname(__file__), "134", "chromedriver.exe"),
            os.path.join(os.getcwd(), "134", "chromedriver.exe"),
            "134\\chromedriver.exe",
            os.path.join(os.path.dirname(__file__), "browsers", "chromedriver", "chromedriver.exe"),
            os.path.join(os.getcwd(), "browsers", "chromedriver", "chromedriver.exe"),
            os.path.join(os.path.dirname(__file__), "portable_chrome", "chromedriver", "chromedriver.exe"),
            os.path.join(os.getcwd(), "portable_chrome", "chromedriver", "chromedriver.exe"),
            "browsers\\chromedriver\\chromedriver.exe",
            "portable_chrome\\chromedriver\\chromedriver.exe",
            "chromedriver\\chromedriver.exe",
            "chromedriver.exe"
        ]

        for path in portable_paths:
            if os.path.exists(path):
                print(f"✅ 找到便携版ChromeDriver: {path}")
                return path

        # 2. 检查便携版配置文件
        config_paths = [
            os.path.join(os.path.dirname(__file__), "browsers", "chrome_config.json"),
            os.path.join(os.getcwd(), "browsers", "chrome_config.json"),
            os.path.join(os.path.dirname(__file__), "portable_chrome", "chrome_config.json"),
            os.path.join(os.getcwd(), "portable_chrome", "chrome_config.json")
        ]

        for config_path in config_paths:
            if os.path.exists(config_path):
                try:
                    import json
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        chromedriver_exe = config.get('chromedriver')
                        if chromedriver_exe and os.path.exists(chromedriver_exe):
                            print(f"✅ 从配置文件找到便携版ChromeDriver: {chromedriver_exe}")
                            return chromedriver_exe
                except Exception as e:
                    print(f"⚠️ 读取便携版配置失败: {e}")

        # 3. 检查系统PATH中的ChromeDriver
        import shutil
        system_chromedriver = shutil.which("chromedriver")
        if system_chromedriver:
            print(f"✅ 找到系统ChromeDriver: {system_chromedriver}")
            return system_chromedriver

        print("⚠️ 未找到ChromeDriver，将使用webdriver-manager自动下载")
        return None

    def _is_portable_chrome(self) -> bool:
        """检查当前使用的是否为便携版Chrome"""
        if not self.chrome_path:
            return False

        # 检查路径中是否包含便携版标识
        portable_indicators = [
            "134\\chrome-win64",
            "134/chrome-win64",
            "browsers\\chrome",
            "browsers/chrome",
            "portable_chrome",
            "chromium_portable",
            "chrome_portable"
        ]

        chrome_path_lower = self.chrome_path.lower()
        for indicator in portable_indicators:
            if indicator in chrome_path_lower:
                return True

        # 检查是否存在便携版配置文件
        chrome_dir = os.path.dirname(self.chrome_path)
        config_paths = [
            os.path.join(chrome_dir, "..", "chrome_config.json"),
            os.path.join(chrome_dir, "chrome_config.json")
        ]

        for config_path in config_paths:
            if os.path.exists(config_path):
                try:
                    import json
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        if config.get('portable', False):
                            return True
                except:
                    pass

        return False

    def _is_browser_running(self, profile_name: str, safe_mode: bool = False) -> bool:
        """检查浏览器是否仍在运行

        Args:
            profile_name: 配置名称
            safe_mode: 安全模式，不修改running_browsers字典
        """
        if profile_name not in self.running_browsers:
            return False

        try:
            browser_info = self.running_browsers[profile_name]

            # 检查process字段
            process = browser_info.get("process")
            if process and hasattr(process, 'poll') and process.poll() is None:
                return True

            # 检查driver字段（Stealth模式可能只有driver）- 使用非阻塞方式
            driver = browser_info.get("driver")
            if driver:
                try:
                    # 检查driver的service进程而不是执行可能阻塞的操作
                    if hasattr(driver, 'service') and driver.service:
                        service_process = driver.service.process
                        if service_process and service_process.poll() is None:
                            return True
                except:
                    # driver已失效
                    pass

            # 如果都检查失败，在非安全模式下清理记录
            if not safe_mode:
                del self.running_browsers[profile_name]
            return False

        except (KeyError, AttributeError, Exception):
            # 如果检查过程中出现任何异常，在非安全模式下清理记录
            if not safe_mode and profile_name in self.running_browsers:
                del self.running_browsers[profile_name]
            return False

    def close_browser(self, profile_name: str) -> bool:
        """关闭指定的浏览器实例 - 仅模拟手动关闭，不强制清理"""
        if profile_name not in self.running_browsers:
            print(f"⚠️ 浏览器 {profile_name} 未在运行记录中")
            return True

        try:
            browser_info = self.running_browsers[profile_name]
            process = browser_info.get("process")
            debug_port = browser_info.get("debug_port")
            stealth_mode = browser_info.get("stealth_mode", False)

            print(f"🔒 模拟手动关闭浏览器: {profile_name} (Stealth: {stealth_mode})")

            # 在关闭前强制保存状态
            self.force_save_browser_state(profile_name)

            # 对于Stealth模式，直接使用driver
            if stealth_mode and "driver" in browser_info:
                try:
                    driver = browser_info["driver"]
                    print("🎭 关闭Stealth模式浏览器...")
                    driver.quit()
                    del self.running_browsers[profile_name]
                    return True
                except Exception as e:
                    print(f"⚠️ 关闭Stealth浏览器失败: {e}")

            # 尝试通过WebDriver优雅关闭（标准模式）
            try:
                driver = self.get_automation_driver(profile_name)
                if driver:
                    print("📱 通过WebDriver关闭所有窗口...")
                    # 关闭所有窗口
                    for handle in driver.window_handles:
                        driver.switch_to.window(handle)
                        driver.close()

                    # 退出WebDriver
                    driver.quit()
                    print("✅ WebDriver优雅关闭成功")

                    # 等待Chrome自然退出
                    time.sleep(3)

                    # 检查进程是否已退出
                    if process.poll() is not None:
                        print("✅ Chrome进程已自然退出")
                        del self.running_browsers[profile_name]
                        return True

            except Exception as e:
                print(f"⚠️ WebDriver关闭失败: {e}")

            # 如果WebDriver关闭失败，使用温和的进程终止（仅当process存在时）
            if process:
                print("🔄 使用温和的进程终止...")
                process.terminate()  # 发送SIGTERM信号，允许Chrome保存数据

                # 给Chrome充足时间保存数据 - 增加到15秒
                try:
                    process.wait(timeout=15)  # 增加到15秒等待时间，确保数据完全保存
                    print("✅ Chrome进程已优雅退出")
                except subprocess.TimeoutExpired:
                    print("⚠️ Chrome进程未在15秒内退出，但不强制终止以保护数据")
                    # 不使用process.kill()，让Chrome继续运行以保护数据
            else:
                print("⚠️ 没有process信息，可能是Stealth模式已处理")

            # 从运行记录中移除（即使进程可能还在运行）
            del self.running_browsers[profile_name]
            return True

        except Exception as e:
            print(f"⚠️ 关闭浏览器过程中出现异常: {e}")
            # 即使出错也不进行强制清理，保护数据
            if profile_name in self.running_browsers:
                del self.running_browsers[profile_name]
            return False

    def get_running_browsers(self) -> Dict:
        """获取所有运行中的浏览器"""
        # 清理已停止的浏览器 - 使用安全的方式避免字典修改冲突
        browsers_to_check = list(self.running_browsers.items())
        stopped_browsers = []

        for name, info in browsers_to_check:
            # 检查浏览器是否还在运行，但不让_is_browser_running修改字典
            try:
                browser_info = self.running_browsers.get(name)
                if not browser_info:
                    continue

                # 使用综合检查方法
                is_running = self._comprehensive_browser_check(browser_info)

                if not is_running:
                    stopped_browsers.append(name)
                    print(f"🔍 检测到浏览器已停止: {name}")

            except Exception:
                stopped_browsers.append(name)

        # 安全地删除已停止的浏览器
        for name in stopped_browsers:
            if name in self.running_browsers:
                del self.running_browsers[name]

        return self.running_browsers.copy()

    def close_all_browsers(self) -> int:
        """关闭所有浏览器实例"""
        closed_count = 0
        browser_names = list(self.running_browsers.keys())

        for name in browser_names:
            if self.close_browser(name):
                closed_count += 1

        return closed_count

    def _is_port_in_use(self, port: int) -> bool:
        """检查端口是否被占用"""
        try:
            import socket
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('127.0.0.1', port))
                return result == 0
        except Exception:
            return False

    def _kill_processes_using_port(self, port: int):
        """已禁用：不再终止占用端口的进程以保护浏览器数据"""
        print(f"🛡️ 跳过端口 {port} 清理，保护浏览器数据")
        # 此方法已被禁用以保护浏览器数据
        # 不再进行任何端口清理操作
        return

    def _force_cleanup_profile_processes(self, profile_name: str):
        """已禁用：不再进行强制清理以保护浏览器数据"""
        print(f"🛡️ 跳过强制清理 {profile_name}，保护浏览器数据")
        # 此方法已被禁用以保护浏览器数据
        # 不再进行任何强制清理操作
        return

    def _check_existing_browser_instance(self, profile_name: str, debug_port: int) -> Optional[Dict]:
        """检查是否存在现有的浏览器实例"""
        try:
            profile = self.config_manager.get_profile(profile_name)
            if not profile:
                return None

            profile_path = profile['path']

            # 使用psutil查找现有的Chrome进程
            try:
                import psutil

                for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                    try:
                        if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                            cmdline = proc.info['cmdline']
                            if cmdline:
                                # 检查是否使用了该配置路径和调试端口
                                has_profile_path = any(profile_path in str(arg) for arg in cmdline)
                                has_debug_port = any(str(debug_port) in str(arg) for arg in cmdline)

                                if has_profile_path and has_debug_port:
                                    print(f"发现现有Chrome进程 PID: {proc.info['pid']}")

                                    # 创建浏览器信息对象
                                    browser_info = {
                                        "pid": proc.info['pid'],
                                        "debug_port": debug_port,
                                        "process": proc,
                                        "profile_name": profile_name,
                                        "started_at": proc.info['create_time']
                                    }

                                    # 验证浏览器是否可以连接
                                    if self._test_browser_connection(debug_port):
                                        return browser_info
                                    else:
                                        print(f"浏览器进程存在但无法连接，可能需要重启")
                                        return None
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        pass

            except ImportError:
                print("psutil未安装，无法检查现有浏览器实例")

            return None

        except Exception as e:
            print(f"检查现有浏览器实例失败: {e}")
            return None

    def _test_browser_connection(self, debug_port: int) -> bool:
        """测试浏览器调试连接是否可用"""
        try:
            import requests
            import json

            # 尝试连接到Chrome DevTools
            response = requests.get(f'http://127.0.0.1:{debug_port}/json', timeout=3)
            if response.status_code == 200:
                tabs = response.json()
                return len(tabs) > 0
            return False

        except Exception:
            return False

    def _is_target_profile_using_port(self, profile_name: str, port: int) -> bool:
        """检查端口是否被目标配置的浏览器占用"""
        try:
            profile = self.config_manager.get_profile(profile_name)
            if not profile:
                return False

            profile_path = profile['path']

            try:
                import psutil

                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                            cmdline = proc.info['cmdline']
                            if cmdline:
                                # 检查是否同时使用了目标配置路径和端口
                                has_profile_path = any(profile_path in str(arg) for arg in cmdline)
                                has_port = any(str(port) in str(arg) for arg in cmdline)

                                if has_profile_path and has_port:
                                    return True
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        pass

            except ImportError:
                pass

            return False

        except Exception:
            return False

    def _comprehensive_browser_check(self, browser_info: Dict) -> bool:
        """综合检查浏览器是否还在运行（多重验证，避免误判）"""
        try:
            # 1. 检查process字段
            process = browser_info.get("process")
            if process and hasattr(process, 'poll') and process.poll() is None:
                return True

            # 2. 检查driver的service进程（Stealth模式）
            driver = browser_info.get("driver")
            if driver:
                try:
                    if hasattr(driver, 'service') and driver.service:
                        service_process = driver.service.process
                        if service_process and service_process.poll() is None:
                            return True
                except:
                    pass

            # 3. 检查debug_port是否还在监听
            debug_port = browser_info.get("debug_port")
            if debug_port:
                try:
                    import socket
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.settimeout(0.3)  # 快速检查
                        result = s.connect_ex(('127.0.0.1', debug_port))
                        if result == 0:
                            return True
                except:
                    pass

            # 4. 使用psutil检查进程是否存在（如果有PID）
            pid = browser_info.get("pid")
            if pid:
                try:
                    import psutil
                    if psutil.pid_exists(pid):
                        proc = psutil.Process(pid)
                        if proc.is_running() and 'chrome' in proc.name().lower():
                            return True
                except:
                    pass

            return False

        except Exception:
            return False

    def force_save_browser_state(self, profile_name: str) -> bool:
        """强制保存浏览器状态（在关闭前调用）"""
        try:
            if profile_name not in self.running_browsers:
                return False

            browser_info = self.running_browsers[profile_name]
            profile = self.config_manager.get_profile(profile_name)

            if not profile:
                return False

            print(f"💾 强制保存浏览器状态: {profile_name}")

            # 1. 更新Preferences文件，确保会话恢复设置正确
            self._ensure_session_restore_config(profile)

            # 2. 如果是Stealth模式，尝试通过driver保存状态
            if browser_info.get("stealth_mode") and "driver" in browser_info:
                try:
                    driver = browser_info["driver"]
                    # 执行JavaScript来强制保存本地存储
                    driver.execute_script("""
                        // 强制保存localStorage
                        if (typeof(Storage) !== "undefined") {
                            localStorage.setItem('browser_state_saved', new Date().toISOString());
                        }

                        // 强制保存sessionStorage
                        if (typeof(sessionStorage) !== "undefined") {
                            sessionStorage.setItem('session_state_saved', new Date().toISOString());
                        }

                        // 触发beforeunload事件，让页面保存状态
                        window.dispatchEvent(new Event('beforeunload'));
                    """)
                    print("✅ 已通过JavaScript强制保存状态")
                except Exception as e:
                    print(f"⚠️ JavaScript保存状态失败: {e}")

            # 3. 创建状态标记文件
            try:
                profile_path = profile['path']
                state_file = os.path.join(profile_path, 'browser_state.json')
                state_data = {
                    'last_saved': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'profile_name': profile_name,
                    'stealth_mode': browser_info.get("stealth_mode", False),
                    'fingerprint': browser_info.get("fingerprint", {}),
                    'clean_exit': True
                }

                with open(state_file, 'w', encoding='utf-8') as f:
                    import json
                    json.dump(state_data, f, indent=2)

                print(f"✅ 已创建状态标记文件: {state_file}")

            except Exception as e:
                print(f"⚠️ 创建状态文件失败: {e}")

            return True

        except Exception as e:
            print(f"❌ 强制保存状态失败: {e}")
            return False

    def _check_last_exit_status(self, profile: Dict):
        """检查上次退出状态，确保会话恢复正常工作"""
        try:
            profile_path = profile['path']
            state_file = os.path.join(profile_path, 'browser_state.json')
            preferences_file = os.path.join(profile_path, 'Preferences')

            # 检查状态文件
            if os.path.exists(state_file):
                try:
                    with open(state_file, 'r', encoding='utf-8') as f:
                        import json
                        state_data = json.load(f)

                    if state_data.get('clean_exit', False):
                        print(f"✅ 上次正常退出，会话恢复应该正常工作")
                    else:
                        print(f"⚠️ 上次可能异常退出，强制设置会话恢复")
                        self._force_session_restore_settings(profile)

                except Exception as e:
                    print(f"⚠️ 读取状态文件失败: {e}")
                    self._force_session_restore_settings(profile)
            else:
                print(f"💡 首次启动或状态文件不存在，设置会话恢复")
                self._force_session_restore_settings(profile)

            # 确保Preferences文件中的退出状态正确
            if os.path.exists(preferences_file):
                try:
                    with open(preferences_file, 'r', encoding='utf-8') as f:
                        import json
                        prefs = json.load(f)

                    # 强制设置为正常退出
                    if 'profile' not in prefs:
                        prefs['profile'] = {}
                    prefs['profile']['exit_type'] = "Normal"
                    prefs['profile']['exited_cleanly'] = True

                    # 确保会话恢复设置
                    if 'session' not in prefs:
                        prefs['session'] = {}
                    prefs['session']['restore_on_startup'] = 1

                    # 保存修改
                    with open(preferences_file, 'w', encoding='utf-8') as f:
                        json.dump(prefs, f, indent=2)

                    print(f"✅ 已修正Preferences中的退出状态")

                except Exception as e:
                    print(f"⚠️ 修正Preferences失败: {e}")

        except Exception as e:
            print(f"⚠️ 检查退出状态失败: {e}")

    def _force_session_restore_settings(self, profile: Dict):
        """强制设置会话恢复配置"""
        try:
            profile_path = profile['path']
            preferences_file = os.path.join(profile_path, 'Preferences')

            # 强制会话恢复配置
            force_config = {
                "session": {
                    "restore_on_startup": 1,
                    "startup_urls": [],
                    "restore_on_startup_migrated": True
                },
                "profile": {
                    "exit_type": "Normal",
                    "exited_cleanly": True,
                    "name": profile['name']
                },
                "browser": {
                    "show_home_button": True,
                    "check_default_browser": False,
                    "has_seen_welcome_page": True
                }
            }

            if os.path.exists(preferences_file):
                # 更新现有配置
                try:
                    with open(preferences_file, 'r', encoding='utf-8') as f:
                        import json
                        prefs = json.load(f)

                    # 强制更新关键设置
                    prefs.update(force_config)

                    with open(preferences_file, 'w', encoding='utf-8') as f:
                        json.dump(prefs, f, indent=2)

                    print(f"✅ 已强制更新会话恢复设置")

                except Exception as e:
                    print(f"⚠️ 强制更新失败: {e}")
            else:
                # 创建新配置
                try:
                    os.makedirs(profile_path, exist_ok=True)
                    with open(preferences_file, 'w', encoding='utf-8') as f:
                        import json
                        json.dump(force_config, f, indent=2)

                    print(f"✅ 已创建强制会话恢复配置")

                except Exception as e:
                    print(f"⚠️ 创建强制配置失败: {e}")

        except Exception as e:
            print(f"⚠️ 强制设置会话恢复失败: {e}")
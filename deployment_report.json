{"timestamp": "2025-07-07T22:13:31.628727", "summary": {"total_checks": 48, "passed": 20, "failed": 2, "warnings": 0, "success_rate": 41.66666666666667}, "details": [{"timestamp": "2025-07-07 22:12:31", "level": "INFO", "message": "🚀 开始部署检查..."}, {"timestamp": "2025-07-07 22:12:31", "level": "INFO", "message": "\n==================== 必需文件 ===================="}, {"timestamp": "2025-07-07 22:12:31", "level": "INFO", "message": "🔍 检查必需文件..."}, {"timestamp": "2025-07-07 22:12:31", "level": "PASS", "message": "✅ 认证服务脚本: auth_service.py"}, {"timestamp": "2025-07-07 22:12:31", "level": "PASS", "message": "✅ 任务处理服务脚本: autoback.py"}, {"timestamp": "2025-07-07 22:12:31", "level": "PASS", "message": "✅ 认证客户端: auth_client.py"}, {"timestamp": "2025-07-07 22:12:31", "level": "PASS", "message": "✅ 服务启动脚本: start_services.py"}, {"timestamp": "2025-07-07 22:12:31", "level": "PASS", "message": "✅ 前端配置文件: frontend_config.js"}, {"timestamp": "2025-07-07 22:12:31", "level": "PASS", "message": "✅ 集成测试脚本: test_service_integration.py"}, {"timestamp": "2025-07-07 22:12:31", "level": "INFO", "message": "✅ 必需文件 通过"}, {"timestamp": "2025-07-07 22:12:31", "level": "INFO", "message": "\n==================== 服务连接性 ===================="}, {"timestamp": "2025-07-07 22:12:31", "level": "INFO", "message": "🔍 检查服务连接性..."}, {"timestamp": "2025-07-07 22:12:33", "level": "PASS", "message": "✅ 认证服务 连接正常 (运行时间: 0.03小时)"}, {"timestamp": "2025-07-07 22:12:37", "level": "PASS", "message": "✅ 任务处理服务 连接正常 (运行时间: 0.00小时)"}, {"timestamp": "2025-07-07 22:12:37", "level": "INFO", "message": "✅ 服务连接性 通过"}, {"timestamp": "2025-07-07 22:12:37", "level": "INFO", "message": "\n==================== 服务集成 ===================="}, {"timestamp": "2025-07-07 22:12:37", "level": "INFO", "message": "🔍 检查服务集成..."}, {"timestamp": "2025-07-07 22:12:41", "level": "PASS", "message": "✅ 任务服务可以连接到认证服务"}, {"timestamp": "2025-07-07 22:12:41", "level": "INFO", "message": "✅ 服务集成 通过"}, {"timestamp": "2025-07-07 22:12:41", "level": "INFO", "message": "\n==================== 认证流程 ===================="}, {"timestamp": "2025-07-07 22:12:41", "level": "INFO", "message": "🔍 检查认证流程..."}, {"timestamp": "2025-07-07 22:12:44", "level": "PASS", "message": "✅ 无效登录正确拒绝"}, {"timestamp": "2025-07-07 22:12:46", "level": "PASS", "message": "✅ 无效token正确拒绝"}, {"timestamp": "2025-07-07 22:12:46", "level": "INFO", "message": "✅ 认证流程 通过"}, {"timestamp": "2025-07-07 22:12:46", "level": "INFO", "message": "\n==================== 任务服务认证 ===================="}, {"timestamp": "2025-07-07 22:12:46", "level": "INFO", "message": "🔍 检查任务服务认证集成..."}, {"timestamp": "2025-07-07 22:12:48", "level": "PASS", "message": "✅ 无token访问正确拒绝"}, {"timestamp": "2025-07-07 22:12:54", "level": "PASS", "message": "✅ 无效token访问正确拒绝"}, {"timestamp": "2025-07-07 22:12:54", "level": "INFO", "message": "✅ 任务服务认证 通过"}, {"timestamp": "2025-07-07 22:12:54", "level": "INFO", "message": "\n==================== API端点 ===================="}, {"timestamp": "2025-07-07 22:12:54", "level": "INFO", "message": "🔍 检查API端点..."}, {"timestamp": "2025-07-07 22:12:56", "level": "PASS", "message": "✅ 认证服务健康检查 正常"}, {"timestamp": "2025-07-07 22:12:58", "level": "PASS", "message": "✅ 认证服务统计 正常"}, {"timestamp": "2025-07-07 22:13:02", "level": "PASS", "message": "✅ 任务服务健康检查 正常"}, {"timestamp": "2025-07-07 22:13:05", "level": "PASS", "message": "✅ 公告服务 正常"}, {"timestamp": "2025-07-07 22:13:07", "level": "PASS", "message": "✅ 系统状态 正常"}, {"timestamp": "2025-07-07 22:13:07", "level": "INFO", "message": "✅ API端点 通过"}, {"timestamp": "2025-07-07 22:13:07", "level": "INFO", "message": "\n==================== 性能检查 ===================="}, {"timestamp": "2025-07-07 22:13:07", "level": "INFO", "message": "🔍 检查性能..."}, {"timestamp": "2025-07-07 22:13:13", "level": "FAIL", "message": "❌ 认证服务 性能较差 (平均: 2031ms)"}, {"timestamp": "2025-07-07 22:13:25", "level": "FAIL", "message": "❌ 任务服务 性能较差 (平均: 4082ms)"}, {"timestamp": "2025-07-07 22:13:25", "level": "INFO", "message": "❌ 性能检查 失败"}, {"timestamp": "2025-07-07 22:13:25", "level": "INFO", "message": "\n==================== 数据库连接 ===================="}, {"timestamp": "2025-07-07 22:13:25", "level": "INFO", "message": "🔍 检查数据库连接..."}, {"timestamp": "2025-07-07 22:13:27", "level": "PASS", "message": "✅ 认证服务数据库连接正常"}, {"timestamp": "2025-07-07 22:13:31", "level": "PASS", "message": "✅ 任务服务数据库连接正常"}, {"timestamp": "2025-07-07 22:13:31", "level": "INFO", "message": "✅ 数据库连接 通过"}, {"timestamp": "2025-07-07 22:13:31", "level": "INFO", "message": "📄 生成部署报告..."}], "recommendations": ["⚠️ 有 2 项检查失败，需要修复"]}
/**
 * API服务层 - 与后端autoback.py通信
 */
import axios from 'axios';
import { getApiBaseUrlSync } from '../config';

// 创建axios实例 - 动态获取baseURL，增加重试机制
const apiClient = axios.create({
  timeout: 30000, // 增加到30秒超时时间
  headers: {
    'Content-Type': 'application/json',
  },
  // 添加更多网络配置
  maxRedirects: 5,
  validateStatus: (status) => status < 500, // 只有5xx错误才被认为是错误
});

// 创建管理界面客户端 - 任务状态查询
const getManagementBaseUrl = () => {
  // 根据当前环境动态构建管理界面地址
  const currentProtocol = window.location.protocol;
  const currentHost = window.location.hostname;

  // 在开发环境中使用 localhost:5002 (autoback.py)
  if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
    return `${currentProtocol}//${currentHost}:5002/api`;
  } else {
    // 生产环境：使用生产服务器，避免端口号问题
    return getApiBaseUrlSync();
  }
};

const managementClient = axios.create({
  baseURL: getManagementBaseUrl(),
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
  maxRedirects: 5,
  validateStatus: (status) => status < 500,
});

// 动态设置生产服务器baseURL
apiClient.defaults.baseURL = getApiBaseUrlSync();

// 重试配置 - 修改为不重试，一次即可
const MAX_RETRIES = 0; // 🔥 修改为0，不重试
const RETRY_DELAY = 1500; // 保持延迟配置（虽然不会用到）

// 重试函数 - 改进的指数退避策略
const retryRequest = async (config: any, retryCount = 0): Promise<any> => {
  try {
    return await apiClient(config);
  } catch (error: any) {
    if (retryCount < MAX_RETRIES && shouldRetry(error)) {
      // 指数退避：1.5s, 3s, 6s, 12s
      const delay = RETRY_DELAY * Math.pow(2, retryCount);
      console.log(`请求失败，${delay}ms后进行第${retryCount + 1}次重试...`);
      console.log(`错误详情: ${error.message || error.code || 'Unknown error'}`);

      await new Promise(resolve => setTimeout(resolve, delay));
      return retryRequest(config, retryCount + 1);
    }
    throw error;
  }
};

// 判断是否应该重试 - 更宽松的重试条件
const shouldRetry = (error: any): boolean => {
  // 网络错误、超时错误、连接错误或5xx服务器错误时重试
  if (!error.response) {
    // 网络层面的错误都重试
    return true;
  }

  // HTTP状态码错误
  const status = error.response.status;
  return status >= 500 || // 服务器错误
         status === 408 || // 请求超时
         status === 429 || // 请求过多
         status === 502 || // 网关错误
         status === 503 || // 服务不可用
         status === 504;   // 网关超时
};

// 请求拦截器 - 动态更新baseURL和添加认证头
apiClient.interceptors.request.use(
  (config) => {
    // 动态更新baseURL，确保使用最新配置
    config.baseURL = getApiBaseUrlSync();

    console.log('发送API请求:', config.method?.toUpperCase(), config.baseURL + config.url);

    // 从localStorage获取token并添加到请求头
    const userStorage = localStorage.getItem('user-storage');
    if (userStorage) {
      try {
        const userData = JSON.parse(userStorage);
        if (userData.state?.token) {
          config.headers.Authorization = `Bearer ${userData.state.token}`;
        }
      } catch (error) {
        console.warn('解析用户token失败:', error);
      }
    }

    return config;
  },
  (error) => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器 - 改进错误处理
apiClient.interceptors.response.use(
  (response) => {
    console.log('API响应:', response.status, response.data);
    return response;
  },
  (error) => {
    console.error('响应错误:', error.response?.status, error.response?.data || error.message);

    // 改进错误消息 - 更详细的错误分类
    if (!error.response) {
      // 网络错误
      if (error.code === 'ECONNREFUSED') {
        error.message = '连接被拒绝，服务器可能未启动';
      } else if (error.code === 'ETIMEDOUT' || error.message?.includes('timeout')) {
        error.message = '请求超时，网络连接可能不稳定';
      } else if (error.code === 'ENOTFOUND') {
        error.message = '无法解析服务器地址';
      } else if (error.message?.includes('ERR_CONNECTION_CLOSED')) {
        error.message = '连接被意外关闭，可能是网络不稳定';
      } else if (error.message?.includes('Failed to fetch')) {
        error.message = '网络请求失败，请检查网络连接';
      } else {
        error.message = `网络错误: ${error.message || error.code || 'Unknown network error'}`;
      }
    } else if (error.response.status >= 500) {
      error.message = `服务器错误 (${error.response.status})，请稍后重试`;
    } else if (error.response.status === 404) {
      error.message = '请求的资源不存在';
    } else if (error.response.status === 401) {
      error.message = '认证失败，请重新登录';
    }

    return Promise.reject(error);
  }
);

// 任务状态枚举
export enum TaskStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  SUCCESS = 'success',
  FAILED = 'failed'
}

// 数据类型定义
export interface Task {
  id: string;
  email: string;
  status: TaskStatus;
  message?: string;
  error?: string;
  created_at: string;
  completed_at?: string;
}

export interface SubmitResponse {
  success: boolean;
  task_id?: string;
  message?: string;
  error?: string;
}

export interface SystemStatus {
  success: boolean;
  is_processing: boolean;
  current_email?: string;
  started_at?: string;
  queue_length: number;
  available_profiles: number;
}

export interface QueueStatus {
  success: boolean;
  queue: Task[];
  total_count: number;
}

// API方法
export const emailAPI = {
  /**
   * 提交邮箱地址（需要用户认证）
   */
  submitEmail: async (email: string): Promise<SubmitResponse> => {
    try {
      // 获取用户token
      const userStorage = localStorage.getItem('user-storage');
      if (!userStorage) {
        throw new Error('请先登录');
      }

      const userData = JSON.parse(userStorage);
      const token = userData.state?.token;
      if (!token) {
        throw new Error('请先登录');
      }

      const response = await retryRequest({
        method: 'post',
        url: '/submit',
        data: { email },
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || error.response?.data?.error || error.message || '提交失败');
    }
  },

  /**
   * 获取任务状态（需要用户认证）
   */
  getTaskStatus: async (taskId: string): Promise<Task> => {
    try {
      // 获取用户token
      const userStorage = localStorage.getItem('user-storage');
      if (!userStorage) {
        throw new Error('请先登录');
      }

      const userData = JSON.parse(userStorage);
      const token = userData.state?.token;
      if (!token) {
        throw new Error('请先登录');
      }

      // 使用管理界面客户端查询任务状态
      console.log('发送任务状态查询请求:', `/task/${taskId}/status`);
      const response = await managementClient.get(`/task/${taskId}/status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('任务状态API响应:', response.status, response.data);
      const data = response.data;

      // 转换后端数据格式到前端格式
      if (data.success) {
        return {
          id: data.task_id.toString(),
          email: data.email,
          status: data.status === 'pending' ? TaskStatus.PENDING :
                  data.status === 'processing' ? TaskStatus.PROCESSING :
                  data.status === 'success' ? TaskStatus.SUCCESS :
                  data.status === 'failed' ? TaskStatus.FAILED : TaskStatus.PENDING,
          message: data.result_message,
          error: data.status === 'failed' ? data.result_message : undefined,
          created_at: data.created_at,
          completed_at: data.completed_at
        };
      } else {
        throw new Error(data.message || '获取任务状态失败');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取任务状态失败');
    }
  },

  /**
   * 获取系统状态 - 直接从管理界面获取
   */
  getSystemStatus: async (): Promise<SystemStatus> => {
    try {
      // 使用管理界面客户端查询系统状态
      console.log('发送系统状态查询请求: /system/status');
      const response = await managementClient.get('/system/status');
      console.log('系统状态API响应:', response.status, response.data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || '获取系统状态失败');
    }
  },

  /**
   * 获取任务队列 - 直接从管理界面获取
   */
  getQueue: async (): Promise<QueueStatus> => {
    try {
      // 获取用户token
      const userStorage = localStorage.getItem('user-storage');
      if (!userStorage) {
        throw new Error('请先登录');
      }

      const userData = JSON.parse(userStorage);
      const token = userData.state?.token;
      if (!token) {
        throw new Error('请先登录');
      }

      // 使用管理界面客户端查询队列
      console.log('发送队列查询请求: /queue/list');
      const response = await managementClient.get('/queue/list', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('队列API响应:', response.status, response.data);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || '获取队列失败');
    }
  },

  /**
   * 测试API连接
   */
  testConnection: async (): Promise<boolean> => {
    try {
      const response = await retryRequest({
        method: 'get',
        url: '/test'
      });
      return response.data.success;
    } catch (error) {
      console.warn('连接测试失败:', error);
      return false;
    }
  }
};

// 工具函数
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const formatDateTime = (dateString: string): string => {
  try {
    // 如果后端已经返回了格式化的北京时间字符串，直接使用
    if (dateString && dateString.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
      return dateString;
    }

    // 否则按照原来的逻辑处理
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  } catch {
    return dateString;
  }
};

export const getStatusColor = (status: TaskStatus): string => {
  switch (status) {
    case TaskStatus.PENDING:
      return '#faad14'; // 橙色
    case TaskStatus.PROCESSING:
      return '#1890ff'; // 蓝色
    case TaskStatus.SUCCESS:
      return '#52c41a'; // 绿色
    case TaskStatus.FAILED:
      return '#ff4d4f'; // 红色
    default:
      return '#d9d9d9'; // 灰色
  }
};

export const getStatusText = (status: TaskStatus): string => {
  switch (status) {
    case TaskStatus.PENDING:
      return '等待处理';
    case TaskStatus.PROCESSING:
      return '正在处理';
    case TaskStatus.SUCCESS:
      return '处理成功';
    case TaskStatus.FAILED:
      return '处理失败';
    default:
      return '未知状态';
  }
};

"""
管理员公告管理工具
用于管理公告缓存、查看缓存状态、手动刷新等
"""
import requests
import time
import json
from datetime import datetime


class AnnouncementManager:
    """公告管理器"""
    
    def __init__(self, api_base="http://localhost:5001"):
        self.api_base = api_base
    
    def get_cache_status(self):
        """获取缓存状态"""
        try:
            response = requests.get(f"{self.api_base}/api/announcements", timeout=10)
            if response.status_code == 200:
                data = response.json()
                return {
                    'success': True,
                    'count': data.get('count', 0),
                    'source': data.get('source', 'unknown'),
                    'cache_age': data.get('cache_age', 0),
                    'cache_status': data.get('cache_status', 'unknown'),
                    'announcements': data.get('data', [])
                }
            else:
                return {'success': False, 'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def refresh_cache(self):
        """刷新公告缓存"""
        try:
            response = requests.post(f"{self.api_base}/api/admin/refresh-announcements", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                return {'success': False, 'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def display_cache_status(self):
        """显示缓存状态"""
        print("📊 公告缓存状态")
        print("-" * 40)
        
        status = self.get_cache_status()
        
        if status['success']:
            print(f"✅ 缓存状态: 正常")
            print(f"📋 公告数量: {status['count']}")
            print(f"🏷️ 数据源: {status['source']}")
            print(f"⏰ 缓存年龄: {status['cache_age']}秒")
            print(f"📈 缓存状态: {status['cache_status']}")
            
            # 显示公告列表
            print(f"\n📝 公告列表:")
            for i, announcement in enumerate(status['announcements'], 1):
                title = announcement.get('title', 'N/A')
                ann_type = announcement.get('type', 'unknown')
                print(f"  {i}. {title} ({ann_type})")
        else:
            print(f"❌ 获取缓存状态失败: {status['error']}")
    
    def refresh_and_show_result(self):
        """刷新缓存并显示结果"""
        print("🔄 刷新公告缓存...")
        
        result = self.refresh_cache()
        
        if result.get('success'):
            print("✅ 缓存刷新成功")
            print(f"📊 旧数量: {result.get('old_count', 0)}")
            print(f"📊 新数量: {result.get('new_count', 0)}")
            print(f"💬 消息: {result.get('message', 'N/A')}")
        else:
            print(f"❌ 缓存刷新失败: {result.get('error', 'Unknown error')}")
    
    def monitor_cache_performance(self, duration=60):
        """监控缓存性能"""
        print(f"📈 监控缓存性能 ({duration}秒)...")
        
        start_time = time.time()
        request_count = 0
        response_times = []
        errors = 0
        
        while time.time() - start_time < duration:
            try:
                req_start = time.time()
                response = requests.get(f"{self.api_base}/api/announcements", timeout=5)
                elapsed = time.time() - req_start
                
                if response.status_code == 200:
                    response_times.append(elapsed * 1000)
                    request_count += 1
                    
                    if request_count % 10 == 0:
                        avg_time = sum(response_times[-10:]) / min(10, len(response_times))
                        print(f"  请求 {request_count}: 平均响应时间 {avg_time:.1f}ms")
                else:
                    errors += 1
                    
            except Exception as e:
                errors += 1
                print(f"  请求异常: {e}")
            
            time.sleep(1)  # 每秒一个请求
        
        # 统计结果
        if response_times:
            avg_time = sum(response_times) / len(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            
            print(f"\n📊 性能监控结果:")
            print(f"  总请求数: {request_count}")
            print(f"  错误数: {errors}")
            print(f"  平均响应时间: {avg_time:.1f}ms")
            print(f"  最快响应: {min_time:.1f}ms")
            print(f"  最慢响应: {max_time:.1f}ms")
            print(f"  成功率: {(request_count/(request_count+errors))*100:.1f}%")
    
    def interactive_menu(self):
        """交互式菜单"""
        while True:
            print("\n" + "=" * 50)
            print("🛠️ 公告管理工具")
            print("=" * 50)
            print("1. 查看缓存状态")
            print("2. 刷新缓存")
            print("3. 性能监控 (60秒)")
            print("4. 快速性能测试 (10次请求)")
            print("5. 退出")
            
            try:
                choice = input("\n请选择操作 (1-5): ").strip()
                
                if choice == '1':
                    self.display_cache_status()
                    
                elif choice == '2':
                    self.refresh_and_show_result()
                    
                elif choice == '3':
                    self.monitor_cache_performance(60)
                    
                elif choice == '4':
                    self.quick_performance_test()
                    
                elif choice == '5':
                    print("👋 退出管理工具")
                    break
                    
                else:
                    print("❌ 无效选择，请输入 1-5")
                    
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出管理工具")
                break
            except Exception as e:
                print(f"❌ 操作异常: {e}")
    
    def quick_performance_test(self):
        """快速性能测试"""
        print("⚡ 快速性能测试 (10次请求)...")
        
        response_times = []
        errors = 0
        
        for i in range(10):
            try:
                start_time = time.time()
                response = requests.get(f"{self.api_base}/api/announcements", timeout=5)
                elapsed = time.time() - start_time
                
                if response.status_code == 200:
                    response_times.append(elapsed * 1000)
                    print(f"  请求 {i+1}: {elapsed*1000:.1f}ms")
                else:
                    errors += 1
                    print(f"  请求 {i+1}: 失败 (HTTP {response.status_code})")
                    
            except Exception as e:
                errors += 1
                print(f"  请求 {i+1}: 异常 - {e}")
            
            time.sleep(0.1)
        
        if response_times:
            avg_time = sum(response_times) / len(response_times)
            print(f"\n📊 快速测试结果:")
            print(f"  成功请求: {len(response_times)}/10")
            print(f"  平均响应时间: {avg_time:.1f}ms")
            print(f"  最快响应: {min(response_times):.1f}ms")
            print(f"  最慢响应: {max(response_times):.1f}ms")
            
            if avg_time < 50:
                print("  🎉 性能评级: 优秀")
            elif avg_time < 200:
                print("  ✅ 性能评级: 良好")
            else:
                print("  ⚠️ 性能评级: 需要优化")


def main():
    """主函数"""
    print("🚀 公告管理工具启动")
    
    # 检查服务器连接
    manager = AnnouncementManager()
    
    try:
        response = requests.get(f"{manager.api_base}/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ 连接到autoback.py服务器成功")
        else:
            print(f"⚠️ 服务器响应异常: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请确保autoback.py正在运行在 http://localhost:5001")
        return
    
    # 显示初始状态
    print("\n📊 当前缓存状态:")
    manager.display_cache_status()
    
    # 启动交互式菜单
    manager.interactive_menu()


if __name__ == "__main__":
    main()

{"development": {"api": {"host": "0.0.0.0", "port": 5001, "base_url": "/api"}, "frontend": {"port": 3000}}, "production": {"api": {"host": "0.0.0.0", "port": 5001, "base_url": "/api"}, "frontend": {"port": 3000}, "tunnel": {"enabled": true, "domain": "aug8.xyz", "api_path": "/api/*", "frontend_path": "/*"}}, "hybrid": {"production_server": {"host": "127.0.0.1", "port": 5001, "base_url": "/api", "threads": 6, "description": "生产级API服务器，专注高性能处理"}, "management_gui": {"host": "127.0.0.1", "port": 5002, "base_url": "/api", "description": "管理界面，按需启动进行账号和配置管理"}, "shared_resources": {"sqlite_db": "tasks.db", "profiles_dir": "browser_profiles", "config_file": "config.json"}}}
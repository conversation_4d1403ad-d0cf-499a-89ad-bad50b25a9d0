/**
 * 加载屏幕组件
 * 在服务器连接和数据库加载期间显示动画
 */
import React, { useState, useEffect } from 'react';
import { Progress, Typography, Space, Card } from 'antd';
import { LoadingOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import '../styles/auth-loading.css';

const { Title, Text } = Typography;

export interface LoadingStep {
  id: string;
  name: string;
  status: 'waiting' | 'loading' | 'success' | 'error';
  progress?: number;
  error?: string;
}

interface LoadingScreenProps {
  visible: boolean;
  steps: LoadingStep[];
  onComplete?: () => void;
  onError?: (error: string) => void;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({
  visible,
  steps,
  onComplete,
  onError
}) => {
  const [overallProgress, setOverallProgress] = useState(0);

  // 计算整体进度
  useEffect(() => {
    const completedSteps = steps.filter(step => step.status === 'success').length;
    const totalSteps = steps.length;
    const progress = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;
    setOverallProgress(progress);

    // 当前步骤索引（用于UI显示，但当前实现中未使用）
    // const loadingStepIndex = steps.findIndex(step => step.status === 'loading');

    // 检查是否全部完成
    if (completedSteps === totalSteps && totalSteps > 0) {
      setTimeout(() => {
        onComplete?.();
      }, 500); // 延迟500ms让用户看到100%完成
    }

    // 检查是否有错误
    const errorStep = steps.find(step => step.status === 'error');
    if (errorStep) {
      onError?.(errorStep.error || '加载失败');
    }
  }, [steps, onComplete, onError]);

  // 获取步骤图标
  const getStepIcon = (step: LoadingStep) => {
    switch (step.status) {
      case 'loading':
        return <LoadingOutlined style={{ color: '#1890ff' }} />;
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <div style={{ width: 14, height: 14, borderRadius: '50%', backgroundColor: '#d9d9d9' }} />;
    }
  };

  // 获取步骤文本颜色
  const getStepTextColor = (step: LoadingStep) => {
    switch (step.status) {
      case 'loading':
        return '#1890ff';
      case 'success':
        return '#52c41a';
      case 'error':
        return '#ff4d4f';
      default:
        return '#8c8c8c';
    }
  };

  if (!visible) {
    return null;
  }

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 9999,
        backdropFilter: 'blur(4px)'
      }}
    >
      <Card
        style={{
          width: 480,
          maxWidth: '90vw',
          textAlign: 'center',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
          borderRadius: 16
        }}
        bodyStyle={{ padding: '32px' }}
      >
        {/* 标题 */}
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
              风车AUG云激活
            </Title>
            <Text type="secondary">正在初始化系统...</Text>
          </div>

          {/* 整体进度条 */}
          <div style={{ width: '100%' }}>
            <Progress
              percent={overallProgress}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
              trailColor="#f0f0f0"
              strokeWidth={8}
              showInfo={false}
            />
            <div style={{ marginTop: 8 }}>
              <Text style={{ fontSize: 14, color: '#666' }}>
                {overallProgress}% 完成
              </Text>
            </div>
          </div>

          {/* 步骤列表 */}
          <div style={{ width: '100%', textAlign: 'left' }}>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              {steps.map((step, index) => (
                <div
                  key={step.id}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '8px 12px',
                    borderRadius: 8,
                    backgroundColor: step.status === 'loading' ? '#f6ffed' : 'transparent',
                    border: step.status === 'loading' ? '1px solid #b7eb8f' : '1px solid transparent',
                    transition: 'all 0.3s ease'
                  }}
                >
                  <div style={{ marginRight: 12, minWidth: 14 }}>
                    {getStepIcon(step)}
                  </div>
                  <div style={{ flex: 1 }}>
                    <Text
                      style={{
                        color: getStepTextColor(step),
                        fontWeight: step.status === 'loading' ? 500 : 'normal'
                      }}
                    >
                      {step.name}
                    </Text>
                    {step.status === 'error' && step.error && (
                      <div style={{ marginTop: 4 }}>
                        <Text type="danger" style={{ fontSize: 12 }}>
                          {step.error}
                        </Text>
                      </div>
                    )}
                    {step.status === 'loading' && step.progress !== undefined && (
                      <div style={{ marginTop: 4 }}>
                        <Progress
                          percent={step.progress}
                          size="small"
                          showInfo={false}
                          strokeColor="#1890ff"
                        />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </Space>
          </div>

          {/* 底部提示 */}
          <div style={{ marginTop: 16 }}>
            <Text type="secondary" style={{ fontSize: 12 }}>
              首次加载可能需要几秒钟，请耐心等待...
            </Text>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default LoadingScreen;

/**
 * 登录注册专用加载动画组件
 * 提供漂亮的进度动画，让用户愿意等待
 */
export interface AuthLoadingProps {
  visible: boolean;
  type: 'login' | 'register';
  onCancel?: () => void;
  onComplete?: () => void;
}

export const AuthLoading: React.FC<AuthLoadingProps> = ({
  visible,
  type,
  onCancel,
  onComplete
}) => {
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);

  const steps = type === 'login'
    ? ['验证用户信息', '连接服务器', '加载用户数据', '登录成功']
    : ['验证注册信息', '检查用户名', '创建账户', '注册完成'];

  // 模拟进度动画
  useEffect(() => {
    if (!visible) {
      setProgress(0);
      setCurrentStep(0);
      setIsCompleted(false);
      return;
    }

    let progressValue = 0;
    let stepIndex = 0;

    const interval = setInterval(() => {
      progressValue += Math.random() * 12 + 3; // 每次增加3-15%

      // 根据进度更新当前步骤
      const newStepIndex = Math.floor((progressValue / 100) * steps.length);
      if (newStepIndex !== stepIndex && newStepIndex < steps.length) {
        stepIndex = newStepIndex;
        setCurrentStep(stepIndex);
      }

      // 限制最大进度为95%，等待实际完成
      const finalProgress = Math.min(progressValue, 95);
      setProgress(finalProgress);

      // 如果达到95%，停止增长
      if (finalProgress >= 95) {
        clearInterval(interval);
        // 模拟最后的完成步骤
        setTimeout(() => {
          setProgress(100);
          setCurrentStep(steps.length - 1);
          setIsCompleted(true);
          // 延迟调用完成回调
          setTimeout(() => {
            onComplete?.();
          }, 800);
        }, 500);
      }
    }, 300);

    return () => clearInterval(interval);
  }, [visible, steps.length, onComplete]);

  if (!visible) {
    return null;
  }

  return (
    <div
      className="auth-loading-container"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000,
        backdropFilter: 'blur(8px)'
      }}
    >
      <Card
        style={{
          width: 420,
          maxWidth: '90vw',
          textAlign: 'center',
          boxShadow: '0 12px 48px rgba(0, 0, 0, 0.2)',
          borderRadius: 20,
          border: 'none',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white'
        }}
        bodyStyle={{
          padding: '40px 32px',
          background: 'rgba(255, 255, 255, 0.95)',
          borderRadius: 20,
          color: '#333'
        }}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 动画图标 */}
          <div style={{ position: 'relative', marginBottom: 16 }}>
            <div
              className={`auth-loading-icon ${isCompleted ? 'completed' : ''}`}
              style={{
                width: 80,
                height: 80,
                margin: '0 auto',
                borderRadius: '50%',
                background: isCompleted
                  ? 'linear-gradient(135deg, #52c41a, #1890ff)'
                  : 'linear-gradient(135deg, #1890ff, #722ed1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: isCompleted
                  ? '0 8px 32px rgba(82, 196, 26, 0.4)'
                  : '0 8px 32px rgba(24, 144, 255, 0.3)'
              }}
            >
              {isCompleted ? (
                <CheckCircleOutlined
                  style={{
                    fontSize: 32,
                    color: 'white'
                  }}
                />
              ) : (
                <LoadingOutlined
                  style={{
                    fontSize: 32,
                    color: 'white'
                  }}
                />
              )}
            </div>

            {/* 外圈动画 */}
            <div
              className="auth-loading-ring"
              style={{
                position: 'absolute',
                top: -10,
                left: '50%',
                transform: 'translateX(-50%)',
                width: 100,
                height: 100,
                borderRadius: '50%',
                border: '2px solid rgba(24, 144, 255, 0.2)'
              }}
            />
          </div>

          {/* 标题和描述 */}
          <div>
            <Title level={3} style={{
              margin: 0,
              color: isCompleted ? '#52c41a' : '#1890ff',
              fontWeight: 600,
              transition: 'color 0.3s ease'
            }}>
              {isCompleted ?
                (type === 'login' ? '登录成功' : '注册成功') :
                (type === 'login' ? '正在登录' : '正在注册')
              }
            </Title>
            <Text style={{ color: '#666', fontSize: 14 }}>
              {isCompleted ?
                (type === 'login' ? '欢迎回来！正在跳转...' : '账户创建成功！正在跳转...') :
                (type === 'login' ? '验证您的身份信息...' : '创建您的专属账户...')
              }
            </Text>
          </div>

          {/* 进度条 */}
          <div style={{ width: '100%' }}>
            <div className={`auth-loading-progress ${isCompleted ? 'completed' : ''}`}>
              <Progress
                percent={Math.round(progress)}
                strokeColor={isCompleted ? {
                  '0%': '#52c41a',
                  '100%': '#52c41a',
                } : {
                  '0%': '#1890ff',
                  '50%': '#722ed1',
                  '100%': '#52c41a',
                }}
                trailColor="#f0f0f0"
                strokeWidth={12}
                showInfo={false}
                style={{
                  marginBottom: 12
                }}
              />
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text style={{ fontSize: 14, color: isCompleted ? '#52c41a' : '#666' }}>
                {Math.round(progress)}% 完成
              </Text>
              <Text style={{ fontSize: 12, color: '#999' }}>
                {isCompleted ? '即将完成' : `预计还需 ${Math.max(1, Math.ceil((100 - progress) / 20))} 秒`}
              </Text>
            </div>
          </div>

          {/* 步骤指示器 */}
          <div style={{ width: '100%' }}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              {steps.map((step, index) => (
                <div
                  key={index}
                  className={`auth-loading-step ${
                    index === currentStep && !isCompleted ? 'active' :
                    (index < currentStep || (isCompleted && index === steps.length - 1)) ? 'completed' : ''
                  }`}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '8px 16px',
                    borderRadius: 12,
                    backgroundColor: index <= currentStep ?
                      (isCompleted && index === steps.length - 1 ? 'rgba(82, 196, 26, 0.1)' : 'rgba(24, 144, 255, 0.1)') :
                      'transparent',
                    border: index === currentStep ? '1px solid rgba(24, 144, 255, 0.3)' :
                           (isCompleted && index === steps.length - 1 ? '1px solid rgba(82, 196, 26, 0.3)' : '1px solid transparent'),
                    transition: 'all 0.4s ease',
                    transform: index <= currentStep ? 'translateX(0)' : 'translateX(-10px)',
                    opacity: index <= currentStep ? 1 : 0.5
                  }}
                >
                  <div style={{ marginRight: 12, minWidth: 20 }}>
                    {index < currentStep || (isCompleted && index === steps.length - 1) ? (
                      <CheckCircleOutlined style={{ color: '#52c41a', fontSize: 16 }} />
                    ) : index === currentStep && !isCompleted ? (
                      <LoadingOutlined style={{ color: '#1890ff', fontSize: 16 }} />
                    ) : (
                      <div
                        style={{
                          width: 16,
                          height: 16,
                          borderRadius: '50%',
                          backgroundColor: '#d9d9d9',
                          border: '2px solid #f0f0f0'
                        }}
                      />
                    )}
                  </div>
                  <Text
                    style={{
                      color: index < currentStep || (isCompleted && index === steps.length - 1) ? '#52c41a' :
                             index === currentStep ? '#1890ff' : '#999',
                      fontWeight: index === currentStep || (isCompleted && index === steps.length - 1) ? 600 : 'normal',
                      fontSize: 14
                    }}
                  >
                    {step}
                  </Text>
                </div>
              ))}
            </Space>
          </div>

          {/* 底部提示和取消按钮 */}
          <div style={{ marginTop: 16 }}>
            <Space direction="vertical" size="small">
              <Text style={{ color: '#999', fontSize: 12 }}>
                {isCompleted ? '✅ 操作已完成' : '🔒 您的信息正在安全传输中'}
              </Text>
              {onCancel && !isCompleted && (
                <button
                  onClick={onCancel}
                  style={{
                    background: 'none',
                    border: '1px solid #d9d9d9',
                    borderRadius: 6,
                    padding: '4px 12px',
                    color: '#666',
                    fontSize: 12,
                    cursor: 'pointer',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = '#1890ff';
                    e.currentTarget.style.color = '#1890ff';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = '#d9d9d9';
                    e.currentTarget.style.color = '#666';
                  }}
                >
                  取消操作
                </button>
              )}
            </Space>
          </div>
        </Space>
      </Card>


    </div>
  );
};

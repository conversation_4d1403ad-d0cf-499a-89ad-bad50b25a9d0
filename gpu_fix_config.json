{"gpu_fix_enabled": true, "description": "GPU错误修复配置 - 禁用硬件加速避免DirectX/ANGLE错误", "chrome_args": ["--disable-gpu", "--disable-gpu-sandbox", "--disable-gpu-process-crash-limit", "--disable-gpu-memory-buffer-video-frames", "--disable-accelerated-2d-canvas", "--disable-accelerated-video-decode", "--disable-software-rasterizer", "--use-gl=swiftshader", "--disable-webgl", "--disable-webgl2", "--disable-3d-apis", "--disable-accelerated-mjpeg-decode", "--disable-accelerated-video-encode"], "created_at": "2025-07-02", "issues_fixed": ["Failed to query ID3D11Device from ANGLE", "GPU process crash", "WebGL context lost", "DirectX initialization failed"]}
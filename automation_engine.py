"""
自动化操作引擎
提供网页自动化操作功能
"""
import time
from typing import Dict, List, Optional, Callable
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from browser_manager import BrowserManager


class AutomationEngine:
    """自动化操作引擎"""

    def __init__(self, browser_manager: BrowserManager):
        """初始化自动化引擎"""
        self.browser_manager = browser_manager
        self.default_timeout = 10

    def execute_action(self, profile_name: str, action_func: Callable, *args, **kwargs):
        """执行自动化操作"""
        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                driver = self.browser_manager.get_automation_driver(profile_name)
                if not driver:
                    if attempt < max_retries - 1:
                        print(f"第{attempt + 1}次尝试获取WebDriver失败，{retry_delay}秒后重试...")
                        time.sleep(retry_delay)
                        continue
                    else:
                        raise RuntimeError(f"无法获取配置文件 '{profile_name}' 的WebDriver")

                return action_func(driver, *args, **kwargs)

            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"第{attempt + 1}次执行自动化操作失败: {e}，{retry_delay}秒后重试...")
                    time.sleep(retry_delay)
                    # 尝试清理可能的连接问题
                    try:
                        if 'driver' in locals():
                            driver.quit()
                    except:
                        pass
                else:
                    print(f"执行自动化操作失败: {e}")
                    raise

    def navigate_to_url(self, profile_name: str, url: str) -> bool:
        """导航到指定URL"""
        def action(driver):
            driver.get(url)
            return True

        return self.execute_action(profile_name, action)

    def wait_for_element(self, profile_name: str, locator: tuple, timeout: int = None) -> bool:
        """等待元素出现"""
        timeout = timeout or self.default_timeout

        def action(driver):
            WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located(locator)
            )
            return True

        return self.execute_action(profile_name, action)

    def click_element(self, profile_name: str, locator: tuple, timeout: int = None) -> bool:
        """点击元素"""
        timeout = timeout or self.default_timeout

        def action(driver):
            element = WebDriverWait(driver, timeout).until(
                EC.element_to_be_clickable(locator)
            )
            element.click()
            return True

        return self.execute_action(profile_name, action)

    def input_text(self, profile_name: str, locator: tuple, text: str,
                   clear_first: bool = True, timeout: int = None) -> bool:
        """输入文本"""
        timeout = timeout or self.default_timeout

        def action(driver):
            element = WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located(locator)
            )
            if clear_first:
                element.clear()
            element.send_keys(text)
            return True

        return self.execute_action(profile_name, action)

    def get_element_text(self, profile_name: str, locator: tuple, timeout: int = None) -> str:
        """获取元素文本"""
        timeout = timeout or self.default_timeout

        def action(driver):
            element = WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located(locator)
            )
            return element.text

        return self.execute_action(profile_name, action)

    def execute_script(self, profile_name: str, script: str, *args):
        """执行JavaScript脚本"""
        def action(driver):
            return driver.execute_script(script, *args)

        return self.execute_action(profile_name, action)

    def take_screenshot(self, profile_name: str, filename: str = None) -> str:
        """截图"""
        def action(driver):
            if filename is None:
                timestamp = int(time.time())
                filename_with_timestamp = f"screenshot_{profile_name}_{timestamp}.png"
            else:
                filename_with_timestamp = filename

            driver.save_screenshot(filename_with_timestamp)
            return filename_with_timestamp

        return self.execute_action(profile_name, action)

    def auto_login(self, profile_name: str, login_url: str, username: str,
                   password: str, username_locator: tuple, password_locator: tuple,
                   submit_locator: tuple, success_indicator: tuple = None) -> bool:
        """自动登录"""
        def action(driver):
            # 导航到登录页面
            driver.get(login_url)

            # 等待并输入用户名
            username_element = WebDriverWait(driver, self.default_timeout).until(
                EC.presence_of_element_located(username_locator)
            )
            username_element.clear()
            username_element.send_keys(username)

            # 输入密码
            password_element = driver.find_element(*password_locator)
            password_element.clear()
            password_element.send_keys(password)

            # 点击登录按钮
            submit_element = WebDriverWait(driver, self.default_timeout).until(
                EC.element_to_be_clickable(submit_locator)
            )
            submit_element.click()

            # 如果提供了成功指示器，等待登录成功
            if success_indicator:
                WebDriverWait(driver, self.default_timeout).until(
                    EC.presence_of_element_located(success_indicator)
                )
            else:
                # 等待URL变化作为登录成功的指示
                WebDriverWait(driver, self.default_timeout).until(
                    lambda d: d.current_url != login_url
                )

            return True

        return self.execute_action(profile_name, action)

    def scroll_to_element(self, profile_name: str, locator: tuple, timeout: int = None) -> bool:
        """滚动到元素"""
        timeout = timeout or self.default_timeout

        def action(driver):
            element = WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located(locator)
            )
            driver.execute_script("arguments[0].scrollIntoView();", element)
            return True

        return self.execute_action(profile_name, action)

    def hover_element(self, profile_name: str, locator: tuple, timeout: int = None) -> bool:
        """悬停在元素上"""
        timeout = timeout or self.default_timeout

        def action(driver):
            element = WebDriverWait(driver, timeout).until(
                EC.presence_of_element_located(locator)
            )
            ActionChains(driver).move_to_element(element).perform()
            return True

        return self.execute_action(profile_name, action)

    def wait_and_switch_to_window(self, profile_name: str, window_index: int = -1) -> bool:
        """切换到指定窗口"""
        def action(driver):
            # 等待新窗口出现
            WebDriverWait(driver, self.default_timeout).until(
                lambda d: len(d.window_handles) > abs(window_index)
            )

            # 切换到指定窗口
            driver.switch_to.window(driver.window_handles[window_index])
            return True

        return self.execute_action(profile_name, action)

    def create_custom_action(self, action_func: Callable) -> Callable:
        """创建自定义操作"""
        def wrapper(profile_name: str, *args, **kwargs):
            return self.execute_action(profile_name, action_func, *args, **kwargs)

        return wrapper

"""
Chrome日志优化器
专门用于减少SSL握手错误等网络日志
"""

def get_log_reduction_args():
    """获取用于减少日志的Chrome参数"""
    return [
        # 核心日志控制
        '--log-level=3',              # 只显示致命错误 (0=INFO, 1=WARNING, 2=ERROR, 3=FATAL)
        '--silent',                   # 静默模式
        '--disable-logging',          # 禁用日志记录
        '--disable-log-file',         # 禁用日志文件
        
        # 网络相关错误减少
        '--disable-background-networking',     # 禁用后台网络活动
        '--disable-domain-reliability',       # 禁用域名可靠性检查
        '--disable-component-update',         # 禁用组件更新
        '--disable-background-timer-throttling', # 禁用后台定时器限制
        
        # SSL/TLS相关优化
        '--ignore-ssl-errors',               # 忽略SSL错误
        '--ignore-certificate-errors',       # 忽略证书错误
        '--ignore-certificate-errors-spki-list',
        '--ignore-certificate-errors-ssl-errors',
        '--disable-web-security',           # 禁用Web安全检查
        
        # 减少其他网络错误
        '--disable-features=VizDisplayCompositor',
        '--disable-ipc-flooding-protection',
        '--disable-renderer-backgrounding',
        '--disable-backgrounding-occluded-windows',
        '--disable-client-side-phishing-detection',
        
        # 减少扩展和服务相关错误
        '--disable-extensions',             # 禁用扩展（可选）
        '--disable-plugins',               # 禁用插件
        '--disable-default-apps',          # 禁用默认应用
        '--disable-sync',                  # 禁用同步
        
        # GPU相关错误减少
        '--disable-gpu-sandbox',
        '--use-gl=swiftshader-webgl',
        '--enable-unsafe-swiftshader',
        
        # 其他错误减少
        '--disable-dev-shm-usage',
        '--no-sandbox',                    # 禁用沙盒（减少权限错误）
        '--disable-setuid-sandbox',
    ]

def get_stealth_safe_log_args():
    """获取与Stealth模式兼容的日志减少参数"""
    return [
        # 核心日志控制（保持Stealth兼容性）
        '--log-level=3',
        '--silent',
        '--disable-logging',
        
        # 网络错误减少（不影响指纹）
        '--disable-background-networking',
        '--disable-domain-reliability', 
        '--disable-component-update',
        
        # SSL错误减少
        '--ignore-ssl-errors',
        '--ignore-certificate-errors',
        
        # 减少非关键错误
        '--disable-background-timer-throttling',
        '--disable-ipc-flooding-protection',
        '--disable-client-side-phishing-detection',
    ]

def apply_log_optimization_to_options(options, stealth_mode=True):
    """将日志优化参数应用到Chrome选项"""
    if stealth_mode:
        log_args = get_stealth_safe_log_args()
    else:
        log_args = get_log_reduction_args()
    
    for arg in log_args:
        if arg not in [opt for opt in options.arguments]:
            options.add_argument(arg)
    
    return options

def show_optimization_info():
    """显示优化信息"""
    print("🔇 Chrome日志优化说明")
    print("=" * 40)
    print("🎯 目标: 减少SSL握手错误等网络日志")
    print("\n📋 主要优化参数:")
    print("  --log-level=3           # 只显示致命错误")
    print("  --silent                # 静默模式")
    print("  --disable-logging       # 禁用日志记录")
    print("  --ignore-ssl-errors     # 忽略SSL错误")
    print("  --disable-background-networking  # 禁用后台网络")
    
    print("\n✅ 预期效果:")
    print("  - 大幅减少SSL握手错误日志")
    print("  - 减少证书验证错误信息")
    print("  - 减少后台网络连接错误")
    print("  - 保持浏览器核心功能")
    print("  - 不影响Stealth指纹伪装")
    
    print("\n⚠️ 说明:")
    print("  - 完全消除所有错误可能影响调试")
    print("  - 少量关键错误仍会显示")
    print("  - 不影响网页正常访问和功能")

if __name__ == "__main__":
    show_optimization_info()

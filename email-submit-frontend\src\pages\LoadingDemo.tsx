/**
 * 加载动画演示页面
 * 用于展示登录注册加载动画效果
 */
import React, { useState } from 'react';
import { Button, Card, Space, Typography, Divider } from 'antd';
import { AuthLoading } from '../components/LoadingScreen';

const { Title, Text } = Typography;

const LoadingDemo: React.FC = () => {
  const [showLoginLoading, setShowLoginLoading] = useState(false);
  const [showRegisterLoading, setShowRegisterLoading] = useState(false);

  const handleShowLoginLoading = () => {
    setShowLoginLoading(true);
  };

  const handleShowRegisterLoading = () => {
    setShowRegisterLoading(true);
  };

  const handleLoginComplete = () => {
    setTimeout(() => {
      setShowLoginLoading(false);
    }, 1000);
  };

  const handleRegisterComplete = () => {
    setTimeout(() => {
      setShowRegisterLoading(false);
    }, 1000);
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Card
        style={{
          width: 500,
          maxWidth: '90vw',
          textAlign: 'center',
          boxShadow: '0 12px 48px rgba(0, 0, 0, 0.2)',
          borderRadius: 20,
          border: 'none'
        }}
        bodyStyle={{ padding: '40px' }}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
              加载动画演示
            </Title>
            <Text type="secondary">
              点击下方按钮体验漂亮的登录注册加载动画
            </Text>
          </div>

          <Divider />

          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <Button
              type="primary"
              size="large"
              onClick={handleShowLoginLoading}
              disabled={showLoginLoading || showRegisterLoading}
              style={{
                height: 48,
                borderRadius: 8,
                fontSize: 16,
                fontWeight: 500,
                background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                border: 'none',
                boxShadow: '0 4px 15px rgba(24, 144, 255, 0.3)',
                width: '100%'
              }}
            >
              演示登录加载动画
            </Button>

            <Button
              type="primary"
              size="large"
              onClick={handleShowRegisterLoading}
              disabled={showLoginLoading || showRegisterLoading}
              style={{
                height: 48,
                borderRadius: 8,
                fontSize: 16,
                fontWeight: 500,
                background: 'linear-gradient(135deg, #52c41a, #1890ff)',
                border: 'none',
                boxShadow: '0 4px 15px rgba(82, 196, 26, 0.3)',
                width: '100%'
              }}
            >
              演示注册加载动画
            </Button>
          </Space>

          <Divider />

          <div style={{ textAlign: 'left' }}>
            <Title level={4} style={{ color: '#666' }}>功能特点：</Title>
            <ul style={{ color: '#666', lineHeight: '1.8' }}>
              <li>🎨 漂亮的渐变背景和动画效果</li>
              <li>📊 实时进度条显示</li>
              <li>📋 分步骤状态指示器</li>
              <li>⏱️ 预计剩余时间显示</li>
              <li>🔒 安全传输提示</li>
              <li>❌ 支持取消操作</li>
              <li>📱 响应式设计，支持移动端</li>
              <li>🌙 支持深色模式</li>
              <li>♿ 支持无障碍访问</li>
            </ul>
          </div>

          <div style={{ marginTop: 20 }}>
            <Text style={{ color: '#999', fontSize: 12 }}>
              💡 提示：加载动画会自动在5秒后关闭，或者您可以点击"取消操作"按钮手动关闭
            </Text>
          </div>
        </Space>
      </Card>

      {/* 登录加载动画 */}
      <AuthLoading
        visible={showLoginLoading}
        type="login"
        onCancel={() => setShowLoginLoading(false)}
        onComplete={handleLoginComplete}
      />

      {/* 注册加载动画 */}
      <AuthLoading
        visible={showRegisterLoading}
        type="register"
        onCancel={() => setShowRegisterLoading(false)}
        onComplete={handleRegisterComplete}
      />
    </div>
  );
};

export default LoadingDemo;

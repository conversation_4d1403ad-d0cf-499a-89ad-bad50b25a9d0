#!/usr/bin/env python3
"""
通过API清除缓存
"""
import requests
from datetime import datetime

def clear_cache():
    """清除缓存"""
    print("🔧 清除生产服务器缓存")
    print("="*40)
    
    # 测试本地服务器
    print("\n1️⃣ 清除本地服务器缓存")
    try:
        response = requests.post('http://localhost:5001/api/admin/clear-cache', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ 本地服务器缓存清除成功")
            print(f"   响应: {data}")
        else:
            print(f"❌ 本地服务器缓存清除失败: {response.status_code}")
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"❌ 本地服务器连接失败: {e}")
    
    # 测试生产服务器
    print("\n2️⃣ 清除生产服务器缓存")
    try:
        response = requests.post('https://aug8.xyz/api/admin/clear-cache', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ 生产服务器缓存清除成功")
            print(f"   响应: {data}")
        else:
            print(f"❌ 生产服务器缓存清除失败: {response.status_code}")
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"❌ 生产服务器连接失败: {e}")

def test_after_clear():
    """清除缓存后测试"""
    print("\n3️⃣ 清除缓存后测试系统状态")
    print("-"*40)
    
    try:
        response = requests.get('https://aug8.xyz/api/system/status', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ 系统状态查询成功")
            print(f"   可用配置: {data.get('available_profiles')} ⭐")
            print(f"   队列长度: {data.get('queue_length')}")
            print(f"   处理状态: {data.get('is_processing')}")
            
            if data.get('available_profiles') == 1:
                print("🎉 配置数量已修复！")
            else:
                print("⚠️ 配置数量仍然不正确")
        else:
            print(f"❌ 系统状态查询失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 系统状态查询失败: {e}")

def main():
    """主函数"""
    print(f"🕒 操作时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 清除缓存
    clear_cache()
    
    # 等待一下
    import time
    time.sleep(2)
    
    # 测试结果
    test_after_clear()
    
    print(f"\n💡 说明:")
    print("1. 如果清除缓存成功，系统状态应该显示正确的配置数量")
    print("2. 如果仍然不正确，需要重启 production_server.py")
    print("3. 清除缓存API仅用于调试，生产环境中应该移除")

if __name__ == "__main__":
    main()

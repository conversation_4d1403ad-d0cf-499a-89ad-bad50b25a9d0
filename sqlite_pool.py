"""
SQLite连接池管理器
提供SQLite连接的池化管理，减少文件锁定竞争
"""
import sqlite3
import threading
import time
import queue
from contextlib import contextmanager


class SQLiteConnectionPool:
    """SQLite连接池"""
    
    def __init__(self, database_path, pool_size=5, timeout=30):
        self.database_path = database_path
        self.pool_size = pool_size
        self.timeout = timeout
        self._pool = queue.Queue(maxsize=pool_size)
        self._lock = threading.Lock()
        self._created_connections = 0
        
        # 预创建连接
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化连接池"""
        for _ in range(self.pool_size):
            try:
                conn = self._create_connection()
                self._pool.put(conn, block=False)
                self._created_connections += 1
            except Exception as e:
                print(f"⚠️ 初始化SQLite连接失败: {e}")
                break
        
        print(f"✅ SQLite连接池初始化完成: {self._created_connections}/{self.pool_size}")
    
    def _create_connection(self):
        """创建新的SQLite连接"""
        conn = sqlite3.connect(
            self.database_path,
            timeout=20.0,
            check_same_thread=False,  # 允许多线程使用
            isolation_level=None  # 自动提交模式
        )
        
        # 优化SQLite设置
        conn.execute('PRAGMA journal_mode=WAL')
        conn.execute('PRAGMA synchronous=NORMAL')
        conn.execute('PRAGMA cache_size=10000')
        conn.execute('PRAGMA temp_store=MEMORY')
        conn.execute('PRAGMA busy_timeout=5000')
        
        # 设置Row工厂
        conn.row_factory = sqlite3.Row
        
        return conn
    
    def get_connection(self, timeout=None):
        """从连接池获取连接"""
        if timeout is None:
            timeout = self.timeout
        
        try:
            # 尝试从池中获取连接
            conn = self._pool.get(timeout=timeout)
            
            # 测试连接有效性
            try:
                conn.execute('SELECT 1').fetchone()
                return conn
            except Exception:
                # 连接无效，创建新连接
                try:
                    conn.close()
                except:
                    pass
                return self._create_connection()
                
        except queue.Empty:
            # 池中没有可用连接，创建新的临时连接
            print(f"⚠️ SQLite连接池耗尽，创建临时连接")
            return self._create_connection()
    
    def return_connection(self, conn):
        """将连接返回到池中"""
        if conn is None:
            return
        
        try:
            # 检查连接是否仍然有效
            conn.execute('SELECT 1').fetchone()
            
            # 尝试将连接放回池中
            self._pool.put(conn, block=False)
        except queue.Full:
            # 池已满，关闭连接
            try:
                conn.close()
            except:
                pass
        except Exception:
            # 连接无效，关闭它
            try:
                conn.close()
            except:
                pass
    
    @contextmanager
    def get_connection_context(self, timeout=None):
        """上下文管理器，自动管理连接的获取和返回"""
        conn = None
        try:
            conn = self.get_connection(timeout)
            yield conn
        finally:
            if conn:
                self.return_connection(conn)
    
    def close_all(self):
        """关闭所有连接"""
        while not self._pool.empty():
            try:
                conn = self._pool.get(block=False)
                conn.close()
            except (queue.Empty, Exception):
                break


# 全局SQLite连接池实例
_sqlite_pools = {}
_pool_lock = threading.Lock()


def get_sqlite_pool(database_path, pool_size=5):
    """获取或创建SQLite连接池"""
    with _pool_lock:
        if database_path not in _sqlite_pools:
            _sqlite_pools[database_path] = SQLiteConnectionPool(
                database_path, 
                pool_size=pool_size
            )
        return _sqlite_pools[database_path]


@contextmanager
def get_sqlite_connection(database_path='tasks.db', timeout=10):
    """便捷的SQLite连接上下文管理器"""
    pool = get_sqlite_pool(database_path)
    with pool.get_connection_context(timeout=timeout) as conn:
        yield conn


# 兼容性函数，用于替换原有的sqlite3.connect调用
def connect_with_pool(database_path='tasks.db', timeout=10):
    """使用连接池的SQLite连接函数"""
    pool = get_sqlite_pool(database_path)
    return pool.get_connection(timeout=timeout)


class PooledConnection:
    """包装的连接类，支持with语句和自动返回连接池"""
    
    def __init__(self, pool, conn):
        self.pool = pool
        self.conn = conn
        self._closed = False
    
    def __enter__(self):
        return self.conn
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if not self._closed:
            self.pool.return_connection(self.conn)
            self._closed = True
    
    def __getattr__(self, name):
        return getattr(self.conn, name)
    
    def close(self):
        """重写close方法，将连接返回池中而不是真正关闭"""
        if not self._closed:
            self.pool.return_connection(self.conn)
            self._closed = True


def get_pooled_connection(database_path='tasks.db', timeout=10):
    """获取包装的连接，支持自动返回连接池"""
    pool = get_sqlite_pool(database_path)
    conn = pool.get_connection(timeout=timeout)
    return PooledConnection(pool, conn)


# 清理函数
def cleanup_all_pools():
    """清理所有连接池"""
    with _pool_lock:
        for pool in _sqlite_pools.values():
            pool.close_all()
        _sqlite_pools.clear()
    print("🧹 所有SQLite连接池已清理")


if __name__ == "__main__":
    # 测试连接池
    print("🧪 测试SQLite连接池...")
    
    # 测试基本功能
    with get_sqlite_connection('test.db') as conn:
        conn.execute('CREATE TABLE IF NOT EXISTS test (id INTEGER PRIMARY KEY, name TEXT)')
        conn.execute('INSERT INTO test (name) VALUES (?)', ('test_pool',))
        result = conn.execute('SELECT * FROM test').fetchall()
        print(f"测试结果: {len(result)} 条记录")
    
    # 测试并发
    import threading
    
    def test_concurrent():
        for i in range(10):
            with get_sqlite_connection('test.db') as conn:
                conn.execute('INSERT INTO test (name) VALUES (?)', (f'concurrent_{i}',))
    
    threads = []
    for _ in range(5):
        t = threading.Thread(target=test_concurrent)
        threads.append(t)
        t.start()
    
    for t in threads:
        t.join()
    
    print("✅ SQLite连接池测试完成")
    cleanup_all_pools()

"""
多账号指纹浏览器管理器
主程序入口
"""
import tkinter as tk
from tkinter import messagebox
import sys
import os
from gui_app import BrowserManagerGUI


def check_dependencies():
    """检查依赖项"""
    missing_deps = []

    # 检查基础依赖
    try:
        import selenium
        from selenium import webdriver
    except ImportError:
        missing_deps.append("selenium")

    # 检查Stealth依赖
    try:
        from selenium_stealth import stealth
        from fake_useragent import UserAgent
        stealth_available = True
    except ImportError:
        stealth_available = False
        missing_deps.extend(["selenium-stealth", "fake-useragent"])

    if missing_deps:
        deps_str = " ".join(missing_deps)
        messagebox.showwarning(
            "依赖缺失",
            f"缺少以下依赖项: {deps_str}\n\n"
            f"请运行以下命令安装:\n"
            f"pip install {deps_str}\n\n"
            f"或运行: pip install -r requirements.txt"
        )
        if not stealth_available:
            messagebox.showinfo(
                "Stealth功能",
                "⚠️ Stealth指纹伪装功能不可用\n\n"
                "程序将以普通模式运行。\n"
                "要启用高级指纹伪装，请安装:\n"
                "pip install selenium-stealth fake-useragent"
            )
        return len(missing_deps) == 0 or stealth_available

    return True


def main():
    """主函数"""
    # 检查依赖项
    if not check_dependencies():
        sys.exit(1)

    # 创建主窗口
    root = tk.Tk()

    # 设置窗口图标（如果有的话）
    try:
        # root.iconbitmap("icon.ico")  # 如果有图标文件
        pass
    except:
        pass

    # 创建应用程序
    try:
        app = BrowserManagerGUI(root)

        # 设置窗口关闭事件
        def on_closing():
            if messagebox.askokcancel("退出", "确定要退出吗？这将关闭所有浏览器实例。"):
                try:
                    # 关闭所有浏览器
                    app.browser_manager.close_all_browsers()
                except:
                    pass
                root.destroy()

        root.protocol("WM_DELETE_WINDOW", on_closing)

        # 启动GUI
        root.mainloop()

    except Exception as e:
        messagebox.showerror("启动错误", f"程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

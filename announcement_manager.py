#!/usr/bin/env python3
"""
本地公告管理工具
用于管理和编辑本地JSON公告文件
"""

import json
import os
import shutil
from datetime import datetime
from typing import Dict, List, Any

class LocalAnnouncementManager:
    """本地公告管理器"""
    
    def __init__(self):
        self.json_files = [
            "email-submit-frontend/public/announcements.json",
            "learn/email-submit-frontend/public/announcements.json"
        ]
    
    def load_announcements(self, file_path: str = None) -> Dict[str, Any]:
        """加载公告数据"""
        if file_path is None:
            file_path = self.json_files[0]
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载文件失败: {e}")
            return {}
    
    def save_announcements(self, data: Dict[str, Any], file_path: str = None):
        """保存公告数据"""
        if file_path is None:
            # 保存到所有文件
            for fp in self.json_files:
                self._save_to_file(data, fp)
        else:
            self._save_to_file(data, file_path)
    
    def _save_to_file(self, data: Dict[str, Any], file_path: str):
        """保存到指定文件"""
        try:
            # 备份原文件
            if os.path.exists(file_path):
                backup_path = f"{file_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(file_path, backup_path)
                print(f"📁 已备份原文件: {backup_path}")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 保存新文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 已保存: {file_path}")
            
        except Exception as e:
            print(f"❌ 保存失败 {file_path}: {e}")
    
    def add_announcement(self, title: str, content: str, announcement_type: str = "announcement"):
        """添加新公告"""
        data = self.load_announcements()
        
        # 生成新ID
        existing_ids = []
        for item in data.get('announcements', []) + data.get('instructions', []) + data.get('emergency_announcements', []):
            existing_ids.append(item.get('id', 0))
        
        new_id = max(existing_ids, default=0) + 1
        
        # 创建新公告
        new_item = {
            "id": new_id,
            "title": title,
            "content": content,
            "type": announcement_type,
            "is_active": True,
            "sort_order": new_id,
            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 添加到相应分类
        if announcement_type == "instruction":
            if 'instructions' not in data:
                data['instructions'] = []
            data['instructions'].append(new_item)
        else:
            if 'announcements' not in data:
                data['announcements'] = []
            data['announcements'].append(new_item)
        
        # 更新元数据
        if 'metadata' not in data:
            data['metadata'] = {}
        data['metadata']['last_updated'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        self.save_announcements(data)
        print(f"✅ 已添加{announcement_type}: {title}")
        return new_id
    
    def update_announcement(self, item_id: int, title: str = None, content: str = None, is_active: bool = None):
        """更新公告"""
        data = self.load_announcements()
        
        # 查找并更新
        updated = False
        for category in ['announcements', 'instructions', 'emergency_announcements']:
            if category in data:
                for item in data[category]:
                    if item.get('id') == item_id:
                        if title is not None:
                            item['title'] = title
                        if content is not None:
                            item['content'] = content
                        if is_active is not None:
                            item['is_active'] = is_active
                        item['updated_at'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        updated = True
                        break
            if updated:
                break
        
        if updated:
            data['metadata']['last_updated'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.save_announcements(data)
            print(f"✅ 已更新ID {item_id}")
        else:
            print(f"❌ 未找到ID {item_id}")
    
    def delete_announcement(self, item_id: int):
        """删除公告"""
        data = self.load_announcements()
        
        # 查找并删除
        deleted = False
        for category in ['announcements', 'instructions', 'emergency_announcements']:
            if category in data:
                original_length = len(data[category])
                data[category] = [item for item in data[category] if item.get('id') != item_id]
                if len(data[category]) < original_length:
                    deleted = True
                    break
        
        if deleted:
            data['metadata']['last_updated'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.save_announcements(data)
            print(f"✅ 已删除ID {item_id}")
        else:
            print(f"❌ 未找到ID {item_id}")
    
    def list_announcements(self):
        """列出所有公告"""
        data = self.load_announcements()
        
        print("\n📢 系统公告:")
        for item in data.get('announcements', []):
            status = "✅" if item.get('is_active') else "❌"
            print(f"  {status} ID:{item.get('id')} - {item.get('title')} ({item.get('type')})")
        
        print("\n📋 使用说明:")
        for item in data.get('instructions', []):
            status = "✅" if item.get('is_active') else "❌"
            print(f"  {status} ID:{item.get('id')} - {item.get('title')} ({item.get('type')})")
        
        print("\n🚨 紧急公告:")
        emergency_items = data.get('emergency_announcements', [])
        if emergency_items:
            for item in emergency_items:
                status = "✅" if item.get('is_active') else "❌"
                print(f"  {status} ID:{item.get('id')} - {item.get('title')} ({item.get('type')})")
        else:
            print("  (无紧急公告)")
    
    def add_emergency_announcement(self, title: str, content: str):
        """添加紧急公告"""
        data = self.load_announcements()
        
        if 'emergency_announcements' not in data:
            data['emergency_announcements'] = []
        
        # 生成新ID
        existing_ids = []
        for item in data.get('announcements', []) + data.get('instructions', []) + data.get('emergency_announcements', []):
            existing_ids.append(item.get('id', 0))
        
        new_id = max(existing_ids, default=900) + 1
        
        emergency_item = {
            "id": new_id,
            "title": title,
            "content": content,
            "type": "emergency",
            "is_active": True,
            "sort_order": 0,  # 紧急公告优先显示
            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        data['emergency_announcements'].append(emergency_item)
        data['metadata']['last_updated'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        self.save_announcements(data)
        print(f"🚨 已添加紧急公告: {title}")
        return new_id

def main():
    """主函数 - 交互式管理界面"""
    manager = LocalAnnouncementManager()
    
    print("🎯 本地公告管理工具")
    print("=" * 40)
    
    while True:
        print("\n📋 可用操作:")
        print("1. 查看所有公告")
        print("2. 添加系统公告")
        print("3. 添加使用说明")
        print("4. 添加紧急公告")
        print("5. 更新公告")
        print("6. 删除公告")
        print("7. 退出")
        
        choice = input("\n请选择操作 (1-7): ").strip()
        
        if choice == "1":
            manager.list_announcements()
        
        elif choice == "2":
            title = input("请输入公告标题: ").strip()
            content = input("请输入公告内容 (支持HTML): ").strip()
            if title and content:
                manager.add_announcement(title, content, "announcement")
            else:
                print("❌ 标题和内容不能为空")
        
        elif choice == "3":
            title = input("请输入说明标题: ").strip()
            content = input("请输入说明内容 (支持HTML): ").strip()
            if title and content:
                manager.add_announcement(title, content, "instruction")
            else:
                print("❌ 标题和内容不能为空")
        
        elif choice == "4":
            title = input("请输入紧急公告标题: ").strip()
            content = input("请输入紧急公告内容 (支持HTML): ").strip()
            if title and content:
                manager.add_emergency_announcement(title, content)
            else:
                print("❌ 标题和内容不能为空")
        
        elif choice == "5":
            try:
                item_id = int(input("请输入要更新的公告ID: ").strip())
                title = input("新标题 (留空保持不变): ").strip() or None
                content = input("新内容 (留空保持不变): ").strip() or None
                active_input = input("是否激活 (y/n/留空保持不变): ").strip().lower()
                is_active = None if not active_input else active_input == 'y'
                
                manager.update_announcement(item_id, title, content, is_active)
            except ValueError:
                print("❌ 请输入有效的ID数字")
        
        elif choice == "6":
            try:
                item_id = int(input("请输入要删除的公告ID: ").strip())
                confirm = input(f"确认删除ID {item_id}? (y/N): ").strip().lower()
                if confirm == 'y':
                    manager.delete_announcement(item_id)
                else:
                    print("❌ 已取消删除")
            except ValueError:
                print("❌ 请输入有效的ID数字")
        
        elif choice == "7":
            print("👋 再见！")
            break
        
        else:
            print("❌ 无效选择，请输入1-7")

if __name__ == "__main__":
    main()

/**
 * 公告和使用说明组件 - 读取本地txt文件
 */
import React, { useState, useEffect, useCallback } from 'react';
import { Card, Alert, Spin } from 'antd';
import { BellOutlined } from '@ant-design/icons';

const AnnouncementCard: React.FC = () => {
  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 从本地txt文件获取公告和使用说明内容
  const fetchContent = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // 直接从public目录读取txt文件
      const response = await fetch('/anounceandreadme.txt', {
        method: 'GET',
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
        },
        // 添加缓存控制，确保获取最新内容
        cache: 'no-cache'
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: 无法加载公告文件`);
      }

      const textContent = await response.text();

      if (!textContent.trim()) {
        throw new Error('公告文件内容为空');
      }

      setContent(textContent);
      setError(null);

      console.log('📢 成功加载公告和使用说明内容');

    } catch (err: any) {
      console.error('加载公告文件失败:', err);
      const errorMessage = err.message || '加载公告文件失败';
      setError(errorMessage);

      // 设置默认内容，确保用户能看到基本信息
      setContent(`🎉 欢迎使用风车AUG云激活系统

本系统提供自动化的AUG激活服务，请按照以下说明正确操作。

📋 使用说明：
1. 请确保邮箱格式正确
2. 每个邮箱只能提交一次
3. 处理时间约1-3分钟
4. 请耐心等待处理完成
5. 激活成功后请到邮箱查收结果

⚠️ 注意事项：
• 请勿重复提交相同邮箱
• 系统会自动检测并拒绝重复请求
• 如需帮助请联系管理员
• 处理过程中请勿关闭页面
• 建议使用Chrome或Edge浏览器

如果您看到此消息，说明公告文件加载失败，请刷新页面重试。`);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    // 只在组件首次加载时获取一次内容
    fetchContent();
  }, [fetchContent]);

  if (loading) {
    return (
      <Card title="公告和使用说明" style={{ marginTop: 16 }}>
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>加载中...</div>
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={
        <span>
          <BellOutlined style={{ marginRight: 8 }} />
          公告和使用说明
        </span>
      }
      style={{ marginTop: 16 }}
    >
      {error && (
        <Alert
          message="文件加载失败，显示默认内容"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <div
        style={{
          lineHeight: '1.6',
          fontSize: '14px',
          padding: '8px 0'
        }}
        dangerouslySetInnerHTML={{
          __html: content.replace(
            /<p>/g, '<p style="margin: 8px 0;">'
          ).replace(
            /<h([1-6])>/g, '<h$1 style="margin: 12px 0 8px 0;">'
          ).replace(
            /<iframe/g, '<iframe style="width: 100%; height: 315px; margin: 12px 0;"'
          )
        }}
      />
    </Card>
  );
};

export default AnnouncementCard;

/**
 * 主应用组件 - 支持用户认证和路由
 */
import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Layout, Row, Col, Typography, Space, message } from 'antd';
import EmailSubmitForm from './components/EmailSubmitForm';
import TaskStatus from './components/TaskStatus';
import ConnectionStatus from './components/ConnectionStatus';
import AnnouncementCard from './components/AnnouncementCard';
import UserInfo from './components/UserInfo';
import WindLogo from './components/WindLogo';
import LoadingScreen from './components/LoadingScreen';

import ProtectedRoute from './components/ProtectedRoute';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import LoadingDemo from './pages/LoadingDemo';
import { useSystemPolling } from './stores/systemStore';
import { useAppLoading } from './hooks/useAppLoading';
import 'antd/dist/reset.css';

const { Header, Content, Footer } = Layout;
const { Title, Text } = Typography;

// 主仪表板组件
const Dashboard: React.FC = () => {
  // 启动系统状态轮询
  useSystemPolling();

  return (
    <Layout style={{ minHeight: '100vh', background: '#f5f5f5' }}>
      {/* 页面头部 */}
      <Header style={{
        background: '#fff',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        padding: '0 24px'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          height: '100%'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <WindLogo size={32} color="#1890ff" />
            <Title level={3} style={{ margin: '0 0 0 12px', color: '#1890ff' }}>
              风车AUG云激活
              <span style={{
                color: '#faad14',
                fontSize: '14px',
                fontWeight: 'bold',
                marginLeft: '8px',
                padding: '2px 6px',
                background: 'rgba(250, 173, 20, 0.1)',
                borderRadius: '4px',
                border: '1px solid rgba(250, 173, 20, 0.3)'
              }}>
                7X24h
              </span>
            </Title>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <ConnectionStatus />
            <UserInfo />
          </div>
        </div>
      </Header>

      {/* 主要内容区域 */}
      <Content style={{ padding: '24px' }}>
        <div style={{ width: '100%', margin: '0 auto' }}>
          {/* 主要功能区域 */}
          <Row gutter={[24, 24]} style={{ width: '100%' }}>
            {/* 左侧：表单和公告 */}
            <Col xs={24} xl={10} xxl={8}>
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <EmailSubmitForm />
                <AnnouncementCard />
              </Space>
            </Col>

            {/* 右侧：任务状态 */}
            <Col xs={24} xl={14} xxl={16}>
              <TaskStatus />
            </Col>
          </Row>
        </div>
      </Content>

      {/* 页面底部 */}
      <Footer style={{ textAlign: 'center', background: '#fff' }}>
        <Text type="secondary">
          风车AUG云激活 ©2025
        </Text>
      </Footer>
    </Layout>
  );
};

// 主App组件
function App() {
  const [appInitialized, setAppInitialized] = useState(false);
  const { isLoading, steps, startLoading } = useAppLoading();

  // 应用初始化
  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log('🚀 开始应用初始化...');

        // 启动加载流程
        await startLoading();

        setAppInitialized(true);
        console.log('✅ 应用初始化完成');
      } catch (error) {
        console.error('❌ 应用初始化失败:', error);
        message.error('应用初始化失败，请刷新页面重试');
        // 即使初始化失败，也要设置为已初始化，让用户能看到界面
        setTimeout(() => {
          setAppInitialized(true);
        }, 3000);
      }
    };

    // 延迟执行初始化，确保DOM完全加载
    const timer = setTimeout(initializeApp, 100);

    return () => clearTimeout(timer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // 只在组件挂载时执行一次

  // 处理加载完成
  const handleLoadingComplete = () => {
    setAppInitialized(true);
  };

  // 处理加载错误
  const handleLoadingError = (errorMessage: string) => {
    message.error(`初始化失败: ${errorMessage}`);
    // 显示错误后仍然进入应用
    setTimeout(() => {
      setAppInitialized(true);
    }, 2000);
  };

  return (
    <>
      {/* 加载屏幕 */}
      <LoadingScreen
        visible={!appInitialized && isLoading}
        steps={steps}
        onComplete={handleLoadingComplete}
        onError={handleLoadingError}
      />

      {/* 主应用内容 */}
      {appInitialized && (
        <Router>
          <Routes>
            {/* 公开路由 */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />
            <Route path="/demo" element={<LoadingDemo />} />

            {/* 受保护的路由 */}
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              }
            />

            {/* 默认重定向 */}
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </Router>
      )}
    </>
  );
}

export default App;

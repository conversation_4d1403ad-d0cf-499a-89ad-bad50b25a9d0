#!/usr/bin/env python3
"""
测试状态映射修复效果
"""

import requests
import json
from datetime import datetime

def test_queue_api_with_auth():
    """使用真实的认证token测试队列API"""
    print("🔍 测试队列API状态映射修复效果")
    
    # 这里需要一个真实的token，但我们可以看看响应格式
    try:
        response = requests.get('https://aug8.xyz/api/queue/list', 
                              headers={'Authorization': 'Bearer fake_token'}, 
                              timeout=15)
        
        print(f"   📊 状态码: {response.status_code}")
        
        if response.status_code == 401:
            print("   ⚠️ 认证失败（预期的）")
            data = response.json()
            print(f"   📄 响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
        elif response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查状态映射
            if 'queue' in data and isinstance(data['queue'], list):
                for task in data['queue']:
                    if task.get('id') == 2:
                        print(f"   🎯 任务ID 2的状态: {task.get('status')}")
                        if task.get('status') == 'success':
                            print("   ✅ 状态映射修复成功！")
                        else:
                            print(f"   ❌ 状态仍然是: {task.get('status')}")
        else:
            print(f"   ❌ 其他错误: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")

def check_database_vs_api():
    """对比数据库状态和API返回状态"""
    print("\n📊 数据库 vs API 状态对比:")
    print("   数据库状态: completed")
    print("   期望API状态: success")
    print("   修复前API状态: completed (导致前端显示pending)")
    print("   修复后API状态: success (前端应该显示100%)")

def main():
    """主函数"""
    print("🔧 状态映射修复验证")
    print("="*60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    check_database_vs_api()
    test_queue_api_with_auth()
    
    print("\n" + "="*60)
    print("🎯 修复说明:")
    print("已在 production_server.py 的 handle_get_queue_list 方法中添加状态映射:")
    print("- waiting → pending")
    print("- processing → processing") 
    print("- completed → success  ✅ 这是关键修复")
    print("- failed → failed")
    print()
    print("现在前端应该能正确显示任务完成状态（100%进度）")
    print("建议用户刷新前端页面测试效果")
    print("="*60)

if __name__ == "__main__":
    main()

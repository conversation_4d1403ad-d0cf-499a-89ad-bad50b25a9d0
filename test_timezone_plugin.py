#!/usr/bin/env python3
"""
测试timezone插件是否正确加载的脚本
"""
import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def test_timezone_plugin():
    """测试timezone插件是否正确加载"""
    
    print("🧪 开始测试timezone插件...")
    
    # 检查Chrome路径
    chrome_path = os.path.join(os.getcwd(), "134", "chrome-win64", "chrome.exe")
    if not os.path.exists(chrome_path):
        print(f"❌ Chrome路径不存在: {chrome_path}")
        return False
    
    # 检查插件路径
    timezone_plugin_path = os.path.join(os.getcwd(), "timezone")
    finger_plugin_path = os.path.join(os.getcwd(), "finger")
    
    available_extensions = []
    if os.path.exists(timezone_plugin_path):
        available_extensions.append(timezone_plugin_path)
        print(f"✅ 发现timezone插件: {timezone_plugin_path}")
    
    if os.path.exists(finger_plugin_path):
        available_extensions.append(finger_plugin_path)
        print(f"✅ 发现finger插件: {finger_plugin_path}")
    
    if not available_extensions:
        print("❌ 未找到任何插件")
        return False
    
    # 创建临时测试配置目录
    test_profile_path = os.path.join(os.getcwd(), "test_profile")
    os.makedirs(test_profile_path, exist_ok=True)
    
    try:
        # 配置Chrome选项
        options = Options()
        options.binary_location = chrome_path
        options.add_argument(f"--user-data-dir={test_profile_path}")
        options.add_argument("--remote-debugging-port=9222")
        options.add_argument("--no-first-run")
        options.add_argument("--no-default-browser-check")
        options.add_argument("--disable-background-timer-throttling")
        options.add_argument("--disable-renderer-backgrounding")
        options.add_argument("--disable-backgrounding-occluded-windows")
        
        # 加载插件
        if available_extensions:
            extension_paths = ",".join(available_extensions)
            options.add_argument(f"--load-extension={extension_paths}")
            options.add_argument("--enable-extensions")
            print(f"🔧 加载插件: {extension_paths}")
        
        # 启动浏览器
        print("🚀 启动Chrome浏览器...")
        driver = webdriver.Chrome(options=options)
        
        # 等待浏览器启动
        time.sleep(3)
        
        # 访问chrome://extensions页面检查插件
        print("🔍 检查插件状态...")
        driver.get("chrome://extensions/")
        time.sleep(2)
        
        # 检查页面内容
        page_source = driver.page_source.lower()
        
        timezone_loaded = "timezone" in page_source or "time shift" in page_source
        finger_loaded = "canvas" in page_source or "fingerprint" in page_source
        
        print("\n📊 插件加载状态:")
        if timezone_loaded:
            print("✅ Timezone插件已加载")
        else:
            print("❌ Timezone插件未加载")
            
        if finger_loaded:
            print("✅ Finger插件已加载")
        else:
            print("❌ Finger插件未加载")
        
        # 测试时区功能
        print("\n🕐 测试时区功能...")
        driver.get("https://www.timeanddate.com/worldclock/")
        time.sleep(3)
        
        # 获取当前时区信息
        try:
            # 执行JavaScript获取时区信息
            timezone_info = driver.execute_script("""
                return {
                    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                    offset: new Date().getTimezoneOffset(),
                    date: new Date().toString()
                };
            """)
            
            print(f"🌍 当前时区: {timezone_info['timezone']}")
            print(f"⏰ 时区偏移: {timezone_info['offset']} 分钟")
            print(f"📅 当前时间: {timezone_info['date']}")
            
        except Exception as e:
            print(f"⚠️ 获取时区信息失败: {e}")
        
        # 保持浏览器打开一段时间供手动检查
        print("\n✨ 测试完成！浏览器将保持打开状态10秒供您检查...")
        print("💡 您可以:")
        print("   1. 点击浏览器工具栏中的timezone插件图标")
        print("   2. 访问 chrome://extensions/ 查看插件详情")
        print("   3. 测试时区修改功能")
        
        time.sleep(10)
        
        return timezone_loaded or finger_loaded
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
        
    finally:
        try:
            driver.quit()
            print("🔚 浏览器已关闭")
        except:
            pass
        
        # 清理测试配置目录
        try:
            import shutil
            shutil.rmtree(test_profile_path, ignore_errors=True)
            print("🧹 清理测试配置完成")
        except:
            pass

if __name__ == "__main__":
    success = test_timezone_plugin()
    if success:
        print("\n🎉 插件测试成功！")
    else:
        print("\n❌ 插件测试失败！")

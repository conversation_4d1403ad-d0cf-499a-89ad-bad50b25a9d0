{"timestamp": "2025-07-02 04:19:55", "basic_connectivity": {"https://www.google.com": {"status": "success", "status_code": 200, "response_time": 229.93, "headers": {"Date": "Tue, 01 Jul 2025 20:19:36 GMT", "Expires": "-1", "Cache-Control": "private, max-age=0", "Content-Type": "text/html; charset=ISO-8859-1", "Content-Security-Policy-Report-Only": "object-src 'none';base-uri 'self';script-src 'nonce-Trv5ZXZsEKkcYaLvQdezMg' 'strict-dynamic' 'report-sample' 'unsafe-eval' 'unsafe-inline' https: http:;report-uri https://csp.withgoogle.com/csp/gws/other-hp", "Accept-CH": "Sec-CH-Prefers-Color-Scheme", "P3P": "CP=\"This is not a P3P policy! See g.co/p3phelp for more info.\"", "Content-Encoding": "gzip", "Server": "gws", "X-XSS-Protection": "0", "X-Frame-Options": "SAMEORIGIN", "Set-Cookie": "AEC=AVh_V2hSMTaScSuCQN3TzXQR3ZsiCaT5etEQRGxNtMS9Rz6i4S27imcZbQ; expires=Sun, 28-Dec-2025 20:19:36 GMT; path=/; domain=.google.com; Secure; HttpOnly; SameSite=lax, NID=525=MOwvT_T76kuGuvZgDK2sy1KP71EwN3TrM5LBpoXzatFL5a7Qmz5iuQsZJBhTokbV0Mg9ydvxDFyCYLSQ6M_Sy8TIKp3TEGQHmvp_GWXnXbJE87HFkJnMOEtV7MS3gQQudCGdNpSYt2qn9TQCIqGGNlBlQNOJ4UyOp2Bjs4UPXhUEc-904rWlGpaL-1ebRiCkZ0mG5pcVSfoOCEOwiw; expires=Wed, 31-Dec-2025 20:19:36 GMT; path=/; domain=.google.com; HttpOnly", "Alt-Svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000", "Transfer-Encoding": "chunked"}}, "https://www.cloudflare.com": {"status": "success", "status_code": 200, "response_time": 710.72, "headers": {"Date": "Tue, 01 Jul 2025 20:19:36 GMT", "Content-Type": "text/html; charset=utf-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Set-Cookie": "_cfms_willow=enable; Max-Age=1209600; path=/; domain=.www.cloudflare.com; SameSite=Strict; Secure, _ga=GA1.1.ad9662df-bf35-42dd-a574-d9214f70b659; Domain=.cloudflare.com; Expires=Sat Aug 01 2026 20:19:36 GMT+0000 (Coordinated Universal Time); Path=/, cfz_google-analytics_v4={\"nzcr_ga4\":{\"v\":\"ad9662df-bf35-42dd-a574-d9214f70b659\",\"e\":1785615576537}}; Domain=.cloudflare.com; Expires=Sat Aug 01 2026 20:19:36 GMT+0000 (Coordinated Universal Time); HttpOnly; SameSite=Lax; Path=/; Secure, __cf_bm=HRxFJFcB4Qd9NLiWXVHxCWnbnZKz79sOwBrIg7LV_xk-1751401176-1.0.1.1-30Q4yQAUCCKmXeEANplZHGOK5QELlN04rEcIwHpHkJCbnZ46_Zg0zRgHum_b8Q6ox31HoR2d32pfpa3Maf57.pTJamv55qp_UT6NHjjNrE4P6ZeMGPb9Cm5nsuZtAE8c; path=/; expires=Tue, 01-Jul-25 20:49:36 GMT; domain=.www.cloudflare.com; HttpOnly; Secure; SameSite=None", "Strict-Transport-Security": "max-age=31536000; includeSubDomains", "Permissions-Policy": "geolocation=(), camera=(), microphone=()", "Referrer-Policy": "strict-origin-when-cross-origin", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "SAMEORIGIN", "x-RM": "GW", "X-XSS-Protection": "1; mode=block", "Report-To": "{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=sMcZXxS%2B5e8owzq2bjybJoct1%2FOB9NXe8WDVhrZLnY0tUNYXzl11cJTy5XfLDHrMCzfT0Dpon73er9yDQOVJxBcHMZKKyp8d6kCdUEFhJhg2KtBdngA%2Fmr9AMau13EVKE4awEA%3D%3D\"}],\"group\":\"cf-nel\",\"max_age\":604800}", "NEL": "{\"success_fraction\":0,\"report_to\":\"cf-nel\",\"max_age\":604800}", "Vary": "Accept-Encoding", "Server": "cloudflare", "CF-RAY": "958899693f4a46b5-SIN", "Content-Encoding": "gzip", "alt-svc": "h3=\":443\"; ma=86400"}}, "https://httpbin.org/get": {"status": "success", "status_code": 200, "response_time": 1279.35, "headers": {"Date": "Tue, 01 Jul 2025 20:19:38 GMT", "Content-Type": "application/json", "Content-Length": "308", "Connection": "keep-alive", "Server": "gunicorn/19.9.0", "Access-Control-Allow-Origin": "*", "Access-Control-Allow-Credentials": "true"}}, "https://api.github.com": {"status": "success", "status_code": 200, "response_time": 410.13, "headers": {"Date": "Tue, 01 Jul 2025 20:19:38 GMT", "Content-Type": "application/json; charset=utf-8", "Cache-Control": "public, max-age=60, s-maxage=60", "Vary": "Accept,Accept-Encoding, Accept, X-Requested-With", "ETag": "W/\"4f825cc84e1c733059d46e76e6df9db557ae5254f9625dfe8e1b09499c449438\"", "X-GitHub-Media-Type": "github.v3; format=json", "x-github-api-version-selected": "2022-11-28", "Access-Control-Expose-Headers": "ETag, Link, Location, Retry-After, X-GitHub-OTP, X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Used, X-RateLimit-Resource, X-RateLimit-Reset, X-OAuth-Scopes, X-Accepted-OA<PERSON>-Scopes, X-Poll-Interval, X-GitHub-Media-Type, X-GitHub-SSO, X-GitHub-Request-Id, Deprecation, Sunset", "Access-Control-Allow-Origin": "*", "Strict-Transport-Security": "max-age=31536000; includeSubdomains; preload", "X-Frame-Options": "deny", "X-Content-Type-Options": "nosniff", "X-XSS-Protection": "0", "Referrer-Policy": "origin-when-cross-origin, strict-origin-when-cross-origin", "Content-Security-Policy": "default-src 'none'", "Content-Encoding": "gzip", "Server": "github.com", "Accept-Ranges": "bytes", "X-RateLimit-Limit": "60", "X-RateLimit-Remaining": "50", "X-RateLimit-Reset": "1751404609", "X-RateLimit-Resource": "core", "X-RateLimit-Used": "10", "Content-Length": "510", "X-GitHub-Request-Id": "CAB4:15F9E8:605C9:691A2:686442DA"}}}, "dns_resolution": {"google.com": {"status": "success", "ip_addresses": ["***************"], "resolution_time": 128.3}, "cloudflare.com": {"status": "success", "ip_addresses": ["**************", "**************"], "resolution_time": 52.35}, "github.com": {"status": "success", "ip_addresses": ["**************"], "resolution_time": 58.8}, "auth0.com": {"status": "success", "ip_addresses": ["**************", "**************"], "resolution_time": 295.37}, "googletagmanager.com": {"status": "success", "ip_addresses": ["**************"], "resolution_time": 54.84}}, "port_connectivity": {"google.com:80": {"status": "failed", "error": "Connection failed: 10035"}, "google.com:443": {"status": "failed", "error": "Connection failed: 10035"}, "cloudflare.com:443": {"status": "failed", "error": "Connection failed: 10035"}, "auth0.com:443": {"status": "success", "connection_time": 247.09}, "localhost:5001": {"status": "success", "connection_time": 0.82}}, "ssl_certificates": {"https://www.google.com": {"status": "success", "ssl_valid": true, "status_code": 200}, "https://auth0.com": {"status": "success", "ssl_valid": true, "status_code": 200}, "https://challenges.cloudflare.com": {"status": "success", "ssl_valid": true, "status_code": 200}}, "proxy_settings": {}, "summary": {"total_tests": 17, "passed_tests": 14, "failed_tests": 3, "issues_found": ["port_connectivity: google.com:80", "port_connectivity: google.com:443", "port_connectivity: cloudflare.com:443"]}}
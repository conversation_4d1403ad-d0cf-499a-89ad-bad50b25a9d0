/**
 * 初始化管理器
 * 协调各个组件的初始化顺序，避免并发请求冲突
 */

interface InitTask {
  name: string;
  priority: number; // 数字越小优先级越高
  fn: () => Promise<void>;
  retries?: number;
  delay?: number;
}

class InitializationManager {
  private tasks: InitTask[] = [];
  private isInitializing = false;
  private initialized = false;

  /**
   * 添加初始化任务
   */
  addTask(task: InitTask) {
    this.tasks.push(task);
    // 按优先级排序
    this.tasks.sort((a, b) => a.priority - b.priority);
  }

  /**
   * 执行所有初始化任务
   */
  async initialize(): Promise<void> {
    if (this.isInitializing || this.initialized) {
      return;
    }

    this.isInitializing = true;
    console.log('🚀 开始执行初始化任务...');

    try {
      for (const task of this.tasks) {
        await this.executeTask(task);
      }
      
      this.initialized = true;
      console.log('✅ 所有初始化任务完成');
    } catch (error) {
      console.error('❌ 初始化过程中出现错误:', error);
      throw error;
    } finally {
      this.isInitializing = false;
    }
  }

  /**
   * 执行单个任务
   */
  private async executeTask(task: InitTask): Promise<void> {
    const maxRetries = task.retries || 3;
    const delay = task.delay || 0;

    console.log(`📋 执行任务: ${task.name} (优先级: ${task.priority})`);

    // 任务前延迟
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        await task.fn();
        console.log(`✅ 任务完成: ${task.name}`);
        return;
      } catch (error) {
        console.error(`❌ 任务失败: ${task.name} (尝试 ${attempt + 1}/${maxRetries + 1})`, error);
        
        if (attempt < maxRetries) {
          const retryDelay = 1000 * (attempt + 1); // 递增延迟
          console.log(`⏳ ${retryDelay}ms后重试...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        } else {
          console.error(`💥 任务最终失败: ${task.name}`);
          // 不抛出错误，继续执行其他任务
        }
      }
    }
  }

  /**
   * 重置初始化状态
   */
  reset() {
    this.isInitializing = false;
    this.initialized = false;
    this.tasks = [];
  }

  /**
   * 检查是否已初始化
   */
  isInitialized(): boolean {
    return this.initialized;
  }
}

// 创建全局实例
export const initManager = new InitializationManager();

/**
 * 预定义的初始化任务
 */
export const createInitTasks = () => {
  return {
    // 配置初始化 - 最高优先级
    config: {
      name: '配置初始化',
      priority: 1,
      fn: async () => {
        const { initConfig } = await import('../config');
        await initConfig();
      }
    },

    // 用户状态验证 - 第二优先级
    userAuth: {
      name: '用户认证验证',
      priority: 2,
      delay: 100, // 等待配置完成
      fn: async () => {
        const { useUserStore } = await import('../stores/userStore');
        const userStore = useUserStore.getState();
        
        if (userStore.isLoggedIn && userStore.token) {
          try {
            await userStore.refreshUserInfo();
          } catch (error) {
            console.warn('用户认证验证失败，自动登出');
            userStore.logout();
          }
        }
      }
    },

    // 系统状态获取 - 第三优先级
    systemStatus: {
      name: '系统状态获取',
      priority: 3,
      delay: 200,
      retries: 2,
      fn: async () => {
        const { useSystemStore } = await import('../stores/systemStore');
        const systemStore = useSystemStore.getState();
        await systemStore.fetchSystemStatus();
      }
    },

    // 任务队列获取 - 第四优先级
    taskQueue: {
      name: '任务队列获取',
      priority: 4,
      delay: 300,
      retries: 2,
      fn: async () => {
        const { useTaskStore } = await import('../stores/taskStore');
        const taskStore = useTaskStore.getState();
        await taskStore.refreshQueue();
      }
    }
  };
};

/**
 * 执行完整的应用初始化
 */
export const initializeApp = async (): Promise<void> => {
  try {
    // 重置初始化管理器
    initManager.reset();

    // 添加所有初始化任务
    const tasks = createInitTasks();
    Object.values(tasks).forEach(task => {
      initManager.addTask(task);
    });

    // 执行初始化
    await initManager.initialize();
    
  } catch (error) {
    console.error('应用初始化失败:', error);
    // 不抛出错误，让应用继续运行
  }
};

/**
 * 页面刷新时的快速初始化
 * 只执行关键任务，减少加载时间
 */
export const quickInitialize = async (): Promise<void> => {
  try {
    console.log('🔄 执行快速初始化...');
    
    // 重置初始化管理器
    initManager.reset();

    // 只添加关键任务
    const tasks = createInitTasks();
    
    // 配置和用户认证是必需的
    initManager.addTask(tasks.config);
    initManager.addTask(tasks.userAuth);
    
    // 执行初始化
    await initManager.initialize();
    
    console.log('✅ 快速初始化完成');
  } catch (error) {
    console.error('快速初始化失败:', error);
  }
};

/**
 * 检查网络连接并重新初始化
 */
export const reconnectAndInit = async (): Promise<void> => {
  try {
    console.log('🔄 检查连接并重新初始化...');
    
    // 测试连接
    const { emailAPI } = await import('../services/api');
    const connected = await emailAPI.testConnection();
    
    if (connected) {
      console.log('✅ 连接正常，执行完整初始化');
      await initializeApp();
    } else {
      console.warn('⚠️ 连接失败，只执行本地初始化');
      await quickInitialize();
    }
  } catch (error) {
    console.error('重连初始化失败:', error);
  }
};

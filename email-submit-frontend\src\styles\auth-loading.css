/**
 * 登录注册加载动画样式
 * 提供漂亮的CSS动画效果
 */

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 8px 32px rgba(24, 144, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12px 48px rgba(24, 144, 255, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 8px 32px rgba(24, 144, 255, 0.3);
  }
}

/* 旋转动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 外圈旋转动画 */
@keyframes rotate {
  from {
    transform: translateX(-50%) rotate(0deg);
  }
  to {
    transform: translateX(-50%) rotate(360deg);
  }
}

/* 渐入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 滑入动画 */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 弹跳动画 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* 呼吸灯效果 */
@keyframes breathe {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}

/* 成功动画 */
@keyframes successPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

/* 波纹效果 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* 闪烁效果 */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* 进度条动画 */
@keyframes progressGlow {
  0% {
    box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(24, 144, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(24, 144, 255, 0.5);
  }
}

/* 加载动画类 */
.auth-loading-container {
  animation: fadeIn 0.3s ease-out;
}

.auth-loading-icon {
  animation: pulse 2s infinite, spin 1s linear infinite;
  position: relative;
  overflow: hidden;
}

.auth-loading-icon.completed {
  animation: successPulse 0.6s ease-out;
}

.auth-loading-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

.auth-loading-ring {
  animation: rotate 3s linear infinite;
}

.auth-loading-step {
  animation: slideInLeft 0.4s ease-out;
  position: relative;
}

.auth-loading-step.active {
  animation: bounce 0.6s ease-out;
}

.auth-loading-step.completed {
  animation: successPulse 0.4s ease-out;
}

.auth-loading-step::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 4px;
  height: 0;
  background: linear-gradient(to bottom, #1890ff, #52c41a);
  border-radius: 2px;
  transform: translateY(-50%);
  transition: height 0.3s ease;
}

.auth-loading-step.active::before,
.auth-loading-step.completed::before {
  height: 100%;
}

.auth-loading-progress {
  animation: progressGlow 2s ease-in-out infinite;
  position: relative;
  overflow: hidden;
}

.auth-loading-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auth-loading-container .ant-card {
    width: 95vw !important;
    margin: 0 auto;
  }

  .auth-loading-icon {
    width: 60px !important;
    height: 60px !important;
  }

  .auth-loading-icon .anticon {
    font-size: 24px !important;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .auth-loading-container {
    background-color: rgba(0, 0, 0, 0.8) !important;
  }

  .auth-loading-container .ant-card {
    background: rgba(20, 20, 20, 0.95) !important;
    color: #fff !important;
  }

  .auth-loading-container .ant-typography {
    color: #fff !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .auth-loading-container {
    background-color: rgba(0, 0, 0, 0.9) !important;
  }

  .auth-loading-icon {
    border: 2px solid #fff !important;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .auth-loading-icon,
  .auth-loading-ring,
  .auth-loading-step,
  .auth-loading-progress {
    animation: none !important;
  }

  .auth-loading-icon {
    transform: none !important;
  }
}

/* 自定义滚动条 */
.auth-loading-container::-webkit-scrollbar {
  width: 6px;
}

.auth-loading-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.auth-loading-container::-webkit-scrollbar-thumb {
  background: rgba(24, 144, 255, 0.5);
  border-radius: 3px;
}

.auth-loading-container::-webkit-scrollbar-thumb:hover {
  background: rgba(24, 144, 255, 0.8);
}

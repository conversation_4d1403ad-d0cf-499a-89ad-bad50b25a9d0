#!/usr/bin/env python3
"""
混合架构状态检查工具
检查生产服务器和管理界面的运行状态
"""
import os
import sys
import time
import socket
import requests
import subprocess
from datetime import datetime

def check_port_available(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('127.0.0.1', port))
            return True
    except OSError:
        return False

def check_api_endpoint(port, endpoint='/api/test'):
    """检查API端点是否响应"""
    try:
        response = requests.get(f'http://localhost:{port}{endpoint}', timeout=3)
        if response.status_code == 200:
            data = response.json()
            return {
                'status': 'ok',
                'success': data.get('success', False),
                'message': data.get('message', 'N/A'),
                'response_time': response.elapsed.total_seconds()
            }
        else:
            return {
                'status': 'error',
                'code': response.status_code,
                'response_time': response.elapsed.total_seconds()
            }
    except Exception as e:
        return {
            'status': 'failed',
            'error': str(e)
        }

def get_system_info():
    """获取系统信息"""
    try:
        # 检查任务数据库
        tasks_db_exists = os.path.exists('tasks.db')
        
        # 检查配置文件
        config_exists = os.path.exists('config.json')
        
        # 检查关键模块文件
        key_files = [
            'autoback.py',
            'production_server.py', 
            'config_manager.py',
            'browser_manager.py',
            'auth_manager.py'
        ]
        
        missing_files = [f for f in key_files if not os.path.exists(f)]
        
        # 检查浏览器配置目录
        profiles_dir = 'browser_profiles'
        profiles_count = 0
        if os.path.exists(profiles_dir):
            profiles = [d for d in os.listdir(profiles_dir) 
                       if os.path.isdir(os.path.join(profiles_dir, d)) and d != '__pycache__']
            profiles_count = len(profiles)
        
        return {
            'tasks_db': tasks_db_exists,
            'config_file': config_exists,
            'missing_files': missing_files,
            'profiles_count': profiles_count,
            'profiles_dir_exists': os.path.exists(profiles_dir)
        }
    except Exception as e:
        return {'error': str(e)}

def show_detailed_status():
    """显示详细状态"""
    print("🔍 混合架构详细状态检查")
    print("=" * 80)
    print(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. 端口状态检查
    print("📡 端口状态检查:")
    print("-" * 40)
    
    port_5001_used = not check_port_available(5001)
    port_5002_used = not check_port_available(5002)
    
    print(f"  端口 5001 (生产服务器): {'🟢 占用中' if port_5001_used else '🔴 空闲'}")
    print(f"  端口 5002 (管理界面):   {'🟢 占用中' if port_5002_used else '🔴 空闲'}")
    
    # 2. API服务检查
    print("\n🌐 API服务检查:")
    print("-" * 40)
    
    # 检查生产服务器API
    if port_5001_used:
        prod_api = check_api_endpoint(5001)
        if prod_api['status'] == 'ok':
            print(f"  生产服务器 (5001): ✅ 正常 ({prod_api['response_time']:.3f}s)")
            print(f"    消息: {prod_api['message']}")
        else:
            print(f"  生产服务器 (5001): ❌ 异常 - {prod_api.get('error', '未知错误')}")
    else:
        print("  生产服务器 (5001): 🔴 未运行")
    
    # 检查管理界面API
    if port_5002_used:
        mgmt_api = check_api_endpoint(5002)
        if mgmt_api['status'] == 'ok':
            print(f"  管理界面 (5002):   ✅ 正常 ({mgmt_api['response_time']:.3f}s)")
            print(f"    消息: {mgmt_api['message']}")
        else:
            print(f"  管理界面 (5002):   ❌ 异常 - {mgmt_api.get('error', '未知错误')}")
    else:
        print("  管理界面 (5002):   🔴 未运行")
    
    # 3. 系统文件检查
    print("\n📁 系统文件检查:")
    print("-" * 40)
    
    sys_info = get_system_info()
    if 'error' not in sys_info:
        print(f"  任务数据库:     {'✅ 存在' if sys_info['tasks_db'] else '❌ 缺失'}")
        print(f"  配置文件:       {'✅ 存在' if sys_info['config_file'] else '❌ 缺失'}")
        print(f"  浏览器配置目录: {'✅ 存在' if sys_info['profiles_dir_exists'] else '❌ 缺失'}")
        print(f"  浏览器配置数量: {sys_info['profiles_count']} 个")
        
        if sys_info['missing_files']:
            print(f"  缺失文件: ❌ {', '.join(sys_info['missing_files'])}")
        else:
            print("  关键文件: ✅ 完整")
    else:
        print(f"  检查失败: ❌ {sys_info['error']}")
    
    # 4. 架构建议
    print("\n💡 架构状态分析:")
    print("-" * 40)
    
    if port_5001_used and not port_5002_used:
        print("  🎯 当前状态: 生产模式")
        print("  📊 性能: 高性能API服务")
        print("  🔧 管理: 需要时启动管理界面")
        print("  💡 建议: 需要管理时运行 'python start_management.py'")
        
    elif not port_5001_used and port_5002_used:
        print("  🎯 当前状态: 管理模式")
        print("  📊 性能: 标准GUI模式")
        print("  🔧 管理: 完整管理功能可用")
        print("  💡 建议: 高负载时启动生产服务器")
        
    elif port_5001_used and port_5002_used:
        print("  🎯 当前状态: 混合模式")
        print("  📊 性能: 最佳 - 生产API + 管理界面")
        print("  🔧 管理: 完整功能")
        print("  💡 建议: 理想配置，管理完成后可关闭管理界面")
        
    else:
        print("  🎯 当前状态: 未运行")
        print("  📊 性能: 无服务")
        print("  🔧 管理: 不可用")
        print("  💡 建议: 启动生产服务器 'python start_production.py'")
    
    # 5. 快速操作指南
    print("\n🚀 快速操作指南:")
    print("-" * 40)
    print("  启动生产服务器: python start_production.py")
    print("  启动管理界面:   python start_management.py")
    print("  查看状态:       python hybrid_status.py")
    print("  直接启动GUI:    python autoback.py")
    print("  直接启动生产:   python production_server.py")
    
    print("=" * 80)

def show_simple_status():
    """显示简单状态"""
    port_5001 = not check_port_available(5001)
    port_5002 = not check_port_available(5002)
    
    print("📊 混合架构状态:")
    print(f"  生产服务器: {'🟢 运行' if port_5001 else '🔴 停止'}")
    print(f"  管理界面:   {'🟢 运行' if port_5002 else '🔴 停止'}")
    
    if port_5001 and port_5002:
        print("  状态: 🎯 混合模式 (最佳)")
    elif port_5001:
        print("  状态: 🚀 生产模式")
    elif port_5002:
        print("  状态: 🔧 管理模式")
    else:
        print("  状态: ⚠️ 未运行")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == '--simple' or sys.argv[1] == '-s':
            show_simple_status()
        elif sys.argv[1] == '--help' or sys.argv[1] == '-h':
            print("混合架构状态检查工具")
            print("用法:")
            print("  python hybrid_status.py          # 详细状态")
            print("  python hybrid_status.py -s      # 简单状态")
            print("  python hybrid_status.py -h      # 帮助信息")
        else:
            show_detailed_status()
    else:
        show_detailed_status()

if __name__ == "__main__":
    main()

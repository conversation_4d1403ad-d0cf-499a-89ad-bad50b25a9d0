/**
 * 加载动画测试页面
 * 用于测试和演示加载屏幕组件
 */
import React, { useState } from 'react';
import { Button, Card, Space, Typography, Divider } from 'antd';
import { PlayCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import LoadingScreen, { LoadingStep } from '../components/LoadingScreen';

const { Title, Text } = Typography;

const LoadingTestPage: React.FC = () => {
  const [showLoading, setShowLoading] = useState(false);
  const [steps, setSteps] = useState<LoadingStep[]>([]);

  // 模拟加载步骤
  const simulateLoading = async () => {
    const loadingSteps: LoadingStep[] = [
      { id: 'config', name: '加载配置文件', status: 'waiting' },
      { id: 'connection', name: '连接服务器', status: 'waiting' },
      { id: 'database', name: '初始化数据库', status: 'waiting' },
      { id: 'user-auth', name: '验证用户认证', status: 'waiting' },
      { id: 'system-status', name: '获取系统状态', status: 'waiting' },
      { id: 'complete', name: '初始化完成', status: 'waiting' }
    ];

    setSteps(loadingSteps);
    setShowLoading(true);

    // 模拟每个步骤的执行
    for (let i = 0; i < loadingSteps.length; i++) {
      const currentStep = loadingSteps[i];
      
      // 设置当前步骤为加载中
      setSteps(prevSteps => 
        prevSteps.map(step => 
          step.id === currentStep.id 
            ? { ...step, status: 'loading', progress: 0 }
            : step
        )
      );

      // 模拟进度更新
      for (let progress = 0; progress <= 100; progress += 20) {
        await new Promise(resolve => setTimeout(resolve, 100));
        setSteps(prevSteps => 
          prevSteps.map(step => 
            step.id === currentStep.id 
              ? { ...step, progress }
              : step
          )
        );
      }

      // 设置步骤为完成
      setSteps(prevSteps => 
        prevSteps.map(step => 
          step.id === currentStep.id 
            ? { ...step, status: 'success', progress: 100 }
            : step
        )
      );

      // 步骤间延迟
      await new Promise(resolve => setTimeout(resolve, 300));
    }
  };

  // 模拟加载失败
  const simulateError = async () => {
    const loadingSteps: LoadingStep[] = [
      { id: 'config', name: '加载配置文件', status: 'waiting' },
      { id: 'connection', name: '连接服务器', status: 'waiting' },
      { id: 'database', name: '初始化数据库', status: 'waiting' }
    ];

    setSteps(loadingSteps);
    setShowLoading(true);

    // 前两个步骤成功
    for (let i = 0; i < 2; i++) {
      const currentStep = loadingSteps[i];
      
      setSteps(prevSteps => 
        prevSteps.map(step => 
          step.id === currentStep.id 
            ? { ...step, status: 'loading', progress: 0 }
            : step
        )
      );

      for (let progress = 0; progress <= 100; progress += 25) {
        await new Promise(resolve => setTimeout(resolve, 80));
        setSteps(prevSteps => 
          prevSteps.map(step => 
            step.id === currentStep.id 
              ? { ...step, progress }
              : step
          )
        );
      }

      setSteps(prevSteps => 
        prevSteps.map(step => 
          step.id === currentStep.id 
            ? { ...step, status: 'success', progress: 100 }
            : step
        )
      );

      await new Promise(resolve => setTimeout(resolve, 200));
    }

    // 第三个步骤失败
    setSteps(prevSteps => 
      prevSteps.map(step => 
        step.id === 'database' 
          ? { ...step, status: 'loading', progress: 0 }
          : step
      )
    );

    await new Promise(resolve => setTimeout(resolve, 500));

    setSteps(prevSteps => 
      prevSteps.map(step => 
        step.id === 'database' 
          ? { 
              ...step, 
              status: 'error', 
              error: '数据库连接失败，请检查服务器状态' 
            }
          : step
      )
    );
  };

  const handleLoadingComplete = () => {
    console.log('加载完成');
    setTimeout(() => {
      setShowLoading(false);
    }, 1000);
  };

  const handleLoadingError = (error: string) => {
    console.log('加载错误:', error);
    setTimeout(() => {
      setShowLoading(false);
    }, 3000);
  };

  const resetTest = () => {
    setShowLoading(false);
    setSteps([]);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={2}>加载动画测试页面</Title>
            <Text type="secondary">
              测试和演示应用初始化时的加载屏幕效果
            </Text>
          </div>

          <Divider />

          <div>
            <Title level={4}>测试选项</Title>
            <Space size="middle">
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={simulateLoading}
                disabled={showLoading}
              >
                模拟正常加载
              </Button>
              
              <Button
                danger
                icon={<PlayCircleOutlined />}
                onClick={simulateError}
                disabled={showLoading}
              >
                模拟加载失败
              </Button>
              
              <Button
                icon={<ReloadOutlined />}
                onClick={resetTest}
              >
                重置测试
              </Button>
            </Space>
          </div>

          <div>
            <Title level={4}>说明</Title>
            <ul>
              <li><strong>正常加载</strong>：模拟所有步骤都成功完成的情况</li>
              <li><strong>加载失败</strong>：模拟在数据库初始化步骤失败的情况</li>
              <li>加载屏幕会在应用启动时自动显示</li>
              <li>每个步骤都有进度指示和状态反馈</li>
              <li>失败时会显示具体的错误信息</li>
            </ul>
          </div>
        </Space>
      </Card>

      {/* 加载屏幕 */}
      <LoadingScreen
        visible={showLoading}
        steps={steps}
        onComplete={handleLoadingComplete}
        onError={handleLoadingError}
      />
    </div>
  );
};

export default LoadingTestPage;

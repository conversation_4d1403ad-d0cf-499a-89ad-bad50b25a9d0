#!/usr/bin/env python3
"""
混合架构管理器
统一管理生产服务器和管理界面的启动、停止和状态查看
"""
import os
import sys
import time
import socket
import subprocess
import requests
from datetime import datetime

def check_port_available(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('127.0.0.1', port))
            return True
    except OSError:
        return False

def check_production_server():
    """检查生产服务器状态"""
    try:
        response = requests.get('http://localhost:5001/api/test', timeout=3)
        return response.status_code == 200
    except:
        return False

def show_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("🎯 混合架构管理器")
    print("="*60)
    
    # 显示当前状态
    prod_running = not check_port_available(5001)
    mgmt_running = not check_port_available(5002)
    
    print("📊 当前状态:")
    print(f"  生产服务器 (5001): {'🟢 运行中' if prod_running else '🔴 已停止'}")
    print(f"  管理界面 (5002):   {'🟢 运行中' if mgmt_running else '🔴 已停止'}")
    
    if prod_running and mgmt_running:
        status = "🎯 混合模式 (最佳配置)"
    elif prod_running:
        status = "🚀 生产模式"
    elif mgmt_running:
        status = "🔧 管理模式"
    else:
        status = "⚠️ 未运行"
    
    print(f"  架构状态: {status}")
    
    print("\n🚀 操作选项:")
    print("  1. 启动生产服务器")
    print("  2. 启动管理界面")
    print("  3. 启动混合模式 (推荐)")
    print("  4. 查看详细状态")
    print("  5. 停止所有服务")
    print("  6. 重启服务")
    print("  0. 退出")
    print("="*60)

def start_production_server():
    """启动生产服务器"""
    if not check_port_available(5001):
        print("⚠️ 生产服务器已在运行")
        return False
    
    print("🚀 启动生产服务器...")
    try:
        # 使用start_production.py脚本启动
        if os.path.exists('start_production.py'):
            subprocess.Popen([sys.executable, 'start_production.py'])
        else:
            subprocess.Popen([sys.executable, 'production_server.py'])
        
        # 等待启动
        print("⏳ 等待服务器启动...")
        for i in range(10):
            time.sleep(1)
            if check_production_server():
                print("✅ 生产服务器启动成功")
                return True
            print(f"   等待中... ({i+1}/10)")
        
        print("❌ 生产服务器启动超时")
        return False
        
    except Exception as e:
        print(f"❌ 启动生产服务器失败: {e}")
        return False

def start_management_gui():
    """启动管理界面"""
    if not check_port_available(5002):
        print("⚠️ 管理界面已在运行")
        return False
    
    print("🖥️ 启动管理界面...")
    try:
        # 使用start_management.py脚本启动
        if os.path.exists('start_management.py'):
            subprocess.Popen([sys.executable, 'start_management.py'])
        else:
            subprocess.Popen([sys.executable, 'autoback.py'])
        
        print("✅ 管理界面启动命令已发送")
        print("💡 管理界面将在新窗口中打开")
        return True
        
    except Exception as e:
        print(f"❌ 启动管理界面失败: {e}")
        return False

def start_hybrid_mode():
    """启动混合模式"""
    print("🎯 启动混合模式...")
    
    success_count = 0
    
    # 启动生产服务器
    if check_port_available(5001):
        if start_production_server():
            success_count += 1
    else:
        print("✅ 生产服务器已运行")
        success_count += 1
    
    time.sleep(2)
    
    # 启动管理界面
    if check_port_available(5002):
        if start_management_gui():
            success_count += 1
    else:
        print("✅ 管理界面已运行")
        success_count += 1
    
    if success_count == 2:
        print("\n🎉 混合模式启动成功!")
        print("📍 生产服务器: http://localhost:5001")
        print("🖥️ 管理界面: GUI窗口")
        print("💡 这是推荐的运行模式")
        return True
    else:
        print("\n⚠️ 混合模式启动部分成功")
        return False

def show_detailed_status():
    """显示详细状态"""
    print("\n🔍 正在检查详细状态...")
    try:
        subprocess.run([sys.executable, 'hybrid_status.py'], check=True)
    except subprocess.CalledProcessError:
        print("❌ 无法运行状态检查")
    except FileNotFoundError:
        print("❌ hybrid_status.py 文件不存在")

def stop_all_services():
    """停止所有服务"""
    print("🛑 停止所有服务...")
    
    stopped_count = 0
    
    # 这里只能提示用户手动停止，因为我们无法直接杀死其他进程
    if not check_port_available(5001):
        print("⚠️ 请手动停止生产服务器 (Ctrl+C)")
        stopped_count += 1
    
    if not check_port_available(5002):
        print("⚠️ 请手动关闭管理界面窗口")
        stopped_count += 1
    
    if stopped_count == 0:
        print("✅ 没有运行中的服务")
    else:
        print(f"💡 请手动停止 {stopped_count} 个服务")

def restart_services():
    """重启服务"""
    print("🔄 重启服务...")
    print("💡 请先手动停止现有服务，然后选择启动选项")

def main():
    """主函数"""
    print("🎯 混合架构管理器")
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    while True:
        try:
            show_menu()
            choice = input("\n请选择操作 (0-6): ").strip()
            
            if choice == '0':
                print("👋 再见!")
                break
            elif choice == '1':
                start_production_server()
            elif choice == '2':
                start_management_gui()
            elif choice == '3':
                start_hybrid_mode()
            elif choice == '4':
                show_detailed_status()
            elif choice == '5':
                stop_all_services()
            elif choice == '6':
                restart_services()
            else:
                print("❌ 无效选择，请输入 0-6")
            
            if choice != '0':
                input("\n按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n\n🔄 用户中断")
            break
        except Exception as e:
            print(f"\n❌ 操作异常: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()

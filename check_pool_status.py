#!/usr/bin/env python3
"""
连接池状态检查工具
用于检查当前系统中连接池的使用情况
"""

import requests
import json
import time
from datetime import datetime

def check_production_server_pool():
    """检查生产服务器的连接池状态"""
    try:
        # 使用域名访问生产服务器的测试接口
        response = requests.get('https://aug8.xyz/api/test', timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("🟢 生产服务器 (aug8.xyz) - 运行正常")
            if data.get('success'):
                print(f"   📡 API响应: {data.get('message', 'OK')}")
            return True
        else:
            print(f"🔴 生产服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("🔴 生产服务器 (aug8.xyz) - 无法连接")
        return False
    except requests.exceptions.Timeout:
        print("🔴 生产服务器 (aug8.xyz) - 连接超时")
        return False
    except Exception as e:
        print(f"🔴 生产服务器检查失败: {e}")
        return False

def check_autoback_server_pool():
    """检查autoback服务器的连接池状态"""
    try:
        # 尝试访问autoback服务器的系统状态接口
        response = requests.get('http://localhost:5002/api/system/status', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("🟢 Autoback服务器 (localhost:5002) - 运行正常")
            if 'queue_length' in data:
                print(f"   📊 任务队列长度: {data['queue_length']}")
            return True
        else:
            print(f"🔴 Autoback服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("🔴 Autoback服务器 (localhost:5002) - 无法连接")
        return False
    except Exception as e:
        print(f"🔴 Autoback服务器检查失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    try:
        # 尝试登录接口来测试数据库连接
        test_data = {
            "username": "test_connection_check",
            "password": "test_password"
        }

        response = requests.post('https://aug8.xyz/api/auth/login',
                               json=test_data, timeout=15)

        if response.status_code == 200:
            result = response.json()
            if not result.get('success'):
                print("🟡 数据库连接正常 (测试登录失败是预期的)")
                return True
        elif response.status_code == 400 or response.status_code == 401:
            print("🟡 数据库连接正常 (测试登录失败是预期的)")
            return True
        else:
            print(f"🔴 数据库连接可能有问题: HTTP {response.status_code}")
            return False

    except requests.exceptions.Timeout:
        print("🔴 数据库连接超时 - 可能是连接池耗尽")
        return False
    except requests.exceptions.ConnectionError:
        print("🔴 无法连接到服务器")
        return False
    except Exception as e:
        print(f"🔴 数据库连接测试失败: {e}")
        return False

def simulate_concurrent_requests(num_requests=5):
    """模拟并发请求来测试连接池压力"""
    print(f"\n🧪 模拟 {num_requests} 个并发请求...")

    import threading
    import time

    results = []
    start_time = time.time()

    def make_request(request_id):
        try:
            test_data = {
                "username": f"test_user_{request_id}",
                "password": "test_password"
            }

            req_start = time.time()
            response = requests.post('https://aug8.xyz/api/auth/login',
                                   json=test_data, timeout=15)
            req_end = time.time()

            results.append({
                'id': request_id,
                'status_code': response.status_code,
                'response_time': req_end - req_start,
                'success': True
            })

        except requests.exceptions.Timeout:
            results.append({
                'id': request_id,
                'error': 'Timeout',
                'success': False
            })
        except Exception as e:
            results.append({
                'id': request_id,
                'error': str(e),
                'success': False
            })

    # 创建并启动线程
    threads = []
    for i in range(num_requests):
        thread = threading.Thread(target=make_request, args=(i+1,))
        threads.append(thread)
        thread.start()

    # 等待所有线程完成
    for thread in threads:
        thread.join()

    end_time = time.time()
    total_time = end_time - start_time

    # 分析结果
    successful_requests = [r for r in results if r.get('success', False)]
    failed_requests = [r for r in results if not r.get('success', False)]

    print(f"📊 并发测试结果:")
    print(f"   ✅ 成功请求: {len(successful_requests)}/{num_requests}")
    print(f"   ❌ 失败请求: {len(failed_requests)}/{num_requests}")
    print(f"   ⏱️ 总耗时: {total_time:.2f}秒")

    if successful_requests:
        avg_response_time = sum(r['response_time'] for r in successful_requests) / len(successful_requests)
        max_response_time = max(r['response_time'] for r in successful_requests)
        min_response_time = min(r['response_time'] for r in successful_requests)

        print(f"   📈 响应时间统计:")
        print(f"      平均: {avg_response_time:.3f}秒")
        print(f"      最大: {max_response_time:.3f}秒")
        print(f"      最小: {min_response_time:.3f}秒")

    if failed_requests:
        print(f"   ❌ 失败原因:")
        for req in failed_requests:
            print(f"      请求{req['id']}: {req.get('error', '未知错误')}")

    # 判断连接池状态
    timeout_count = len([r for r in failed_requests if r.get('error') == 'Timeout'])
    if timeout_count > 0:
        print(f"⚠️ 检测到 {timeout_count} 个超时请求，可能存在连接池耗尽问题")
        return False
    elif len(failed_requests) == 0:
        print("✅ 连接池工作正常，无并发问题")
        return True
    else:
        print("🟡 存在一些请求失败，但不是连接池问题")
        return True

def main():
    """主函数"""
    print("=" * 60)
    print(f"🔍 连接池状态检查 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    # 1. 检查服务器状态
    print("\n📡 检查服务器状态:")
    prod_ok = check_production_server_pool()
    auto_ok = check_autoback_server_pool()

    if not prod_ok:
        print("❌ 生产服务器未运行，无法检查连接池状态")
        return

    # 2. 测试数据库连接
    print("\n🗄️ 测试数据库连接:")
    db_ok = test_database_connection()

    # 3. 并发压力测试
    if db_ok:
        pool_ok = simulate_concurrent_requests(8)  # 测试8个并发请求

        print("\n" + "=" * 60)
        print("📋 连接池状态总结:")
        print("=" * 60)

        if pool_ok:
            print("✅ 连接池工作正常")
            print("💡 建议: 当前连接池配置足够应对正常负载")
        else:
            print("❌ 连接池可能存在问题")
            print("💡 建议: 考虑增加连接池大小或移除连接池使用直连")
    else:
        print("\n❌ 数据库连接有问题，无法进行连接池测试")

    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()

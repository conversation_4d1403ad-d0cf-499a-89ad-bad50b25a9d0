/**
 * 任务状态显示组件
 */
import React, { useEffect, useState } from 'react';
import { Card, Badge, Progress, Button, Space, Typography, Divider, Empty } from 'antd';
import {
  ClockCircleOutlined,
  LoadingOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useTaskState, useTaskActions } from '../stores/taskStore';
import { useSystemState, useSystemActions } from '../stores/systemStore';
import { TaskStatus as TaskStatusEnum, getStatusColor, getStatusText, formatDateTime } from '../services/api';

const { Text, Title } = Typography;

const TaskStatus: React.FC = () => {
  const { tasks } = useTaskState();
  const { refreshQueue } = useTaskActions();
  const { systemStatus } = useSystemState();
  const { fetchSystemStatus } = useSystemActions();
  const [showAnimation, setShowAnimation] = useState(false);
  const [lastCompletedTaskId, setLastCompletedTaskId] = useState<string | null>(null);

  // 获取要显示的任务（显示最新任务）
  const getDisplayTask = () => {
    if (tasks.length > 0) {
      // 按创建时间排序，返回最新的任务
      const sortedTasks = tasks.sort((a, b) => {
        const timeA = new Date(a.created_at).getTime();
        const timeB = new Date(b.created_at).getTime();
        return timeB - timeA;
      });

      return sortedTasks[0];
    }

    return null;
  };

  const displayTask = getDisplayTask();

  // 监听任务完成，触发动画
  useEffect(() => {
    if (displayTask &&
        (displayTask.status === TaskStatusEnum.SUCCESS || displayTask.status === TaskStatusEnum.FAILED) &&
        displayTask.id !== lastCompletedTaskId) {

      setShowAnimation(true);
      setLastCompletedTaskId(displayTask.id);

      // 3秒后隐藏动画
      const timer = setTimeout(() => {
        setShowAnimation(false);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [displayTask, lastCompletedTaskId]);

  // 监听任务状态变化，强制重新渲染进度条
  useEffect(() => {
    if (displayTask) {
      console.log('任务状态更新:', {
        id: displayTask.id,
        email: displayTask.email,
        status: displayTask.status,
        progress: getProgress(displayTask.status)
      });
    }
  }, [displayTask?.status, displayTask?.id, displayTask]);

  // 系统状态现在由systemStore统一管理，无需在此处刷新

  // 获取状态图标
  const getStatusIcon = (status: TaskStatusEnum) => {
    switch (status) {
      case TaskStatusEnum.PENDING:
        return <ClockCircleOutlined style={{ color: getStatusColor(status) }} />;
      case TaskStatusEnum.PROCESSING:
        return <LoadingOutlined style={{ color: getStatusColor(status) }} />;
      case TaskStatusEnum.SUCCESS:
        return <CheckCircleOutlined style={{ color: getStatusColor(status) }} />;
      case TaskStatusEnum.FAILED:
        return <CloseCircleOutlined style={{ color: getStatusColor(status) }} />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  // 获取进度百分比
  const getProgress = (status: TaskStatusEnum) => {
    switch (status) {
      case TaskStatusEnum.PENDING:
        return 20;
      case TaskStatusEnum.PROCESSING:
        return 60;
      case TaskStatusEnum.SUCCESS:
      case TaskStatusEnum.FAILED:
        return 100;
      default:
        return 0;
    }
  };

  // 获取进度状态
  const getProgressStatus = (status: TaskStatusEnum) => {
    switch (status) {
      case TaskStatusEnum.SUCCESS:
        return 'success';
      case TaskStatusEnum.FAILED:
        return 'exception';
      case TaskStatusEnum.PROCESSING:
        return 'active';
      default:
        return 'normal';
    }
  };

  return (
    <Card
      title="任务状态"
      extra={
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => {
              fetchSystemStatus();
              refreshQueue();
              console.log('手动刷新任务状态和队列');
            }}
            size="small"
          >
            刷新状态
          </Button>

        </Space>
      }
      style={{ width: '100%' }}
    >
      {/* 系统状态 */}
      {systemStatus && (
        <div style={{ marginBottom: 16 }}>
          <Title level={5}>系统状态</Title>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Badge
                status={systemStatus.is_processing ? 'processing' : 'success'}
                text={systemStatus.is_processing ? '正在处理中' : '空闲中'}
              />
            </div>
            {systemStatus.current_email && (
              <Text type="secondary">
                当前处理: {systemStatus.current_email}
              </Text>
            )}

            {/* 系统可激活账号今日剩余 */}
            <div style={{
              padding: '8px 12px',
              background: systemStatus.available_profiles === 0 ? '#fff2f0' :
                         systemStatus.available_profiles <= 5 ? '#fffbe6' : '#f6ffed',
              borderRadius: '6px',
              border: `1px solid ${
                systemStatus.available_profiles === 0 ? '#ffccc7' :
                systemStatus.available_profiles <= 5 ? '#ffe58f' : '#b7eb8f'
              }`
            }}>
              <Space align="center">
                <Text strong style={{
                  color: systemStatus.available_profiles === 0 ? '#ff4d4f' :
                         systemStatus.available_profiles <= 5 ? '#faad14' : '#52c41a'
                }}>
                  系统可激活账号今日剩余: {systemStatus.available_profiles}
                </Text>
                {systemStatus.available_profiles === 0 && (
                  <Text type="danger" style={{ fontSize: '12px' }}>
                    (额度已用完)
                  </Text>
                )}
                {systemStatus.available_profiles > 0 && systemStatus.available_profiles <= 5 && (
                  <Text type="warning" style={{ fontSize: '12px' }}>
                    (剩余较少)
                  </Text>
                )}
              </Space>
            </div>

            <Text style={{ color: '#faad14', fontWeight: 'bold' }}>
              有 {systemStatus.queue_length} 人在排队激活 处理过程中请耐心等待
            </Text>
          </Space>
        </div>
      )}

      <Divider />

      {/* 任务状态显示 */}
      {displayTask ? (
        <div>
          <Title level={5}>
            {(() => {
              if (displayTask.status === TaskStatusEnum.PENDING) {
                return '⏳ 等待处理';
              } else if (displayTask.status === TaskStatusEnum.PROCESSING) {
                return '🔄 正在处理';
              } else if (displayTask.status === TaskStatusEnum.SUCCESS) {
                return '✅ 处理成功';
              } else if (displayTask.status === TaskStatusEnum.FAILED) {
                return '❌ 处理失败';
              } else {
                return '最新任务';
              }
            })()}
          </Title>
          <Space direction="vertical" style={{ width: '100%' }}>
            {/* 任务信息 */}
            <div>
              <Text strong>邮箱: </Text>
              <Text>{displayTask.email}</Text>
            </div>

            <div>
              <Text strong>任务ID: </Text>
              <Text code>{displayTask.id}</Text>
            </div>

            <div>
              <Text strong>创建时间: </Text>
              <Text>{formatDateTime(displayTask.created_at)}</Text>
            </div>

            {displayTask.completed_at && (
              <div>
                <Text strong>完成时间: </Text>
                <Text>{formatDateTime(displayTask.completed_at)}</Text>
              </div>
            )}

            {/* 状态显示 */}
            <div>
              <Space>
                {getStatusIcon(displayTask.status)}
                <Badge
                  color={getStatusColor(displayTask.status)}
                  text={`${getStatusText(displayTask.status)} (${getProgress(displayTask.status)}%)`}
                />
              </Space>
            </div>

            {/* 进度条 - 动态响应任务状态 */}
            <Progress
              key={`progress-${displayTask.id}-${displayTask.status}`}
              percent={getProgress(displayTask.status)}
              status={getProgressStatus(displayTask.status) as any}
              strokeColor={getStatusColor(displayTask.status)}
              strokeLinecap="round"
              trailColor="#f0f0f0"
              showInfo={true}
              size="default"
              format={(percent) => {
                const currentStatus = displayTask.status;
                console.log('Progress format - Status:', currentStatus, 'Percent:', percent);

                if (currentStatus === TaskStatusEnum.SUCCESS) {
                  return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
                }
                if (currentStatus === TaskStatusEnum.FAILED) {
                  return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
                }
                if (currentStatus === TaskStatusEnum.PROCESSING) {
                  return <LoadingOutlined style={{ color: '#1890ff' }} />;
                }
                if (currentStatus === TaskStatusEnum.PENDING) {
                  return '等待中...';
                }
                return `${percent}%`;
              }}
            />



            {/* 错误显示 */}
            {displayTask.error && (
              <div>
                <Text strong>错误: </Text>
                <Text type="danger">{displayTask.error}</Text>
              </div>
            )}



            {/* 成功动画提示 */}
            {displayTask.status === TaskStatusEnum.SUCCESS && (
              <div style={{
                marginTop: 16,
                padding: '12px',
                backgroundColor: '#f6ffed',
                border: '1px solid #b7eb8f',
                borderRadius: '6px',
                textAlign: 'center',
                transform: showAnimation ? 'scale(1.05)' : 'scale(1)',
                transition: 'all 0.3s ease-in-out',
                boxShadow: showAnimation ? '0 4px 20px rgba(82, 196, 26, 0.3)' : 'none'
              }}>
                <CheckCircleOutlined style={{
                  color: '#52c41a',
                  fontSize: '24px',
                  marginBottom: '8px'
                }} />
                <div>
                  <Text strong style={{ color: '#52c41a' }}>🎉 处理成功！</Text>
                </div>
              </div>
            )}

            {/* 失败动画提示 */}
            {displayTask.status === TaskStatusEnum.FAILED && (
              <div style={{
                marginTop: 16,
                padding: '12px',
                backgroundColor: '#fff2f0',
                border: '1px solid #ffccc7',
                borderRadius: '6px',
                textAlign: 'center',
                transform: showAnimation ? 'scale(1.05)' : 'scale(1)',
                transition: 'all 0.3s ease-in-out',
                boxShadow: showAnimation ? '0 4px 20px rgba(255, 77, 79, 0.3)' : 'none'
              }}>
                <CloseCircleOutlined style={{
                  color: '#ff4d4f',
                  fontSize: '24px',
                  marginBottom: '8px'
                }} />
                <div>
                  <Text strong style={{ color: '#ff4d4f' }}>❌ 处理失败</Text>
                </div>
                <div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    邮箱 {displayTask.email} 处理时出现错误
                  </Text>
                </div>
              </div>
            )}
          </Space>
        </div>
      ) : (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无任务"
        />
      )}
    </Card>
  );
};

export default TaskStatus;

"""
用户认证管理模块
处理用户注册、登录、权限验证等功能
"""
import bcrypt
import jwt
import json
import mysql.connector
from mysql.connector import Error, pooling
from datetime import datetime, timedelta
import logging
import time
import threading

class AuthManager:
    def __init__(self, main_db_config, jihuoma_db_config, jwt_secret="your-secret-key"):
        self.main_db_config = main_db_config
        self.jihuoma_db_config = jihuoma_db_config
        self.jwt_secret = jwt_secret
        self.logger = logging.getLogger(__name__)

        # 初始化MySQL连接池（仅主数据库）
        self.main_db_pool = None
        self._pool_lock = threading.Lock()

        # 创建主数据库连接池
        self._create_main_connection_pool()

    def _create_main_connection_pool(self):
        """创建主数据库连接池（优化版：轻量化 + 智能化）"""
        try:
            # 🔥 优化的连接池配置（移除不支持的参数）
            main_pool_config = self.main_db_config.copy()
            main_pool_config.update({
                'pool_name': 'main_db_pool',
                'pool_size': 2,                    # 🔥 从8减少到2个核心连接
                'pool_reset_session': True,
                'autocommit': True,
                'connection_timeout': 8,           # 🔥 从15秒减少到8秒
                'sql_mode': '',
                'charset': 'utf8mb4',
                'use_unicode': True
            })

            # 创建主数据库连接池
            self.main_db_pool = pooling.MySQLConnectionPool(**main_pool_config)
            self.logger.info("✅ 主数据库连接池创建成功 (大小: 2, 优化配置)")

            # 🔥 启动连接池健康监控
            self.start_pool_health_monitor()

        except Exception as e:
            self.logger.error(f"❌ 创建主数据库连接池失败: {e}")
            # 连接池创建失败时，回退到原来的连接方式
            self.main_db_pool = None

    def get_main_db_connection(self, timeout=5):
        """智能连接获取：验证 + 降级 + 重建"""
        start_time = time.time()

        try:
            # 优先使用连接池
            if self.main_db_pool:
                try:
                    connection = self.main_db_pool.get_connection()

                    # 🔥 强化验证：不仅检查连接状态，还执行简单查询
                    if self._validate_connection(connection):
                        elapsed = time.time() - start_time
                        self.logger.debug(f"✅ 连接池连接获取成功 ({elapsed:.3f}s)")
                        return connection
                    else:
                        # 连接无效，关闭并触发重建
                        try:
                            connection.close()
                        except:
                            pass
                        self._trigger_pool_rebuild()

                except Exception as pool_error:
                    self.logger.warning(f"⚠️ 连接池获取失败: {pool_error}")
                    # 连接池失败时，触发重建
                    if "No connection available" in str(pool_error):
                        self._trigger_pool_rebuild()

            # 🔥 降级策略：连接池失败时使用快速直连
            return self._create_fast_direct_connection(timeout)

        except Exception as e:
            elapsed = time.time() - start_time
            self.logger.warning(f"⚠️ 连接获取失败 ({elapsed:.3f}s): {e}")
            return self._create_fast_direct_connection(timeout)

    def _validate_connection(self, connection):
        """连接有效性验证"""
        try:
            if not connection.is_connected():
                return False

            # 🔥 执行轻量级查询验证
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()

            return result is not None
        except:
            return False

    def _create_fast_direct_connection(self, timeout=5):
        """快速直连：优化配置 + 快速失败"""
        try:
            # 🔥 精简配置，专注速度
            fast_config = {
                'host': self.main_db_config['host'],
                'database': self.main_db_config['database'],
                'user': self.main_db_config['user'],
                'password': self.main_db_config['password'],
                'charset': 'utf8mb4',
                'autocommit': True,
                'connection_timeout': timeout,  # 🔥 使用传入的超时时间
                'use_unicode': True,
                'raise_on_warnings': False
            }

            connection = mysql.connector.connect(**fast_config)
            self.logger.info(f"✅ 快速直连成功 (超时:{timeout}s)")
            return connection

        except Exception as e:
            self.logger.error(f"❌ 快速直连失败: {e}")
            return None

    def _trigger_pool_rebuild(self):
        """智能连接池重建：防抖 + 异步"""
        current_time = time.time()

        # 🔥 防抖机制：5分钟内不重复重建
        if hasattr(self, '_last_rebuild_time'):
            if current_time - self._last_rebuild_time < 300:
                return

        self._last_rebuild_time = current_time

        # 🔥 异步重建，不阻塞当前请求
        def rebuild_in_background():
            try:
                with self._pool_lock:
                    self.logger.info("🔄 开始重建连接池...")
                    old_pool = self.main_db_pool

                    # 创建新连接池
                    self._create_main_connection_pool()

                    # 清理旧连接池
                    if old_pool:
                        try:
                            old_pool._remove_connections()
                        except:
                            pass

                    self.logger.info("✅ 连接池重建完成")
            except Exception as e:
                self.logger.error(f"❌ 连接池重建失败: {e}")

        threading.Thread(target=rebuild_in_background, daemon=True).start()

    def get_jihuoma_db_connection(self, timeout=10):
        """获取激活码数据库连接（直接连接，无连接池）"""
        start_time = time.time()

        try:
            # 激活码数据库使用频率很低，直接连接即可
            config = self.jihuoma_db_config.copy()
            config.update({
                'connection_timeout': timeout,
                'autocommit': True,
                'charset': 'utf8mb4',
                'use_unicode': True
            })

            connection = mysql.connector.connect(**config)

            if connection.is_connected():
                elapsed = time.time() - start_time
                self.logger.debug(f"✅ 激活码数据库连接成功 ({elapsed:.3f}s)")
                return connection
            else:
                raise Error("激活码数据库连接失败")

        except Exception as e:
            elapsed = time.time() - start_time
            self.logger.error(f"❌ 激活码数据库连接失败 ({elapsed:.3f}s): {e}")
            return None

    def hash_password(self, password):
        """密码加密"""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

    def verify_password(self, password, hashed):
        """密码验证"""
        # 确保hashed是bytes类型
        if isinstance(hashed, str):
            hashed = hashed.encode('utf-8')
        return bcrypt.checkpw(password.encode('utf-8'), hashed)

    def generate_jwt_token(self, user_id, username):
        """生成JWT令牌"""
        payload = {
            'user_id': user_id,
            'username': username,
            'exp': datetime.utcnow() + timedelta(days=7)  # 7天过期
        }
        return jwt.encode(payload, self.jwt_secret, algorithm='HS256')

    def verify_jwt_token(self, token):
        """验证JWT令牌"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=['HS256'])
            self.logger.debug(f"JWT token验证成功，用户ID: {payload.get('user_id')}")
            return payload
        except jwt.ExpiredSignatureError:
            self.logger.debug("JWT token已过期")
            return None
        except jwt.InvalidTokenError as e:
            self.logger.debug(f"JWT token无效: {e}")
            return None
        except Exception as e:
            self.logger.error(f"JWT token验证异常: {e}")
            return None

    def get_system_config(self, config_key):
        """获取系统配置"""
        connection = self.get_main_db_connection()
        if not connection:
            return None

        try:
            cursor = connection.cursor()
            query = "SELECT config_value FROM systemconfig WHERE config_key = %s"
            cursor.execute(query, (config_key,))
            result = cursor.fetchone()
            return result[0] if result else None
        except Error as e:
            self.logger.error(f"获取系统配置失败: {e}")
            return None
        finally:
            cursor.close()
            connection.close()

    def register_user(self, username, password):
        """用户注册"""
        # 验证用户名格式 - 移除限制，只要求非空
        if not username or len(username.strip()) == 0:
            return {"success": False, "message": "用户名不能为空"}

        connection = self.get_main_db_connection()
        if not connection:
            return {"success": False, "message": "数据库连接失败"}

        try:
            cursor = connection.cursor()

            # 检查用户名是否已存在
            check_query = "SELECT COUNT(*) FROM users WHERE username = %s"
            cursor.execute(check_query, (username,))
            if cursor.fetchone()[0] > 0:
                return {"success": False, "message": "用户名已存在"}

            # 获取注册赠送次数
            gift_quota = self.get_system_config('register_gift_quota')
            gift_quota = int(gift_quota) if gift_quota else 3

            # 加密密码
            hashed_password = self.hash_password(password)

            # 创建用户
            insert_query = """
                INSERT INTO users (username, password, time_quota)
                VALUES (%s, %s, %s)
            """
            cursor.execute(insert_query, (username, hashed_password, gift_quota))
            connection.commit()

            self.logger.info(f"用户注册成功: {username}")
            return {
                "success": True,
                "message": f"注册成功！赠送 {gift_quota} 次免费使用机会",
                "gift_quota": gift_quota
            }

        except Error as e:
            connection.rollback()
            self.logger.error(f"用户注册失败: {e}")
            return {"success": False, "message": "注册失败，请稍后重试"}
        finally:
            cursor.close()
            connection.close()

    def login_user(self, username, password):
        """用户登录"""
        connection = self.get_main_db_connection()
        if not connection:
            return {"success": False, "message": "数据库连接失败"}

        try:
            cursor = connection.cursor()

            # 获取用户信息
            query = """
                SELECT id, username, password, time_quota, time_count, created_at, lastusetime
                FROM users WHERE username = %s
            """
            cursor.execute(query, (username,))
            user = cursor.fetchone()

            if not user:
                return {"success": False, "message": "用户名或密码错误"}

            # 验证密码
            if not self.verify_password(password, user[2]):
                return {"success": False, "message": "用户名或密码错误"}

            # 更新最后登录时间
            update_query = "UPDATE users SET lastusetime = CURRENT_TIMESTAMP WHERE id = %s"
            cursor.execute(update_query, (user[0],))
            connection.commit()

            # 生成JWT令牌
            token = self.generate_jwt_token(user[0], user[1])

            # 返回用户信息
            user_info = {
                "id": user[0],
                "username": user[1],
                "time_quota": user[3],
                "time_count": user[4],
                "created_at": user[5].strftime('%Y-%m-%d %H:%M:%S') if user[5] else None,
                "lastusetime": user[6].strftime('%Y-%m-%d %H:%M:%S') if user[6] else None
            }

            self.logger.info(f"用户登录成功: {username}")
            return {
                "success": True,
                "message": "登录成功",
                "token": token,
                "user": user_info
            }

        except Error as e:
            self.logger.error(f"用户登录失败: {e}")
            return {"success": False, "message": "登录失败，请稍后重试"}
        finally:
            cursor.close()
            connection.close()

    def get_user_info(self, user_id):
        """获取用户信息"""
        connection = self.get_main_db_connection()
        if not connection:
            self.logger.error("获取用户信息失败: 数据库连接失败")
            return None

        try:
            cursor = connection.cursor()
            query = """
                SELECT id, username, time_quota, time_count, created_at, lastusetime
                FROM users WHERE id = %s
            """
            cursor.execute(query, (user_id,))
            user = cursor.fetchone()

            if user:
                user_info = {
                    "id": user[0],
                    "username": user[1],
                    "time_quota": user[2],
                    "time_count": user[3],
                    "created_at": user[4].strftime('%Y-%m-%d %H:%M:%S') if user[4] else None,
                    "lastusetime": user[5].strftime('%Y-%m-%d %H:%M:%S') if user[5] else None
                }
                self.logger.debug(f"获取用户信息成功: {user_info['username']} (ID: {user_info['id']})")
                return user_info
            else:
                self.logger.debug(f"用户不存在: user_id={user_id}")
                return None

        except Error as e:
            self.logger.error(f"获取用户信息失败: {e}")
            return None
        finally:
            cursor.close()
            connection.close()

    def check_user_quota(self, user_id):
        """检查用户剩余次数"""
        connection = self.get_main_db_connection()
        if not connection:
            return False

        try:
            cursor = connection.cursor()
            query = "SELECT time_quota FROM users WHERE id = %s"
            cursor.execute(query, (user_id,))
            result = cursor.fetchone()
            return result[0] > 0 if result else False

        except Error as e:
            self.logger.error(f"检查用户次数失败: {e}")
            return False
        finally:
            cursor.close()
            connection.close()

    def consume_user_quota(self, user_id):
        """消费用户次数（任务成功后调用）"""
        connection = self.get_main_db_connection()
        if not connection:
            return False

        try:
            cursor = connection.cursor()
            query = """
                UPDATE users
                SET time_quota = time_quota - 1,
                    time_count = time_count + 1,
                    lastusetime = CURRENT_TIMESTAMP
                WHERE id = %s AND time_quota > 0
            """
            cursor.execute(query, (user_id,))
            connection.commit()

            # 检查是否成功更新
            if cursor.rowcount > 0:
                self.logger.info(f"用户 {user_id} 次数消费成功")
                return True
            else:
                self.logger.warning(f"用户 {user_id} 次数不足或不存在")
                return False

        except Error as e:
            connection.rollback()
            self.logger.error(f"消费用户次数失败: {e}")
            return False
        finally:
            cursor.close()
            connection.close()

    def recharge_with_activation_code(self, user_id, activation_code):
        """使用激活码充值"""
        main_conn = None
        jihuoma_conn = None

        try:
            # 获取数据库连接
            main_conn = self.get_main_db_connection()
            jihuoma_conn = self.get_jihuoma_db_connection()

            if not main_conn or not jihuoma_conn:
                return {"success": False, "message": "数据库连接失败"}

            # 开始事务
            main_conn.start_transaction()
            jihuoma_conn.start_transaction()

            # 1. 验证激活码是否存在且未使用，并且remark必须是augactive
            jihuoma_cursor = jihuoma_conn.cursor()
            check_query = """
                SELECT code, use_times, is_used
                FROM activation_codes
                WHERE code = %s AND is_used = 0 AND remark = 'augactive'
            """
            jihuoma_cursor.execute(check_query, (activation_code,))
            code_info = jihuoma_cursor.fetchone()

            if not code_info:
                return {"success": False, "message": "激活码不存在、已被使用或不适用于此产品"}

            use_times = code_info[1]

            # 2. 检查用户是否已使用过此激活码
            main_cursor = main_conn.cursor()
            user_query = "SELECT activecode FROM users WHERE id = %s"
            main_cursor.execute(user_query, (user_id,))
            user_result = main_cursor.fetchone()

            if user_result and user_result[0]:
                used_codes = json.loads(user_result[0]) if user_result[0] else []
                if activation_code in used_codes:
                    return {"success": False, "message": "您已使用过此激活码"}

            # 3. 更新用户次数和激活码记录
            update_user_query = """
                UPDATE users
                SET time_quota = time_quota + %s,
                    activecode = JSON_ARRAY_APPEND(IFNULL(activecode, '[]'), '$', %s)
                WHERE id = %s
            """
            main_cursor.execute(update_user_query, (use_times, activation_code, user_id))

            # 4. 标记激活码已使用并记录使用时间
            update_code_query = "UPDATE activation_codes SET is_used = 1, used_at = NOW() WHERE code = %s"
            jihuoma_cursor.execute(update_code_query, (activation_code,))

            # 提交事务
            main_conn.commit()
            jihuoma_conn.commit()

            self.logger.info(f"用户 {user_id} 使用激活码 {activation_code} 充值成功，获得 {use_times} 次")
            return {
                "success": True,
                "message": f"充值成功！获得 {use_times} 次使用机会",
                "added_quota": use_times
            }

        except Error as e:
            # 回滚事务
            if main_conn:
                main_conn.rollback()
            if jihuoma_conn:
                jihuoma_conn.rollback()

            self.logger.error(f"激活码充值失败: {e}")
            return {"success": False, "message": "充值失败，请稍后重试"}

        finally:
            # 关闭连接
            if main_conn:
                if 'main_cursor' in locals():
                    main_cursor.close()
                main_conn.close()
            if jihuoma_conn:
                if 'jihuoma_cursor' in locals():
                    jihuoma_cursor.close()
                jihuoma_conn.close()

    def get_activation_code_url(self):
        """获取激活码购买链接"""
        return self.get_system_config('activation_code_url') or "https://example.com/buy-code"

    def require_auth(self, token):
        """权限验证辅助函数 - 改进错误处理"""
        try:
            if not token:
                self.logger.debug("认证失败: 没有提供token")
                return None

            # 移除Bearer前缀
            if token.startswith('Bearer '):
                token = token[7:]

            payload = self.verify_jwt_token(token)
            if not payload:
                self.logger.debug("认证失败: JWT token验证失败")
                return None

            # 获取最新用户信息，添加超时保护
            try:
                user_info = self.get_user_info(payload['user_id'])
                if not user_info:
                    self.logger.debug(f"认证失败: 无法获取用户信息，user_id={payload.get('user_id')}")
                    return None

                self.logger.debug(f"认证成功: 用户 {user_info['username']} (ID: {user_info['id']})")
                return user_info

            except Exception as db_error:
                # 数据库连接问题时，记录详细错误但不抛出异常
                error_msg = str(db_error)
                if 'MySQL Connection not available' in error_msg:
                    self.logger.warning("认证失败: MySQL连接不可用，可能是网络问题")
                elif 'timeout' in error_msg.lower():
                    self.logger.warning("认证失败: 数据库连接超时")
                else:
                    self.logger.warning(f"认证失败: 数据库错误 - {error_msg}")
                return None

        except Exception as e:
            self.logger.error(f"认证过程异常: {e}")
            return None

    def start_pool_health_monitor(self):
        """启动连接池健康监控"""
        def health_check_loop():
            while True:
                try:
                    time.sleep(600)  # 🔥 每10分钟检查一次
                    self._check_pool_health()
                except Exception as e:
                    self.logger.error(f"连接池健康检查失败: {e}")
                    time.sleep(300)  # 出错时5分钟后重试

        threading.Thread(target=health_check_loop, daemon=True).start()
        self.logger.info("✅ 连接池健康监控已启动")

    def _check_pool_health(self):
        """连接池健康检查"""
        if not self.main_db_pool:
            self.logger.warning("⚠️ 连接池不存在，尝试重建")
            self._create_main_connection_pool()
            return

        try:
            # 🔥 快速健康检查
            available = self.main_db_pool._cnx_queue.qsize()
            total = self.main_db_pool._pool_size

            self.logger.debug(f"📊 连接池状态: {available}/{total} 可用")

            # 如果可用连接过少，预热连接池
            if available < 1:
                self.logger.info("🔄 连接池预热中...")
                self._warm_up_pool()

        except Exception as e:
            self.logger.warning(f"⚠️ 连接池健康检查异常: {e}")

    def _warm_up_pool(self):
        """预热连接池"""
        try:
            # 获取并立即释放连接，触发连接池预热
            connections = []
            for i in range(2):  # 预热2个连接
                try:
                    conn = self.get_main_db_connection(timeout=3)
                    if conn:
                        connections.append(conn)
                except:
                    pass

            # 释放连接回池中
            for conn in connections:
                try:
                    conn.close()
                except:
                    pass

            self.logger.info(f"✅ 连接池预热完成，预热了 {len(connections)} 个连接")

        except Exception as e:
            self.logger.warning(f"⚠️ 连接池预热失败: {e}")

    def get_pool_status(self):
        """获取连接池状态"""
        if not self.main_db_pool:
            return {"status": "不存在", "available": 0, "total": 0}

        try:
            available = self.main_db_pool._cnx_queue.qsize()
            total = self.main_db_pool._pool_size
            return {
                "status": "正常",
                "available": available,
                "total": total,
                "usage": f"{total-available}/{total}"
            }
        except:
            return {"status": "异常", "available": "未知", "total": "未知"}
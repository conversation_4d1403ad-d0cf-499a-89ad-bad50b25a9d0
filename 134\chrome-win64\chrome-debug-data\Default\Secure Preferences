{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "在这里，您可以找到适用于 Chromium 的精彩应用、游戏、扩展程序和主题背景。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "应用商店", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Users\\<USER>\\Desktop\\pro\\freeAPI\\chrome-win64\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "mfhcmdonhekjhfbjmeacdjbhlfgpjabp": {"account_extension_type": 0, "ack_prompt_count": 1, "active_bit": false, "active_permissions": {"api": ["storage", "tabs", "unlimitedStorage", "webRequest", "declarativeNetRequest"], "explicit_host": ["*://*/*"], "manifest_permissions": [], "scriptable_host": ["<all_urls>"]}, "allowlist": 1, "commands": {}, "content_settings": [], "creation_flags": 9, "cws-info": {"is-live": true, "is-present": true, "last-updated-time-millis": "*************", "no-privacy-practice": false, "unpublished-long-ago": false, "violation-type": 0}, "disable_reasons": [8192], "external_first_run": true, "first_install_time": "*****************", "from_webstore": true, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 6, "manifest": {"action": {"default_icon": {"19": "img/icon19.png", "38": "img/icon38.png"}, "default_title": "Web Safety"}, "author": "Lavasoft", "background": {"service_worker": "background.js"}, "content_scripts": [{"js": ["js/browserDom.js"], "matches": ["<all_urls>"], "run_at": "document_start"}], "current_locale": "zh_CN", "default_locale": "en", "description": "Stay protected while searching and surfing the Web. Never worry about suspicious websites again.", "host_permissions": ["*://*/*"], "icons": {"128": "img/icon_128.png", "16": "img/icon_16.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlyrX9xQiVq8KAnu06GO4uicqdvHfmgl8t4K1/TJEhBUs0WcyGC9q1igxPinJADjcw3AraCBsam1sfLGKNXRXc7i77guJcLqo2dFI995kkHfrt5RsfUGPPN8gq/EpeMKr90GowK76wx/Re/U/XUhXjAWU+F2DV5F3b+HNfEvUGLtQUO5YwifszceFzApoP7cbFJvxrDny1lyzsEH45ci34apPRcm87xl2Bke/3G50Tvo8S+wHXCl8q2GspPxrYXzdHCIumVhR68+QiM9I0mss0yzpopUlBkvyZYjdfbWcAB0kDRGs5n8UGtdQPTn07dh0nzuOObU6igTs40rn4IisoQIDAQAB", "manifest_version": 3, "minimum_chrome_version": "121", "name": "Web Safety", "permissions": ["storage", "tabs", "webRequest", "unlimitedStorage", "declarativeNetRequest"], "short_name": "Web Safety", "update_url": "https://clients2.google.com/service/update2/crx", "version": "2.0.0", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["img/*"]}]}, "path": "mfhcmdonhekjhfbjmeacdjbhlfgpjabp\\2.0.0_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "state": 0, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chromium PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Users\\<USER>\\Desktop\\pro\\freeAPI\\chrome-win64\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "ncennffkjdiamlpmcbajkmaiiiddgioo": {"account_extension_type": 0, "ack_prompt_count": 2, "active_bit": false, "active_permissions": {"api": ["contextMenus", "cookies", "downloads", "nativeMessaging", "notifications", "storage", "tabs", "webRequest", "scripting"], "explicit_host": ["<all_urls>", "http://*/*", "https://*/*"], "manifest_permissions": [], "scriptable_host": ["ftp://*/*", "http://*/*", "https://*/*"]}, "allowlist": 1, "commands": {}, "content_settings": [], "creation_flags": 9, "cws-info": {"is-live": true, "is-present": true, "last-updated-time-millis": "*************", "no-privacy-practice": false, "unpublished-long-ago": false, "violation-type": 0}, "disable_reasons": [8192], "external_first_run": true, "first_install_time": "*****************", "from_webstore": true, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 3, "manifest": {"action": {"default_icon": "assets/icon19_normal.png", "default_popup": "popup.html", "default_title": "迅雷Chrome支持"}, "background": {"service_worker": "service-worker-loader.js", "type": "module"}, "content_scripts": [{"all_frames": true, "css": ["assets/content.css", "assets/Jsq-8833d7eb.css", "assets/content-673cd3f7.css"], "js": ["assets/content.js-loader-8297b8d7.js"], "matches": ["http://*/*", "https://*/*", "ftp://*/*"], "run_at": "document_start"}], "current_locale": "zh_CN", "default_locale": "zh_CN", "description": "迅雷下载支持", "differential_fingerprint": "1.04d1268da7abac85bcb5330cf6aadaf364e25fa52a3d234e06c3680d0009e8cb", "host_permissions": ["<all_urls>", "http://*/*", "https://*/*"], "icons": {"128": "assets/install_logo.png", "16": "assets/menu_logo.png", "48": "assets/extension_logo.png"}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDEa5DG04lhgzzm3gRSXPPOZOv6ZXnzQrBv+rjUE/dL5br9Duh1kbwGQJCO4QMDvD1usf6FoXDsuvZwYzH6lg1pLI7m/wmQC3NQURHQ7J5zAy7VY0F7qSVqclcpRKY2k00vcqxok6lota3Z1QxUVUwWc9VUfr4gRUeQa4KlEsXzGwIDAQAB", "manifest_version": 3, "name": "迅雷下载支持", "optional_permissions": [], "options_page": "options.html", "permissions": ["contextMenus", "cookies", "tabs", "webRequest", "downloads", "nativeMessaging", "storage", "scripting", "notifications"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "3.52.10", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["assets/*"], "use_dynamic_url": false}, {"matches": ["ftp://*/*", "http://*/*", "https://*/*"], "resources": ["assets/runtime-dom.esm-bundler-3c6fceb0.js", "assets/util-86a8139d.js", "assets/index-9000aff5.js", "assets/Jsq-b86ddcf2.js", "assets/stat-481b0a88.js", "assets/tool-13238bfa.js", "assets/content.js-610e9598.js"], "use_dynamic_url": false}]}, "path": "ncennffkjdiamlpmcbajkmaiiiddgioo\\3.52.10_0", "pending_on_installed_event_dispatch_info": {"previous_version": "3.52.5"}, "preferences": {}, "regular_only_preferences": {}, "state": 0, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "ngpampappnmepgilojfohadhhmbhlaek": {"account_extension_type": 0, "ack_prompt_count": 2, "active_bit": false, "active_permissions": {"api": ["contextMenus", "cookies", "downloads", "downloads.shelf", "management", "nativeMessaging", "proxy", "storage", "tabs", "webNavigation", "webRequest", "webRequestBlocking"], "explicit_host": ["<all_urls>", "chrome://favicon/*"], "manifest_permissions": [], "scriptable_host": ["file:///*", "ftp://*/*", "http://*/*", "https://*/*"]}, "allowlist": 1, "commands": {}, "content_settings": [], "creation_flags": 9, "cws-info": {"is-live": true, "is-present": true, "last-updated-time-millis": "*************", "no-privacy-practice": false, "unpublished-long-ago": false, "violation-type": 0}, "disable_reasons": [8192], "external_first_run": true, "first_install_time": "*****************", "from_webstore": true, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 3, "manifest": {"author": "Tonec FZE", "background": {"persistent": true, "scripts": ["background.js"]}, "browser_action": {"default_icon": {"16": "images/logo16.png", "32": "images/logo32.png"}}, "content_scripts": [{"all_frames": true, "js": ["content.js"], "matches": ["http://*/*", "https://*/*", "ftp://*/*", "file:///*"], "run_at": "document_start"}], "content_security_policy": "connect-src *; script-src 'self' 'sha256-3A6Y6ygbQdayC7L3d1LSwz60wQiRVT9GBErQTn6TwTo='; style-src 'unsafe-inline'; default-src 'self'", "current_locale": "zh_CN", "default_locale": "en", "description": "Download files with Internet Download Manager", "differential_fingerprint": "1.c3947c99da409218ff7c3a4a3cd4d4fa269c88c72bb5a32c2ae82c87eaa3c0ee", "externally_connectable": {"matches": ["*://*.internetdownloadmanager.com/*", "*://*.tonec.com/*"]}, "homepage_url": "http://www.internetdownloadmanager.com/", "icons": {"128": "images/logo128.png", "16": "images/logo16.png", "48": "images/logo48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAutN8/aVCnWWo01YitMu1kREcX+50UY//YxrPd0XXSLTK0mEJqPRYbcgxjxAfUc1+eHtFbWvR5BDQvip0+3chrMpGfG+Fwn/aQkaZj7T6gEqbHQElqoncCtXaDTRvo0M7Pj0RWjc1dTV+COlhHZI4RIw4TmL1SAvwO/1+VUgg4ohTjFNKhcSsz89kUbaPqfMfFk1UbrI3pkCHTUNCu0+doN7KQDbH1QBlYxt3ajsd15hzhca/dT4eDfew1nHRADOF5A4JNEuDHoyR/ETvn/CZgNeSo2lWNyYp/xpxRR6Fygjhjf4rNoRm/CtdCCF6mPNjbOkvIU8nQJQvvj08QZ3yFQIDAQAB", "manifest_version": 2, "minimum_chrome_version": "47.0", "name": "IDM Integration Module", "offline_enabled": false, "optional_permissions": ["notifications", "system.display"], "permissions": ["<all_urls>", "tabs", "cookies", "contextMenus", "webNavigation", "webRequest", "webRequestBlocking", "downloads", "downloads.shelf", "management", "storage", "proxy", "nativeMessaging"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "6.42.32", "web_accessible_resources": ["captured.html"]}, "path": "ngpampappnmepgilojfohadhhmbhlaek\\6.42.32_0", "pending_on_installed_event_dispatch_info": {"previous_version": "*********"}, "preferences": {}, "regular_only_preferences": {}, "state": 0, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}}}, "pinned_tabs": [], "protection": {"macs": {"browser": {"show_home_button": "4BEDB9C296DE5D3FC7772342F4C0D43738EE599DA172756D5C8E2F2CD6A62F36"}, "default_search_provider_data": {"template_url_data": "54C21AC49D63ECF04A6C0BA68340D23497D1C8C8E53408056B94327136D2426E"}, "enterprise_signin": {"policy_recovery_token": "3C67B01FE14C954A11671F6875573A26B0F3C5D0D767990BE8EEB5D098CF4010"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "8727F437E339EAB48BE386B435CDB2472E012D16A51740AFA5B345FD8D8AFD25", "mfhcmdonhekjhfbjmeacdjbhlfgpjabp": "CCB046FC70350672F51D9FFE3A39B0735917261F89600E46F46617BB894549AA", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "9CD6B42EBFEF942D7FEAB04B542D34C0B1885BB76FAFB3D071AA60546A4F3FDA", "ncennffkjdiamlpmcbajkmaiiiddgioo": "695855172E42A993FD600A9FA1309678C0DF63B8BD33D61E51AF826BEA96BB87", "ngpampappnmepgilojfohadhhmbhlaek": "ED3EDC75C7511CE960CE9BDFFE80BB51259DDE3E50D7740C46D4CF6629458E17"}, "ui": {"developer_mode": "EB63861C4398A9E6CC33A01EB1E27F6B44C320F8BD977FF13A97729160B9AB5B"}}, "google": {"services": {"account_id": "9739344241FFED5772ECF01B791D969E66836ABF6970EB59025B094A22C90673", "last_signed_in_username": "F3E130084B570512493248A56521B6B2E0D4FECDC639A39206E2333CE3AC765B", "last_username": "8771BDA01B5846AE74E3A1CA3372532ABAB82BEF62250B3D46D88598C61CA86D"}}, "homepage": "8AE29A121C0C3069073ABD963FA0B354567769D938F0068626791C1C93A7FFB8", "homepage_is_newtabpage": "A570B13A5FEA2F98AAB7E889A86EA84C415BF9BC1BAD8DA8097C797170A024B8", "media": {"cdm": {"origin_data": "C8F36F6A91164BD94F64B292A4F1759E54E980147881E77110DA4A86E5ABD433"}, "storage_id_salt": "C26833E433E4BD533709238C74CCEC3C28E51CE5718FD87AE59107A3879AC041"}, "pinned_tabs": "1134C1CB5368034A6E54107FC312BCC5F6DBF99478C4F44299B05FBB7A9744E6", "prefs": {"preference_reset_time": "F1151138D26A72B9446D6D30A48542594809DE0D757080B7E9DE5A462CE38CC2"}, "safebrowsing": {"incidents_sent": "907808926B5A1D3C1DE9DD23D5C93A8B870FC124739E683769552FC0D85F3349"}, "search_provider_overrides": "FDC6EC4D2DAF307282795A7113CF81A4FCB01705669CA42CA994C8B5972E9D3F", "session": {"restore_on_startup": "439023B6B9626E5F066E5611EF60F5395EFBC6251FDF85408CC3F63B781C85FF", "startup_urls": "3D60A3004AD5FD92DB961FEB42A23D981300BC25DB0C71858E1FBE06AFE82DA0"}}, "super_mac": "DA48E1CFF8964A9FB9E93334A7A5E3C7C5FF48455A01EB0A4BC0DD0FD2B56E58"}}
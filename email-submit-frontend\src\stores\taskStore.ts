/**
 * 任务状态管理 - 使用Zustand
 */
import { create } from 'zustand';
import { emailAPI, Task, TaskStatus, SystemStatus } from '../services/api';

interface TaskStore {
  // 状态
  tasks: Task[];
  currentTask: Task | null;
  systemStatus: SystemStatus | null;
  isSubmitting: boolean;
  isLoading: boolean;
  error: string | null;

  // 操作
  submitEmail: (email: string) => Promise<void>;
  updateTaskStatus: (taskId: string) => Promise<void>;
  refreshSystemStatus: () => Promise<void>;
  refreshQueue: () => Promise<void>;
  clearError: () => void;
  clearCurrentTask: () => void;

  // 轮询相关
  pollingInterval: NodeJS.Timeout | null;
  startPolling: (taskId: string) => void;
  stopPolling: () => void;
}

export const useTaskStore = create<TaskStore>((set, get) => ({
  // 初始状态
  tasks: [],
  currentTask: null,
  systemStatus: null,
  isSubmitting: false,
  isLoading: false,
  error: null,
  pollingInterval: null,

  // 提交邮箱
  submitEmail: async (email: string) => {
    set({ isSubmitting: true, error: null });

    try {
      const response = await emailAPI.submitEmail(email);

      if (response.success && response.task_id) {
        // 先获取真实的任务信息
        try {
          const realTask = await emailAPI.getTaskStatus(response.task_id.toString());
          set({
            currentTask: realTask,
            isSubmitting: false
          });
        } catch (error) {
          // 如果获取失败，使用临时任务对象
          const newTask: Task = {
            id: response.task_id.toString(),
            email: email,
            status: TaskStatus.PENDING,
            message: response.message,
            created_at: new Date().toISOString()
          };
          set({
            currentTask: newTask,
            isSubmitting: false
          });
        }

        // 开始轮询任务状态
        get().startPolling(response.task_id.toString());

        // 刷新队列
        get().refreshQueue();

      } else {
        throw new Error(response.error || '提交失败');
      }
    } catch (error: any) {
      set({
        error: error.message,
        isSubmitting: false
      });
    }
  },

  // 更新任务状态
  updateTaskStatus: async (taskId: string) => {
    try {
      console.log('正在查询任务状态:', taskId);
      const task = await emailAPI.getTaskStatus(taskId);
      console.log('任务状态查询结果:', task);

      set(state => {
        // 更新当前任务
        const newCurrentTask = state.currentTask?.id === taskId ? task : state.currentTask;

        // 更新任务列表中的对应任务
        const newTasks = state.tasks.map(t => {
          if (t.id === taskId || t.id.toString() === taskId) {
            return { ...task, id: taskId }; // 确保ID格式一致
          }
          return t;
        });

        // 如果任务不在列表中，添加到列表开头
        if (!newTasks.some(t => t.id === taskId || t.id.toString() === taskId)) {
          newTasks.unshift({ ...task, id: taskId });
        }

        return {
          currentTask: newCurrentTask,
          tasks: newTasks
        };
      });

      // 如果任务完成，停止轮询
      if (task.status === TaskStatus.SUCCESS || task.status === TaskStatus.FAILED) {
        get().stopPolling();
        // 移除自动刷新队列，避免状态冲突
        // 队列刷新由用户手动触发或页面刷新时进行
        console.log('任务完成，已停止轮询:', task.id, task.status);

        // 如果任务成功完成，刷新用户信息以获取最新次数
        if (task.status === TaskStatus.SUCCESS) {
          // 动态导入userStore以避免循环依赖
          import('./userStore').then(({ useUserStore }) => {
            const userStore = useUserStore.getState();
            if (userStore.isLoggedIn) {
              // 添加延迟和错误处理，避免因网络问题导致自动登出
              setTimeout(() => {
                userStore.refreshUserInfo().catch(error => {
                  console.warn('任务完成后刷新用户信息失败，但不影响登录状态:', error);
                  // 不做任何处理，保持用户登录状态
                });
              }, 1000); // 延迟1秒，确保后端状态已更新
            }
          }).catch(console.error);
        }
      }

    } catch (error: any) {
      console.error('更新任务状态失败:', error);

      // 如果是404错误（任务不存在），停止轮询
      if (error.message.includes('404') || error.message.includes('任务不存在')) {
        console.log('任务不存在，停止轮询');
        get().stopPolling();
      }
    }
  },

  // 刷新系统状态
  refreshSystemStatus: async () => {
    try {
      const status = await emailAPI.getSystemStatus();
      set({ systemStatus: status });
    } catch (error: any) {
      console.error('获取系统状态失败:', error);
    }
  },

  // 刷新任务队列
  refreshQueue: async () => {
    set({ isLoading: true });

    try {
      const queueData = await emailAPI.getQueue();

      if (queueData.success && queueData.queue && Array.isArray(queueData.queue)) {
        // 转换后端状态到前端状态
        const convertedTasks = queueData.queue.map(task => ({
          ...task,
          id: task.id.toString(),
          status: task.status === 'pending' ? TaskStatus.PENDING :
                  task.status === 'processing' ? TaskStatus.PROCESSING :
                  task.status === 'success' ? TaskStatus.SUCCESS :
                  task.status === 'failed' ? TaskStatus.FAILED : TaskStatus.PENDING
        }));

        // 获取当前状态
        const currentState = get();

        // 如果有正在轮询的任务，保持其状态不被队列数据覆盖
        let finalTasks = convertedTasks;
        if (currentState.currentTask && currentState.pollingInterval) {
          // 正在轮询中，不要用队列数据覆盖当前任务的状态
          finalTasks = convertedTasks.filter(task => task.id !== currentState.currentTask?.id);
          // 保持当前任务在列表中的最新状态
          if (currentState.currentTask) {
            finalTasks.unshift(currentState.currentTask);
          }
        }

        set({
          tasks: finalTasks,
          isLoading: false
        });
      } else {
        // 如果没有队列数据，设置为空数组
        set({
          tasks: [],
          isLoading: false
        });
      }
    } catch (error: any) {
      console.error('刷新任务队列失败:', error);

      // 如果是认证错误，清空任务列表
      if (error.message.includes('401') || error.message.includes('请先登录')) {
        set({
          tasks: [],
          isLoading: false,
          error: '请重新登录'
        });
      } else {
        // 对于网络错误，不清空现有任务，只记录错误
        const { tasks } = get();
        set({
          error: tasks.length > 0 ? null : error.message, // 如果有现有数据，不显示错误
          isLoading: false
        });
      }
    }
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },

  // 清除当前任务
  clearCurrentTask: () => {
    set({ currentTask: null });
    get().stopPolling();
  },

  // 开始轮询任务状态
  startPolling: (taskId: string) => {
    // 先停止之前的轮询
    get().stopPolling();

    // 开始新的轮询
    const interval = setInterval(() => {
      get().updateTaskStatus(taskId);
    }, 2000); // 每2秒查询一次

    set({ pollingInterval: interval });
  },

  // 停止轮询
  stopPolling: () => {
    const { pollingInterval } = get();
    if (pollingInterval) {
      clearInterval(pollingInterval);
      set({ pollingInterval: null });
    }
  }
}));

// 自定义Hook：用于组件中使用
export const useTaskActions = () => {
  const store = useTaskStore();

  return {
    submitEmail: store.submitEmail,
    refreshQueue: store.refreshQueue,
    refreshSystemStatus: store.refreshSystemStatus,
    clearError: store.clearError,
    clearCurrentTask: store.clearCurrentTask
  };
};

export const useTaskState = () => {
  const store = useTaskStore();

  return {
    tasks: store.tasks,
    currentTask: store.currentTask,
    systemStatus: store.systemStatus,
    isSubmitting: store.isSubmitting,
    isLoading: store.isLoading,
    error: store.error
  };
};

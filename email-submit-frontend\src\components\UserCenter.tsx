/**
 * 用户中心组件
 */
import React, { useState, useEffect } from 'react';
import { Card, Descriptions, Button, Modal, Form, Input, message, Space, Typography, Divider, Table, Tag, Tabs } from 'antd';
import { UserOutlined, CreditCardOutlined, GiftOutlined, LogoutOutlined, ShoppingOutlined, HistoryOutlined, ReloadOutlined } from '@ant-design/icons';
import { useUserState, useUserActions } from '../stores/userStore';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface UserSubmission {
  id: number;
  email: string;
  status: 'waiting' | 'processing' | 'completed' | 'failed';
  queue_position?: number;
  result_message?: string;
  submitted_at: string;
  started_at?: string;
  completed_at?: string;
}

const UserCenter: React.FC = () => {
  const { userInfo, isLoading, error } = useUserState();
  const { logout, recharge, clearError } = useUserActions();
  const [rechargeModalVisible, setRechargeModalVisible] = useState(false);
  const [rechargeForm] = Form.useForm();
  const [submissions, setSubmissions] = useState<UserSubmission[]>([]);
  const [submissionsLoading, setSubmissionsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('info');
  const [historyLoaded, setHistoryLoaded] = useState(false);

  // 添加CSS动画样式
  React.useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes pulse {
        0%, 100% { opacity: 0.3; }
        50% { opacity: 1; }
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      @keyframes fadeInOut {
        0%, 100% { opacity: 0.5; }
        50% { opacity: 1; }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // 获取用户提交历史
  const fetchUserSubmissions = async (forceRefresh = false) => {
    if (!userInfo) return;

    // 如果不是强制刷新且已经加载过，则不重复请求
    if (!forceRefresh && historyLoaded) return;

    setSubmissionsLoading(true);
    try {
      const token = localStorage.getItem('user-storage');
      if (!token) return;

      const userData = JSON.parse(token);
      const authToken = userData.state?.token;

      if (!authToken) return;

      // 使用配置文件中的API地址
      const { getApiBaseUrlSync } = await import('../config');
      const apiBaseUrl = getApiBaseUrlSync();
      const response = await fetch(`${apiBaseUrl}/user/submissions`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      if (data.success) {
        setSubmissions(data.data.submissions);
        setHistoryLoaded(true);
      } else {
        message.error('获取提交历史失败');
      }
    } catch (error) {
      console.error('获取提交历史失败:', error);
      message.error('获取提交历史失败');
    } finally {
      setSubmissionsLoading(false);
    }
  };

  // 只在切换到历史标签页且未加载过时获取数据
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    if (activeTab === 'history' && userInfo && !historyLoaded) {
      fetchUserSubmissions();
    }
  }, [activeTab, userInfo, historyLoaded]); // eslint-disable-line react-hooks/exhaustive-deps

  // 处理充值
  const handleRecharge = async (values: { activationCode: string }) => {
    try {
      // 显示充值进行中的提示
      const hideLoading = message.loading({
        content: '充值进行中，请稍等...',
        duration: 0, // 不自动关闭
        key: 'recharge-loading'
      });

      const success = await recharge(values.activationCode);

      // 关闭加载提示
      hideLoading();

      if (success) {
        message.success({
          content: '充值成功！次数已到账',
          duration: 3,
          key: 'recharge-success'
        });
        setRechargeModalVisible(false);
        rechargeForm.resetFields();
      } else {
        // 显示错误信息
        if (error) {
          message.error({
            content: error,
            duration: 4,
            key: 'recharge-error'
          });
        } else {
          message.error({
            content: '充值失败，请检查激活码是否正确',
            duration: 4,
            key: 'recharge-error'
          });
        }
      }
    } catch (err) {
      console.error('充值过程中发生错误:', err);
      message.error({
        content: '充值失败，请稍后重试',
        duration: 4,
        key: 'recharge-error'
      });
    }
  };

  // 处理登出
  const handleLogout = () => {
    Modal.confirm({
      title: '确认登出',
      content: '确定要退出登录吗？',
      onOk: () => {
        logout();
        message.success('已退出登录');
      },
    });
  };

  // 获取充值链接
  const handleBuyCode = async () => {
    try {
      // 使用配置文件中的API地址
      const { getApiBaseUrlSync } = await import('../config');
      const apiBaseUrl = getApiBaseUrlSync();
      const response = await fetch(`${apiBaseUrl}/config/recharge-url`);
      const data = await response.json();
      if (data.success) {
        window.open(data.url, '_blank');
      } else {
        message.error('获取购买链接失败');
      }
    } catch (error) {
      message.error('获取购买链接失败');
    }
  };

  // 状态标签渲染
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      waiting: { color: 'blue', text: '等待中' },
      processing: { color: 'orange', text: '处理中' },
      completed: { color: 'green', text: '已完成' },
      failed: { color: 'red', text: '失败' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns = [
    {
      title: '邮箱地址',
      dataIndex: 'email',
      key: 'email',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => renderStatusTag(status),
    },
    {
      title: '提交时间',
      dataIndex: 'submitted_at',
      key: 'submitted_at',
      render: (time: string) => time ? new Date(time).toLocaleString('zh-CN') : '-',
    },
    {
      title: '完成时间',
      dataIndex: 'completed_at',
      key: 'completed_at',
      render: (time: string) => time ? new Date(time).toLocaleString('zh-CN') : '-',
    },
    {
      title: '结果',
      dataIndex: 'result_message',
      key: 'result_message',
      ellipsis: true,
      render: (message: string) => {
        if (!message) return '-';

        // 清理消息内容，防止显示CSS代码
        const cleanMessage = (msg: string): string => {
          // 检查是否包含CSS/JS代码特征
          const codePatterns = [
            /@scope/,
            /font-weight.*!important/,
            /font-size.*!important/,
            /\{[\s\S]*\}/,
            /\/\*[\s\S]*\*\//
          ];

          if (codePatterns.some(pattern => pattern.test(msg))) {
            // 如果包含代码，尝试提取有用信息
            if (msg.includes('成功添加成员')) {
              return '成员添加成功';
            } else if (msg.includes('添加失败')) {
              return '成员添加失败';
            } else {
              return '操作已完成';
            }
          }

          // 限制长度
          return msg.length > 50 ? msg.substring(0, 50) + '...' : msg;
        };

        return cleanMessage(message);
      },
    },
  ];

  if (!userInfo) {
    return null;
  }

  return (
    <>
      <Card
        title={
          <Space>
            <UserOutlined />
            用户中心
          </Space>
        }
        extra={
          <Button
            type="text"
            danger
            icon={<LogoutOutlined />}
            onClick={handleLogout}
          >
            退出登录
          </Button>
        }
        style={{ width: '100%', maxWidth: 800 }}
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="用户信息" key="info">
            {/* 用户基本信息 */}
            <Descriptions column={1} bordered>
          <Descriptions.Item label="用户名">
            <Text strong>{userInfo.username}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="激活剩余次数">
            <Space>
              <Text strong style={{ color: userInfo.time_quota > 0 ? '#52c41a' : '#ff4d4f', fontSize: 16 }}>
                {userInfo.time_quota} 次
              </Text>
              {userInfo.time_quota <= 0 && (
                <Text type="secondary" style={{ fontSize: 12 }}>
                  (次数不足，请充值)
                </Text>
              )}
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label="已使用次数">
            <Text>{userInfo.time_count} 次</Text>
          </Descriptions.Item>
          <Descriptions.Item label="注册时间">
            <Text>{userInfo.created_at}</Text>
          </Descriptions.Item>
          {userInfo.lastusetime && (
            <Descriptions.Item label="最后使用时间">
              <Text>{userInfo.lastusetime}</Text>
            </Descriptions.Item>
          )}
        </Descriptions>

        <Divider />

        {/* 操作按钮 */}
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          <div style={{ textAlign: 'center' }}>
            <Title level={4} style={{ margin: 0 }}>
              <GiftOutlined style={{ color: '#1890ff' }} /> 充值服务
            </Title>
            <Text type="secondary">使用激活码增加使用次数</Text>
          </div>

          <Space style={{ width: '100%', justifyContent: 'center' }} size="large">
            <Button
              type="primary"
              icon={<CreditCardOutlined />}
              size="large"
              onClick={() => setRechargeModalVisible(true)}
            >
              激活码充值
            </Button>

            <Button
              icon={<ShoppingOutlined />}
              size="large"
              onClick={handleBuyCode}
            >
              购买激活码
            </Button>
          </Space>

          <div style={{
            textAlign: 'center',
            padding: 16,
            background: '#f8f9fa',
            borderRadius: 8
          }}>
            <Text type="secondary" style={{ fontSize: 12 }}>
              💡 激活码可以增加您的使用次数<br />
              🔄 每次成功激活账号会消耗1次使用次数<br />
              📞 如有问题请联系客服
            </Text>
          </div>
        </Space>
          </TabPane>

          <TabPane tab={<span><HistoryOutlined />提交历史</span>} key="history">
            <div style={{ marginBottom: 16, textAlign: 'right' }}>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchUserSubmissions(true)}
                loading={submissionsLoading}
                size="small"
              >
                刷新数据
              </Button>
            </div>

            <Table
              columns={columns}
              dataSource={submissions}
              loading={submissionsLoading}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: false,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
              scroll={{ x: 600 }}
              locale={{
                emptyText: '暂无提交记录'
              }}
            />

            <div style={{
              marginTop: 16,
              padding: 12,
              background: '#f0f8ff',
              borderRadius: 6,
              border: '1px solid #d6e4ff'
            }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                💡 <strong>说明：</strong><br />
                • 这里显示您的所有邮箱提交记录<br />
                • 可以查看每次提交的状态和结果<br />
                • 数据实时更新，刷新页面获取最新状态
              </Text>
            </div>
          </TabPane>
        </Tabs>
      </Card>

      {/* 充值弹窗 */}
      <Modal
        title="激活码充值"
        open={rechargeModalVisible}
        onCancel={() => {
          setRechargeModalVisible(false);
          rechargeForm.resetFields();
          clearError();
        }}
        footer={null}
        width={400}
      >
        {/* 加载状态提示 */}
        {isLoading && (
          <div style={{
            marginBottom: 16,
            padding: 12,
            background: '#e6f7ff',
            borderRadius: 6,
            border: '1px solid #91d5ff',
            textAlign: 'center'
          }}>
            <Space>
              <div style={{
                width: 16,
                height: 16,
                border: '2px solid #1890ff',
                borderTop: '2px solid transparent',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                display: 'inline-block'
              }} />
              <Text style={{ color: '#1890ff', fontWeight: 500 }}>
                正在处理充值请求，请稍等...
              </Text>
            </Space>
          </div>
        )}

        {/* 错误提示 */}
        {error && !isLoading && (
          <div style={{ marginBottom: 16 }}>
            <Text type="danger">{error}</Text>
          </div>
        )}

        <Form
          form={rechargeForm}
          onFinish={handleRecharge}
          layout="vertical"
          disabled={isLoading}
        >
          <Form.Item
            name="activationCode"
            label="激活码"
            rules={[
              { required: true, message: '请输入激活码' }
            ]}
          >
            <Input
              placeholder={isLoading ? "充值进行中..." : "请输入激活码"}
              size="large"
              disabled={isLoading}
              style={{
                backgroundColor: isLoading ? '#f5f5f5' : undefined,
                cursor: isLoading ? 'not-allowed' : undefined
              }}
            />
          </Form.Item>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'space-between' }}>
              <Button
                onClick={handleBuyCode}
                disabled={isLoading}
                style={{
                  opacity: isLoading ? 0.6 : 1,
                  cursor: isLoading ? 'not-allowed' : 'pointer'
                }}
              >
                购买激活码
              </Button>
              <Space>
                <Button
                  onClick={() => {
                    setRechargeModalVisible(false);
                    rechargeForm.resetFields();
                    clearError();
                  }}
                  disabled={isLoading}
                  style={{
                    opacity: isLoading ? 0.6 : 1,
                    cursor: isLoading ? 'not-allowed' : 'pointer'
                  }}
                >
                  {isLoading ? '充值中...' : '取消'}
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={isLoading}
                  disabled={isLoading}
                  style={{
                    background: isLoading ? '#1890ff' : undefined,
                    borderColor: isLoading ? '#1890ff' : undefined,
                  }}
                >
                  {isLoading ? (
                    <Space>
                      <span>充值进行中</span>
                      <span style={{
                        animation: 'pulse 1.5s ease-in-out infinite',
                        fontSize: '12px'
                      }}>
                        ●●●
                      </span>
                    </Space>
                  ) : (
                    '立即充值'
                  )}
                </Button>
              </Space>
            </Space>
          </Form.Item>
        </Form>

        <div style={{
          marginTop: 16,
          padding: 12,
          background: isLoading ? '#fff7e6' : '#f0f8ff',
          borderRadius: 6,
          border: isLoading ? '1px solid #ffd591' : '1px solid #d6e4ff',
          transition: 'all 0.3s ease'
        }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {isLoading ? (
              <>
                ⏳ <strong>充值进行中：</strong><br />
                • 正在验证激活码有效性<br />
                • 正在更新账户余额<br />
                • 请耐心等待，不要关闭页面
              </>
            ) : (
              <>
                💡 <strong>充值说明：</strong><br />
                • 输入有效的激活码即可增加使用次数<br />
                • 每个激活码只能使用一次<br />
                • 充值成功后次数立即到账
              </>
            )}
          </Text>
        </div>
      </Modal>
    </>
  );
};

export default UserCenter;

"""
配置使用统计跟踪器
使用Excel表格记录配置使用情况
"""
import os
import time
from datetime import datetime
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, PatternFill, Alignment
from typing import Dict, List, Optional

class ProfileUsageTracker:
    """配置使用统计跟踪器"""

    def __init__(self, excel_file: str = "profile_usage.xlsx"):
        """初始化跟踪器"""
        self.excel_file = excel_file
        self.init_excel_file()

    def init_excel_file(self):
        """初始化Excel文件"""
        if not os.path.exists(self.excel_file):
            # 创建新的Excel文件
            wb = Workbook()
            ws = wb.active
            ws.title = "配置使用记录"

            # 设置表头（添加使用邮箱列）
            headers = ["配置名称", "状态", "最后使用时间", "使用次数", "使用邮箱", "创建时间", "备注"]
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal="center")

            # 设置列宽
            ws.column_dimensions['A'].width = 15  # 配置名称
            ws.column_dimensions['B'].width = 10  # 状态
            ws.column_dimensions['C'].width = 20  # 最后使用时间
            ws.column_dimensions['D'].width = 10  # 使用次数
            ws.column_dimensions['E'].width = 25  # 使用邮箱
            ws.column_dimensions['F'].width = 20  # 创建时间
            ws.column_dimensions['G'].width = 30  # 备注

            wb.save(self.excel_file)
            print(f"✅ 创建配置使用统计表: {self.excel_file}")
        else:
            # 检查现有文件是否需要升级（添加使用邮箱列）
            self._upgrade_excel_structure()

    def _upgrade_excel_structure(self):
        """升级Excel文件结构，添加使用邮箱列"""
        try:
            wb = load_workbook(self.excel_file)
            ws = wb.active

            # 首先检查表头是否完整
            self._ensure_headers_exist(ws)

            # 检查是否已有使用邮箱列（第5列）
            if ws.max_column >= 5:
                header_e = ws.cell(row=1, column=5).value
                if header_e == "使用邮箱":
                    # 已经升级过了
                    return

            print(f"🔄 升级Excel文件结构，添加使用邮箱列...")

            # 如果只有6列（旧格式），需要插入使用邮箱列
            if ws.max_column == 6:
                # 在第5列（创建时间前）插入使用邮箱列
                ws.insert_cols(5)

                # 设置新列的表头
                header_cell = ws.cell(row=1, column=5, value="使用邮箱")
                header_cell.font = Font(bold=True, color="FFFFFF")
                header_cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                header_cell.alignment = Alignment(horizontal="center")

                # 设置列宽
                ws.column_dimensions['E'].width = 25  # 使用邮箱

                # 为现有数据行添加空的使用邮箱列
                for row in range(2, ws.max_row + 1):
                    ws.cell(row=row, column=5, value="")  # 使用邮箱列初始为空

                # 再次确保所有表头都正确
                self._ensure_headers_exist(ws)

                wb.save(self.excel_file)
                print(f"✅ Excel文件结构升级完成")

        except Exception as e:
            print(f"❌ 升级Excel文件结构失败: {e}")

    def _ensure_headers_exist(self, ws):
        """确保表头存在且正确"""
        try:
            expected_headers = ["配置名称", "状态", "最后使用时间", "使用次数", "使用邮箱", "创建时间", "备注"]

            # 检查第一行是否为空或不完整
            headers_missing = False
            for col in range(1, len(expected_headers) + 1):
                current_header = ws.cell(row=1, column=col).value
                if current_header != expected_headers[col-1]:
                    headers_missing = True
                    break

            if headers_missing:
                print(f"🔧 修复缺失的表头...")

                # 设置正确的表头
                for col, header in enumerate(expected_headers, 1):
                    cell = ws.cell(row=1, column=col, value=header)
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center")

                # 设置列宽
                ws.column_dimensions['A'].width = 15  # 配置名称
                ws.column_dimensions['B'].width = 10  # 状态
                ws.column_dimensions['C'].width = 20  # 最后使用时间
                ws.column_dimensions['D'].width = 10  # 使用次数
                ws.column_dimensions['E'].width = 25  # 使用邮箱
                ws.column_dimensions['F'].width = 20  # 创建时间
                ws.column_dimensions['G'].width = 30  # 备注

                print(f"✅ 表头修复完成")

        except Exception as e:
            print(f"❌ 确保表头存在失败: {e}")

    def add_profile(self, profile_name: str, profile_path: str = None):
        """添加新配置到统计表"""
        try:
            wb = load_workbook(self.excel_file)
            ws = wb.active

            # 检查配置是否已存在
            for row in range(2, ws.max_row + 1):
                if ws.cell(row=row, column=1).value == profile_name:
                    # 如果配置已存在但状态是"已移动"，则恢复为"未使用"
                    current_status = ws.cell(row=row, column=2).value
                    if current_status == "已移动":
                        ws.cell(row=row, column=2, value="未使用")
                        ws.cell(row=row, column=6, value=f"路径: {profile_path or 'N/A'}")

                        # 设置状态颜色为绿色
                        status_cell = ws.cell(row=row, column=2)
                        status_cell.fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")

                        wb.save(self.excel_file)
                        print(f"✅ 恢复配置状态: {profile_name}")
                    else:
                        print(f"配置 {profile_name} 已存在于统计表中")
                    return

            # 添加新行
            new_row = ws.max_row + 1
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            ws.cell(row=new_row, column=1, value=profile_name)  # 配置名称
            ws.cell(row=new_row, column=2, value="未使用")       # 状态
            ws.cell(row=new_row, column=3, value="")           # 最后使用时间
            ws.cell(row=new_row, column=4, value=0)            # 使用次数
            ws.cell(row=new_row, column=5, value="")           # 使用邮箱
            ws.cell(row=new_row, column=6, value=current_time) # 创建时间
            ws.cell(row=new_row, column=7, value=f"路径: {profile_path or 'N/A'}")  # 备注

            # 设置状态颜色
            status_cell = ws.cell(row=new_row, column=2)
            status_cell.fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")

            wb.save(self.excel_file)
            print(f"✅ 添加配置到统计表: {profile_name}")

        except Exception as e:
            print(f"❌ 添加配置失败: {e}")

    def mark_profile_used(self, profile_name: str, task_email: str = None):
        """标记配置为已使用"""
        try:
            wb = load_workbook(self.excel_file)
            ws = wb.active

            # 查找配置行
            for row in range(2, ws.max_row + 1):
                if ws.cell(row=row, column=1).value == profile_name:
                    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    current_count = ws.cell(row=row, column=4).value or 0

                    # 更新数据
                    ws.cell(row=row, column=2, value="已使用")           # 状态
                    ws.cell(row=row, column=3, value=current_time)      # 最后使用时间
                    ws.cell(row=row, column=4, value=current_count + 1) # 使用次数
                    ws.cell(row=row, column=5, value=task_email or "")  # 使用邮箱

                    # 更新备注
                    if task_email:
                        ws.cell(row=row, column=7, value=f"最后处理: {task_email}")
                    else:
                        # 保持原有备注或设置默认备注
                        current_remark = ws.cell(row=row, column=7).value
                        if not current_remark or current_remark.startswith("路径:"):
                            ws.cell(row=row, column=7, value="已使用但未记录邮箱")

                    # 设置状态颜色为红色
                    status_cell = ws.cell(row=row, column=2)
                    status_cell.fill = PatternFill(start_color="FFB6C1", end_color="FFB6C1", fill_type="solid")

                    wb.save(self.excel_file)
                    print(f"✅ 标记配置已使用: {profile_name}")
                    return True

            print(f"⚠️ 未找到配置: {profile_name}")
            return False

        except Exception as e:
            print(f"❌ 标记配置使用失败: {e}")
            return False

    def get_next_unused_profile(self) -> Optional[str]:
        """获取下一个未使用的配置"""
        try:
            wb = load_workbook(self.excel_file)
            ws = wb.active

            # 查找未使用的配置
            for row in range(2, ws.max_row + 1):
                profile_name = ws.cell(row=row, column=1).value
                status = ws.cell(row=row, column=2).value

                if profile_name and status == "未使用":
                    print(f"🔄 选择下一个配置: {profile_name}")
                    return profile_name

            print("⚠️ 没有未使用的配置")
            return None

        except Exception as e:
            print(f"❌ 获取下一个配置失败: {e}")
            return None

    def get_usage_stats(self) -> Dict:
        """获取使用统计"""
        try:
            wb = load_workbook(self.excel_file)
            ws = wb.active

            total_count = 0
            used_count = 0
            unused_count = 0
            profiles = []

            for row in range(2, ws.max_row + 1):
                profile_name = ws.cell(row=row, column=1).value
                status = ws.cell(row=row, column=2).value
                last_used = ws.cell(row=row, column=3).value
                use_count = ws.cell(row=row, column=4).value or 0
                used_email = ws.cell(row=row, column=5).value or ""  # 使用邮箱

                if profile_name:
                    total_count += 1
                    if status == "已使用":
                        used_count += 1
                    elif status == "未使用":
                        unused_count += 1
                    # 其他状态（已删除、已移动等）不计入used_count或unused_count

                    profiles.append({
                        'name': profile_name,
                        'status': status,
                        'last_used': last_used,
                        'use_count': use_count,
                        'used_email': used_email
                    })

            return {
                'total_count': total_count,
                'used_count': used_count,
                'unused_count': unused_count,
                'profiles': profiles
            }

        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")
            return {
                'total_count': 0,
                'used_count': 0,
                'unused_count': 0,
                'profiles': []
            }

    def reset_all_profiles(self):
        """重置所有配置为未使用状态"""
        try:
            wb = load_workbook(self.excel_file)
            ws = wb.active

            reset_count = 0
            for row in range(2, ws.max_row + 1):
                profile_name = ws.cell(row=row, column=1).value
                if profile_name:
                    ws.cell(row=row, column=2, value="未使用")  # 状态
                    ws.cell(row=row, column=3, value="")       # 清空最后使用时间
                    ws.cell(row=row, column=4, value=0)        # 重置使用次数
                    ws.cell(row=row, column=5, value="")       # 清空使用邮箱

                    # 设置状态颜色为绿色
                    status_cell = ws.cell(row=row, column=2)
                    status_cell.fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")

                    reset_count += 1

            wb.save(self.excel_file)
            print(f"✅ 重置了 {reset_count} 个配置为未使用状态")
            return reset_count

        except Exception as e:
            print(f"❌ 重置配置失败: {e}")
            return 0

    def sync_with_config_manager(self, config_manager):
        """与配置管理器同步（包括old文件夹中的配置）"""
        try:
            # 获取活跃配置
            active_profiles = config_manager.get_all_profiles()

            # 获取old文件夹中的配置
            old_profiles = set()
            if hasattr(config_manager, 'old_profiles_dir') and os.path.exists(config_manager.old_profiles_dir):
                old_items = os.listdir(config_manager.old_profiles_dir)
                old_profiles = {item for item in old_items
                              if os.path.isdir(os.path.join(config_manager.old_profiles_dir, item))}

            # 获取Excel中现有的配置
            wb = load_workbook(self.excel_file)
            ws = wb.active

            excel_profiles = {}
            for row in range(2, ws.max_row + 1):
                profile_name = ws.cell(row=row, column=1).value
                if profile_name:
                    status = ws.cell(row=row, column=2).value
                    excel_profiles[profile_name] = status

            # 1. 添加/更新活跃配置
            for profile_name, profile_info in active_profiles.items():
                self.add_profile(profile_name, profile_info.get('path'))

            # 2. 标记old文件夹中的配置为"已使用"
            for old_profile in old_profiles:
                if old_profile in excel_profiles:
                    current_status = excel_profiles[old_profile]
                    if current_status != "已使用":
                        self._update_profile_status(old_profile, "已使用", "配置已移动到old文件夹")
                else:
                    # 添加新的已使用配置
                    self._add_used_profile(old_profile)

            # 3. 处理不存在的配置
            all_known_profiles = set(active_profiles.keys()) | old_profiles
            for excel_profile in excel_profiles:
                if excel_profile not in all_known_profiles:
                    # 这个配置既不在活跃列表也不在old文件夹中，标记为已删除
                    self._update_profile_status(excel_profile, "已删除", "配置文件已被删除")

            print(f"✅ 同步了 {len(active_profiles)} 个活跃配置和 {len(old_profiles)} 个已使用配置到Excel统计表")

        except Exception as e:
            print(f"❌ 同步配置失败: {e}")

    def _update_profile_status(self, profile_name: str, status: str, remark: str = None):
        """更新配置状态"""
        try:
            wb = load_workbook(self.excel_file)
            ws = wb.active

            for row in range(2, ws.max_row + 1):
                if ws.cell(row=row, column=1).value == profile_name:
                    ws.cell(row=row, column=2, value=status)
                    if remark:
                        ws.cell(row=row, column=6, value=remark)

                    # 设置状态颜色
                    status_cell = ws.cell(row=row, column=2)
                    if status == "未使用":
                        status_cell.fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")
                    elif status == "已使用":
                        status_cell.fill = PatternFill(start_color="FFB6C1", end_color="FFB6C1", fill_type="solid")
                    elif status == "已删除":
                        status_cell.fill = PatternFill(start_color="D3D3D3", end_color="D3D3D3", fill_type="solid")

                    wb.save(self.excel_file)
                    print(f"✅ 更新配置状态: {profile_name} -> {status}")
                    break

        except Exception as e:
            print(f"❌ 更新配置状态失败: {e}")

    def _add_used_profile(self, profile_name: str):
        """添加已使用的配置"""
        try:
            wb = load_workbook(self.excel_file)
            ws = wb.active

            # 检查是否已存在
            for row in range(2, ws.max_row + 1):
                if ws.cell(row=row, column=1).value == profile_name:
                    return  # 已存在，不重复添加

            # 添加新行
            new_row = ws.max_row + 1
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            ws.cell(row=new_row, column=1, value=profile_name)
            ws.cell(row=new_row, column=2, value="已使用")
            ws.cell(row=new_row, column=3, value=current_time)
            ws.cell(row=new_row, column=4, value=1)
            ws.cell(row=new_row, column=5, value="未知")
            ws.cell(row=new_row, column=6, value="从old文件夹发现的已使用配置")

            # 设置状态颜色
            status_cell = ws.cell(row=new_row, column=2)
            status_cell.fill = PatternFill(start_color="FFB6C1", end_color="FFB6C1", fill_type="solid")

            wb.save(self.excel_file)
            print(f"✅ 添加已使用配置: {profile_name}")

        except Exception as e:
            print(f"❌ 添加已使用配置失败: {e}")

    def _mark_profile_moved(self, profile_name: str):
        """标记配置为已移动（内部方法）"""
        try:
            wb = load_workbook(self.excel_file)
            ws = wb.active

            # 查找配置行
            for row in range(2, ws.max_row + 1):
                if ws.cell(row=row, column=1).value == profile_name:
                    current_status = ws.cell(row=row, column=2).value

                    # 只有当状态不是"已使用"时才标记为"已移动"
                    if current_status != "已使用":
                        ws.cell(row=row, column=2, value="已移动")
                        ws.cell(row=row, column=6, value="配置已移动到old文件夹")

                        # 设置状态颜色为灰色
                        status_cell = ws.cell(row=row, column=2)
                        status_cell.fill = PatternFill(start_color="D3D3D3", end_color="D3D3D3", fill_type="solid")

                        wb.save(self.excel_file)
                        print(f"✅ 标记配置已移动: {profile_name}")
                    break

        except Exception as e:
            print(f"❌ 标记配置移动失败: {e}")

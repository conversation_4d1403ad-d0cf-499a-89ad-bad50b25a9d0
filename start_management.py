#!/usr/bin/env python3
"""
管理界面启动脚本
用于启动GUI管理界面进行账号和配置管理
"""
import os
import sys
import time
import subprocess
import requests
import socket
from datetime import datetime

def check_port_available(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('127.0.0.1', port))
            return True
    except OSError:
        return False

def check_production_server():
    """检查生产服务器是否运行"""
    try:
        response = requests.get('http://localhost:5001/api/test', timeout=3)
        if response.status_code == 200:
            data = response.json()
            return data.get('success', False)
    except:
        pass
    return False

def show_system_status():
    """显示系统状态"""
    print("\n" + "="*60)
    print("📊 系统状态检查")
    print("="*60)
    
    # 检查端口状态
    port_5001_used = not check_port_available(5001)
    port_5002_used = not check_port_available(5002)
    
    print(f"🔌 端口 5001 (生产服务器): {'🟢 运行中' if port_5001_used else '🔴 未使用'}")
    print(f"🔌 端口 5002 (管理界面):   {'🟢 运行中' if port_5002_used else '🔴 未使用'}")
    
    # 检查生产服务器API
    production_ok = check_production_server()
    print(f"🌐 生产服务器API: {'✅ 正常' if production_ok else '❌ 异常'}")
    
    # 检查关键文件
    key_files = ['autoback.py', 'config.json', 'tasks.db']
    print("\n📁 关键文件检查:")
    for file in key_files:
        exists = os.path.exists(file)
        print(f"  {file}: {'✅ 存在' if exists else '❌ 缺失'}")
    
    # 检查浏览器配置
    profiles_dir = 'browser_profiles'
    if os.path.exists(profiles_dir):
        profiles = [d for d in os.listdir(profiles_dir) 
                   if os.path.isdir(os.path.join(profiles_dir, d)) and d != '__pycache__']
        print(f"\n🌐 浏览器配置: {len(profiles)} 个配置文件")
        if profiles:
            print("  配置列表:")
            for profile in profiles[:5]:  # 只显示前5个
                print(f"    • {profile}")
            if len(profiles) > 5:
                print(f"    • ... 还有 {len(profiles) - 5} 个配置")
    else:
        print("\n🌐 浏览器配置: ❌ 配置目录不存在")
    
    print("="*60)

def check_dependencies():
    """检查GUI依赖"""
    print("🔍 检查GUI依赖...")
    
    try:
        import tkinter
        print("✅ Tkinter可用")
    except ImportError:
        print("❌ Tkinter不可用，请安装Python Tkinter支持")
        return False
    
    # 检查必要的模块文件
    required_modules = [
        'autoback.py',
        'config_manager.py', 
        'browser_manager.py',
        'automation_engine.py',
        'profile_usage_tracker.py',
        'auth_manager.py'
    ]
    
    missing = []
    for module in required_modules:
        if not os.path.exists(module):
            missing.append(module)
    
    if missing:
        print(f"❌ 缺少模块: {', '.join(missing)}")
        return False
    
    print("✅ 所有依赖检查通过")
    return True

def handle_port_conflict():
    """处理端口冲突"""
    if not check_port_available(5002):
        print("⚠️ 端口 5002 已被占用")
        print("💡 可能的解决方案:")
        print("  1. 关闭占用端口的程序")
        print("  2. 使用不同的端口")
        print("  3. 检查是否已有管理界面在运行")
        
        choice = input("\n是否继续启动? (y/n): ").lower().strip()
        return choice == 'y'
    return True

def show_usage_tips():
    """显示使用提示"""
    print("\n💡 管理界面使用提示:")
    print("="*60)
    print("🔧 主要功能:")
    print("  • 用户账号管理 - 添加/编辑/删除用户")
    print("  • 激活码管理 - 充值/查看激活码状态")
    print("  • 浏览器配置 - 管理浏览器配置文件")
    print("  • 系统监控 - 查看任务状态和统计")
    print("  • 配置管理 - 修改系统配置参数")
    
    print("\n⚠️ 注意事项:")
    print("  • 管理界面按需启动，用完即关")
    print("  • 不要同时运行多个管理界面")
    print("  • 重要操作前请备份数据")
    print("  • 生产服务器应保持持续运行")
    
    print("\n🔗 相关命令:")
    print("  • 查看状态: python start_management.py --status")
    print("  • 启动生产服务器: python start_production.py")
    print("="*60)

def main():
    """主函数"""
    print("🖥️ 管理界面启动器")
    print("="*60)
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，请解决后重试")
        return
    
    # 显示系统状态
    show_system_status()
    
    # 检查生产服务器
    if not check_production_server():
        print("\n⚠️ 生产服务器未运行或异常")
        print("💡 建议先启动生产服务器: python start_production.py")
        
        choice = input("\n是否仍要启动管理界面? (y/n): ").lower().strip()
        if choice != 'y':
            print("❌ 用户取消启动")
            return
    else:
        print("\n✅ 生产服务器运行正常")
    
    # 处理端口冲突
    if not handle_port_conflict():
        print("❌ 端口冲突，启动取消")
        return
    
    # 显示使用提示
    show_usage_tips()
    
    print("\n🚀 启动管理界面...")
    print("💡 提示: 关闭GUI窗口即可停止管理界面")
    print("="*60)
    
    try:
        # 启动管理界面
        result = subprocess.run([sys.executable, 'autoback.py'], 
                              capture_output=False)
        
        if result.returncode == 0:
            print("\n✅ 管理界面正常关闭")
        else:
            print(f"\n⚠️ 管理界面异常退出 (代码: {result.returncode})")
            
    except KeyboardInterrupt:
        print("\n🔄 用户中断")
    except Exception as e:
        print(f"\n❌ 启动异常: {e}")
    
    print("🔚 管理界面已关闭")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--status':
        show_system_status()
    else:
        main()

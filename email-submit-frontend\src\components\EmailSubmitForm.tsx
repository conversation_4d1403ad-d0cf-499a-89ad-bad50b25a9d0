/**
 * 邮箱提交表单组件 - 支持用户认证
 */
import React, { useState } from 'react';
import { Form, Input, Button, Card, Alert, Space, Typography } from 'antd';
import { MailOutlined, SendOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useTaskActions, useTaskState } from '../stores/taskStore';
import { useUserState, useUserActions } from '../stores/userStore';
import { useSystemState } from '../stores/systemStore';
import { validateEmail } from '../services/api';

const { Text } = Typography;

const EmailSubmitForm: React.FC = () => {
  const [form] = Form.useForm();
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');

  const { submitEmail, clearError } = useTaskActions();
  const { isSubmitting, error } = useTaskState();
  const { userInfo } = useUserState();
  const { refreshUserInfo } = useUserActions();
  const { systemStatus } = useSystemState();

  // 邮箱格式验证
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmail(value);

    if (value && !validateEmail(value)) {
      setEmailError('请输入有效的augment账号');
    } else {
      setEmailError('');
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    // 首先检查用户是否有剩余次数
    if (!userInfo || userInfo.time_quota <= 0) {
      setEmailError('您的激活剩余次数不足，无法提交新的激活请求');
      return;
    }

    // 检查系统是否有可用配置（只在有系统状态数据时检查）
    if (systemStatus && systemStatus.available_profiles !== undefined && systemStatus.available_profiles <= 0) {
      setEmailError('系统激活额度不足，请等待管理员更新额度');
      return;
    }

    if (!email) {
      setEmailError('请输入augment账号');
      return;
    }

    if (!validateEmail(email)) {
      setEmailError('邮箱格式无效');
      return;
    }

    // 清除之前的错误
    clearError();
    setEmailError('');

    try {
      await submitEmail(email);
      // 提交成功后清空表单
      setEmail('');
      form.resetFields();
      // 刷新用户信息以获取最新次数（由后端在任务完成时扣减）
      refreshUserInfo();
    } catch (error) {
      // 错误已经在store中处理
    }
  };



  // 检查是否有剩余次数
  const hasQuota = userInfo && userInfo.time_quota > 0;

  // 检查系统是否有可用配置
  const hasAvailableProfiles = systemStatus && systemStatus.available_profiles > 0;

  // 综合检查是否可以提交
  const canSubmit = hasQuota && hasAvailableProfiles;

  return (
    <Card
      title={
        <Space>
          <MailOutlined />
          账号激活
        </Space>
      }
      style={{ width: '100%' }}
    >
      {/* 系统配置不足警告 */}
      {!hasAvailableProfiles && (
        <Alert
          message="激活额度不足"
          description={
            <Space direction="vertical" size="small">
              <Text>系统当前激活额度不足，暂时无法处理新的激活请求</Text>
              <Text type="secondary">请等待管理员更新额度后再试</Text>
            </Space>
          }
          type="error"
          icon={<ExclamationCircleOutlined />}
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 次数不足警告 */}
      {hasAvailableProfiles && !hasQuota && (
        <Alert
          message="次数不足"
          description={
            <Space direction="vertical" size="small">
              <Text>您的激活剩余次数不足，无法提交新的激活请求</Text>
              <Text type="secondary">请前往用户中心充值获取更多次数</Text>
            </Space>
          }
          type="warning"
          icon={<ExclamationCircleOutlined />}
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 错误提示 */}
      {error && (
        <Alert
          message="提交失败"
          description={error}
          type="error"
          closable
          onClose={clearError}
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 表单 */}
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Form.Item
          label="输入augment的账号"
          validateStatus={emailError ? 'error' : ''}
          help={emailError}
        >
          <Input
            prefix={<MailOutlined />}
            placeholder="请输入augment的账号"
            value={email}
            onChange={handleEmailChange}
            onPressEnter={handleSubmit}
            size="large"
            disabled={isSubmitting}
          />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            icon={<SendOutlined />}
            loading={isSubmitting}
            onClick={handleSubmit}
            disabled={!email || !!emailError || !canSubmit || isSubmitting}
            size="large"
            block
          >
            {isSubmitting
              ? '提交中...'
              : !hasAvailableProfiles
                ? '激活额度不足请等待管理员更新额度'
                : !hasQuota
                  ? '次数不足'
                  : '立即激活'
            }
          </Button>
        </Form.Item>
      </Form>


    </Card>
  );
};

export default EmailSubmitForm;
